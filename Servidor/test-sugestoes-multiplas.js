const axios = require('axios');

async function testarSugestoesMultiplas() {
  try {
    console.log('Testando geração de múltiplas sugestões regulares...\n');
    
    const response = await axios.post('http://localhost:3030/api/whatsapp/sugestao-resposta', {
      telefone: '551199999999',
      mensagens: [
        {
          texto: 'Ol<PERSON>, gostaria de saber mais sobre o sistema de cardápio digital',
          remetente: 'Lead',
          horario: '10:30:00',
          tipo: 'text'
        },
        {
          texto: 'Claro! O Meu Cardápio AI é perfeito para restaurantes. Como funciona seu processo de pedidos hoje?',
          remetente: 'Eu',
          horario: '10:31:00',
          tipo: 'text'
        },
        {
          texto: 'Usamos WhatsApp e anotamos manualmente os pedidos',
          remetente: 'Lead',
          horario: '10:32:00',
          tipo: 'text'
        }
      ],
      faseSpin: 'situacao',
      produto: '<PERSON><PERSON> Cardápio AI',
      tomConversa: 'informal'
    });
    
    console.log('Status:', response.status);
    console.log('Resposta completa:', JSON.stringify(response.data, null, 2));
    
    if (response.data.sucesso && response.data.data) {
      const data = response.data.data;
      
      if (data.sugestoes && Array.isArray(data.sugestoes)) {
        console.log(`\n✅ Sucesso! Foram geradas ${data.sugestoes.length} sugestões:\n`);
        
        data.sugestoes.forEach((sug, index) => {
          console.log(`--- Sugestão ${index + 1} (Confiança: ${(sug.confianca * 100).toFixed(0)}%) ---`);
          console.log(sug.texto);
          console.log('');
        });
        
        if (data.observacoes) {
          console.log('Observações:', data.observacoes);
        }
      } else {
        console.log('\n❌ Erro: Resposta não contém array de sugestões');
        console.log('Estrutura recebida:', data);
      }
    }
  } catch (error) {
    console.error('Erro ao testar:', error.response?.data || error.message);
  }
}

// Executar teste
testarSugestoesMultiplas();