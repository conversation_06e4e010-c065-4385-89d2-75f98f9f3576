import {Component, OnInit, ViewChild} from '@angular/core';
import {FormaDePagamento} from "../../pedidos/objetos/FormaDePagamento";
import {DialogRef, DialogService} from "@progress/kendo-angular-dialog";
import {NgForm} from "@angular/forms";
import {FormasDePagamentoService} from "../../services/formas-de-pagamento.service";
import {NumerosWhatsappService} from "../../services/numeros-whatsapp.service";
import {NumeroWhatsapp} from "../../pedidos/objetos/NumeroWhatsapp";

@Component({
  selector: 'app-novo-numero-whatsapp',
  templateUrl: './novo-numero-whatsapp.component.html',
  styleUrls: ['./novo-numero-whatsapp.component.scss']
})
export class NovoNumeroWhatsappComponent implements OnInit {
  numeroWhatsapp: NumeroWhatsapp;
  windowRef: DialogRef;
  empresa: any;
  mensagemErro = '';
  telefoneFormatado: string = '';
  @ViewChild('frm',  {static: false} ) frm: NgForm;
  enviando = false;

  constructor(private dialogService: DialogService, private numerosWhatsappService: NumerosWhatsappService) {
    this.numeroWhatsapp = new NumeroWhatsapp();
  }

  ngOnInit(): void {
    // Se estiver editando, formatar o telefone existente
    if (this.numeroWhatsapp.whatsapp) {
      this.telefoneFormatado = this.formatarTelefone(this.numeroWhatsapp.whatsapp);
    }
  }

  onSalvar() {
    delete this.mensagemErro;
    if(!this.frm.valid){
      return;
    }

    // Garantir que o telefone está limpo antes de salvar
    this.numeroWhatsapp.whatsapp = this.limparTelefone(this.telefoneFormatado);

    this.enviando = true;
    this.numerosWhatsappService.salveNumeroWhatsapp(this.numeroWhatsapp, this.empresa).then( (resposta) => {
      this.windowRef.close();
      this.enviando = false;
    }).catch( erro => {
      this.mensagemErro = erro;
      this.enviando = false;
    });
  }

  remova() {

  }

  fechar() {
    this.windowRef.close();
  }

  // Métodos de validação e formatação de telefone
  onTelefoneChange(value: string) {
    this.telefoneFormatado = value;
    // Remove formatação para salvar apenas números
    this.numeroWhatsapp.whatsapp = this.limparTelefone(value);
  }

  onTelefoneBlur() {
    // Validação adicional se necessário
    if (this.telefoneFormatado) {
      this.numeroWhatsapp.whatsapp = this.limparTelefone(this.telefoneFormatado);
    }
  }

  limparTelefone(telefone: string): string {
    if (!telefone) return '';
    // Remove todos os caracteres não numéricos
    return telefone.replace(/\D/g, '');
  }

  formatarTelefone(telefone: string): string {
    if (!telefone) return '';

    // Remove caracteres não numéricos
    const numeroLimpo = telefone.replace(/\D/g, '');

    // Aplica formatação (XX) XXXXX-XXXX
    if (numeroLimpo.length === 11) {
      return `(${numeroLimpo.substring(0, 2)}) ${numeroLimpo.substring(2, 7)}-${numeroLimpo.substring(7)}`;
    } else if (numeroLimpo.length === 10) {
      return `(${numeroLimpo.substring(0, 2)}) ${numeroLimpo.substring(2, 6)}-${numeroLimpo.substring(6)}`;
    }

    return telefone;
  }

  validarTelefone(telefone: string): boolean {
    if (!telefone) return false;
    const telefoneLimpo = this.limparTelefone(telefone);
    // Validação para telefone brasileiro: 11 dígitos (celular com 9)
    return /^[1-9]{2}9[6-9]{1}[0-9]{7}$/.test(telefoneLimpo);
  }
}
