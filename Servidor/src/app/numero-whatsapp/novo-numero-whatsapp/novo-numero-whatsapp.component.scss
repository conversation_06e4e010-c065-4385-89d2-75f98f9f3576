// Label moderno
.label-modern {
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 0.75rem;
  display: flex;
  align-items: center;
  font-size: 0.95rem;
}

// Input group moderno
.input-group-modern {
  position: relative;
  display: flex;
  align-items: center;

  .input-icon {
    position: absolute;
    left: 12px;
    z-index: 10;
    color: #25d366;
    font-size: 1.1rem;
    pointer-events: none;
  }

  .form-control-modern {
    padding-left: 40px !important;
    padding-right: 12px !important;
    padding-top: 12px !important;
    padding-bottom: 12px !important;
    border-radius: 8px !important;
    border: 2px solid #e9ecef !important;
    transition: all 0.2s ease !important;
    font-size: 1rem !important;
    background: #ffffff !important;

    &:focus {
      border-color: #007bff !important;
      box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.15) !important;
      outline: none !important;
    }

    &.ng-invalid.ng-touched {
      border-color: #dc3545 !important;
      box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.15) !important;
    }

    &.ng-valid.ng-touched {
      border-color: #28a745 !important;
      box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.15) !important;
    }

    &::placeholder {
      color: #6c757d !important;
      opacity: 0.7 !important;
    }
  }
}

// Feedback de erro moderno
.invalid-feedback-modern {
  display: block;
  margin-top: 0.5rem;

  .error-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #dc3545;
    font-size: 0.875rem;
    margin-bottom: 0.25rem;
    padding: 0.5rem;
    background: #f8d7da;
    border-radius: 6px;
    border-left: 3px solid #dc3545;

    &:last-child {
      margin-bottom: 0;
    }

    i {
      font-size: 0.8rem;
      flex-shrink: 0;
    }
  }
}

// Checkbox moderno
.checkbox-group-modern {
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;

  .checkbox-modern {
    margin-right: 0.75rem;
  }

  .checkbox-label-modern {
    margin: 0;
    cursor: pointer;
    font-weight: 500;
    color: #495057;
    display: flex;
    align-items: flex-start;
    gap: 0.5rem;

    small {
      font-weight: 400;
      font-size: 0.8rem;
      line-height: 1.3;
    }
  }
}

// Footer moderno
.modal-footer-modern {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
  padding: 1.5rem 0 0 0;
  border-top: 1px solid #e9ecef;
  margin-top: 1.5rem;
}

// Botões modernos
.btn-modern {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 500;
  font-size: 0.9rem;
  transition: all 0.2s ease;
  border: none;
  cursor: pointer;
  min-width: 120px;

  &.btn-primary {
    background: #007bff;
    color: white;

    &:hover:not(:disabled) {
      background: #0056b3;
      transform: translateY(-1px);
      box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);
    }

    &:disabled {
      background: #6c757d;
      cursor: not-allowed;
      transform: none;
      box-shadow: none;
    }
  }

  &.btn-secondary {
    background: #6c757d;
    color: white;

    &:hover {
      background: #545b62;
      transform: translateY(-1px);
      box-shadow: 0 4px 8px rgba(108, 117, 125, 0.3);
    }
  }

  i {
    font-size: 0.85rem;
  }
}

// Texto de ajuda
.form-text {
  margin-top: 0.5rem;
  font-size: 0.875rem;

  i {
    color: #007bff;
  }
}

// Animações
@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}

.ng-invalid.ng-touched .form-control-modern {
  animation: shake 0.5s ease-in-out;
}

// Responsividade
@media (max-width: 480px) {
  .modal-footer-modern {
    flex-direction: column;

    .btn-modern {
      width: 100%;
    }
  }

  .input-group-modern .form-control-modern {
    font-size: 16px !important; // Evita zoom no iOS
  }
}