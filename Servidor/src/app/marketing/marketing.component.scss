.badge {
  font-size: 13px;
  padding: 5px 10px;
}

.acoes .btn{
  padding-left: 10px;padding-right: 10px;
}

// Header de informações do WhatsApp
.whatsapp-info-header {
  margin-bottom: 1.5rem;
}

.whatsapp-header-card {
  background: linear-gradient(135deg, #f8fff9 0%, #e8f5e8 100%);
  border-radius: 12px;
  border: 1px solid #c8e6c9;
  box-shadow: 0 4px 12px rgba(37, 211, 102, 0.1);
  overflow: hidden;
}

.whatsapp-header-content {
  display: flex;
  align-items: center;
  padding: 1.25rem 1.5rem;
  gap: 1.25rem;
}

.whatsapp-header-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  background: #25d366;
  border-radius: 50%;
  color: white;
  font-size: 1.75rem;
  flex-shrink: 0;
  box-shadow: 0 4px 8px rgba(37, 211, 102, 0.3);
}

.whatsapp-header-details {
  flex: 1;

  .whatsapp-header-title {
    display: block;
    font-size: 0.9rem;
    font-weight: 500;
    color: #6c757d;
    margin-bottom: 0.25rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  .whatsapp-header-info {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex-wrap: wrap;
  }

  .whatsapp-header-number {
    font-size: 1.5rem;
    font-weight: 700;
    color: #2c3e50;
    letter-spacing: 0.5px;
  }

  .whatsapp-header-type {
    display: inline-block;
    padding: 0.375rem 1rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    cursor: help;
    transition: all 0.2s ease;

    &.type-principal {
      background: #e3f2fd;
      color: #1976d2;
      border: 1px solid #bbdefb;
    }

    &.type-campanhas {
      background: #e8f5e8;
      color: #2e7d32;
      border: 1px solid #c8e6c9;
    }

    &.type-both {
      background: #f3e5f5;
      color: #7b1fa2;
      border: 1px solid #e1bee7;
    }

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
    }
  }
}

.whatsapp-header-status {
  color: #28a745;
  font-size: 1.5rem;
  flex-shrink: 0;

  i {
    cursor: help;
    filter: drop-shadow(0 2px 4px rgba(40, 167, 69, 0.3));
  }
}

// Responsividade
@media (max-width: 768px) {
  .whatsapp-header-content {
    padding: 1rem;
    gap: 1rem;
    flex-direction: column;
    text-align: center;
  }

  .whatsapp-header-icon {
    width: 50px;
    height: 50px;
    font-size: 1.5rem;
  }

  .whatsapp-header-details {
    .whatsapp-header-number {
      font-size: 1.25rem;
    }

    .whatsapp-header-info {
      flex-direction: column;
      gap: 0.75rem;
      align-items: center;
    }

    .whatsapp-header-type {
      font-size: 0.75rem;
      padding: 0.3rem 0.8rem;
    }
  }

  .whatsapp-header-status {
    font-size: 1.25rem;
  }
}

@media (max-width: 480px) {
  .whatsapp-info-header {
    margin-bottom: 1rem;
  }

  .whatsapp-header-content {
    padding: 0.75rem;
  }

  .whatsapp-header-details {
    .whatsapp-header-title {
      font-size: 0.8rem;
    }

    .whatsapp-header-number {
      font-size: 1.1rem;
    }
  }
}
