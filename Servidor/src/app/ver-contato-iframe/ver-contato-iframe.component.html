<h4 class="page-title">
  <button class="btn btn-outline-blue btn-rounded" (click)="voltar()" *ngIf="!widget" style="margin-right: 5px;">
    <i class="fa fa-arrow-left ct-point" ></i>
  </button>
  <i class="fe-user "></i> Contato
</h4>

<app-notificacoes-promokit></app-notificacoes-promokit>

<div style="position: absolute;top: 15px;right: 15px;">
  <button class="btn btn-secondary btn-sm mr-1" (click)="exibirTodosLogs();" kendoTooltip title="Ver TODOS os logs do sistema (debug)">
    <i class="fas fa-stethoscope"></i>
  </button>
  <button class="btn btn-blue" (click)="recarregue();" kendoTooltip title="Recarregar o cardápio e forçar envio das mensagens."><i class="fas fa-sync-alt"></i></button>
</div>

<app-gerenciador-envio-campanha></app-gerenciador-envio-campanha>

<!-- Seção para exibir relatórios e diagnósticos diretamente na tela -->
<div class="card shadow-sm mt-3" *ngIf="exibirRelatorioNaTela">
  <div class="card-header bg-info text-white d-flex justify-content-between align-items-center py-2">
    <h6 class="mb-0 font-weight-bold">
      <i class="fas fa-terminal mr-2"></i>
      {{tituloRelatorio}}
    </h6>
    <button class="btn btn-sm btn-outline-light rounded-circle p-1" (click)="fecharRelatorio()" kendoTooltip title="Fechar relatório" style="width: 28px; height: 28px;">
      <i class="fas fa-times"></i>
    </button>
  </div>

  <!-- Mostrar ListView quando há logs -->
  <div class="card-body p-0" *ngIf="logsListView.length > 0">
    <!-- Barra de estatísticas -->
    <div class="p-3 border-bottom" style="background: #ffffff;">
      <div class="row align-items-center">
        <div class="col-md-6">
          <div class="d-flex align-items-center">
            <span class="mr-3"><strong>Total:</strong> {{totalLogs}} logs</span>
            <span class="estatistica-badge sucesso" *ngIf="estatisticas.sucessos > 0">
              ✅ {{estatisticas.sucessos}}
            </span>
            <span class="estatistica-badge erro" *ngIf="estatisticas.erros > 0">
              ❌ {{estatisticas.erros}}
            </span>
            <span class="estatistica-badge aviso" *ngIf="estatisticas.avisos > 0">
              ⚠️ {{estatisticas.avisos}}
            </span>
            <span class="estatistica-badge processamento" *ngIf="estatisticas.processamentos > 0">
              🔄 {{estatisticas.processamentos}}
            </span>
          </div>
        </div>
        <div class="col-md-6">
          <div class="d-flex justify-content-end">
            <button class="btn btn-sm btn-outline-primary" (click)="limparFiltros()" *ngIf="filtroTipo !== 'TODOS' || filtroCategoria !== 'TODAS' || textoBusca">
              <i class="fas fa-times mr-1"></i> Limpar Filtros
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Barra de filtros -->
    <div class="p-3 border-bottom" style="background: #fafbfc;">
      <div class="row">
        <div class="col-md-4">
          <label class="small text-muted mb-1">Buscar:</label>
          <input type="text" class="form-control form-control-sm" placeholder="Buscar nos logs..."
                 [(ngModel)]="textoBusca" (ngModelChange)="filtrarLogs()">
        </div>
        <div class="col-md-4">
          <label class="small text-muted mb-1">Tipo:</label>
          <select class="form-control form-control-sm" [(ngModel)]="filtroTipo" (ngModelChange)="filtrarLogs()">
            <option value="TODOS">Todos</option>
            <option value="SUCCESS">✅ Sucesso</option>
            <option value="ERROR">❌ Erro</option>
            <option value="WARNING">⚠️ Aviso</option>
            <option value="INFO">🔵 Info</option>
          </select>
        </div>
        <div class="col-md-4">
          <label class="small text-muted mb-1">Categoria:</label>
          <select class="form-control form-control-sm" [(ngModel)]="filtroCategoria" (ngModelChange)="filtrarLogs()">
            <option value="TODAS">Todas</option>
            <option value="PROCESSAMENTO">⚙️ Processamento</option>
            <option value="VALIDACAO">🔍 Validação</option>
            <option value="ENVIO">📤 Envio</option>
            <option value="SISTEMA">🖥️ Sistema</option>
          </select>
        </div>
      </div>
    </div>

    <!-- ListView -->
    <div class="relatorio-listview" style="max-height: 500px; overflow-y: auto; background: #fafbfc;">
      <!-- Mostrar ListView quando há logs -->
      <kendo-listview [data]="logsListViewFiltrados" [height]="500" *ngIf="logsListViewFiltrados.length > 0">
        <ng-template kendoListViewItemTemplate let-dataItem>
          <div class="log-item-card" [ngClass]="dataItem.cssClass" (click)="toggleDetalhes(dataItem)">
            <!-- Texto da mensagem no topo com destaque -->
            <div class="log-texto-mensagem-destaque mb-3">
              <div class="d-flex justify-content-between align-items-start">
                <div class="texto-mensagem-conteudo-principal flex-grow-1">
                  <span class="texto-icone">📝</span>
                  <span *ngIf="dataItem.textoMensagem" class="texto-conteudo">"{{dataItem.textoMensagem}}"</span>
                  <span *ngIf="!dataItem.textoMensagem" class="texto-conteudo text-muted font-italic">
                    <span *ngIf="dataItem.categoria === 'PROCESSAMENTO'">(processando...)</span>
                    <span *ngIf="dataItem.categoria !== 'PROCESSAMENTO'">(sem texto de mensagem)</span>
                  </span>
                </div>
                <div class="log-time text-muted text-nowrap ml-2">
                  <small>{{dataItem.timestamp | date:'HH:mm:ss.SSS'}} ({{dataItem.tempoRelativo}})</small>
                </div>
              </div>
              <div class="mt-1" *ngIf="dataItem.tipoMensagem">
                <small class="text-muted">
                  <i class="fas fa-tag mr-1"></i>
                  Tipo: <strong>{{dataItem.tipoMensagem}}</strong>
                </small>
              </div>
            </div>

            <div class="log-item-header">
              <div class="d-flex justify-content-between align-items-start">
                <div class="log-item-info">
                  <span class="log-icon mr-2">{{dataItem.icone}}</span>
                  <span class="log-categoria mr-2">{{dataItem.iconeCategoria}} {{dataItem.categoria}}</span>
                </div>
                <div class="log-item-pessoa text-right">
                  <span class="log-nome">👤 {{dataItem.nome}}</span>
                  <span class="log-telefone text-muted ml-2" *ngIf="dataItem.telefone">📱 {{dataItem.telefone}}</span>
                </div>
              </div>
            </div>

            <div class="log-item-body mt-2">
              <div class="log-mensagem">
                💬 {{dataItem.mensagem}}
              </div>

              <div class="log-detalhes mt-2" *ngIf="dataItem.detalhesFormatados && dataItem.expanded">
                <pre class="mb-0">{{dataItem.detalhesFormatados}}</pre>
              </div>

              <div class="text-center mt-2" *ngIf="dataItem.detalhesFormatados">
                <button class="btn btn-sm btn-link p-0">
                  <i class="fas" [ngClass]="dataItem.expanded ? 'fa-chevron-up' : 'fa-chevron-down'"></i>
                  {{dataItem.expanded ? 'Menos detalhes' : 'Mais detalhes'}}
                </button>
              </div>
            </div>
          </div>
        </ng-template>
      </kendo-listview>

      <!-- Mensagem quando não há logs após filtro - DENTRO da div relatorio-listview -->
      <div class="empty-state-container" *ngIf="logsListViewFiltrados.length === 0 && logsListView.length > 0">
        <div class="empty-state-content">
          <i class="fas fa-search fa-3x mb-3"></i>
          <h5>Nenhum log encontrado</h5>
          <p class="text-muted">Os filtros aplicados não retornaram resultados.</p>
          <button class="btn btn-sm btn-primary" (click)="limparFiltros()">
            <i class="fas fa-times mr-1"></i> Limpar Filtros
          </button>
        </div>
      </div>

      <!-- Empty state quando não há logs no sistema -->
      <div class="empty-state-container" *ngIf="logsListView.length === 0 && !conteudoRelatorioFormatado">
        <div class="empty-state-content">
          <i class="fas fa-inbox fa-3x mb-3"></i>
          <h5>Nenhum log disponível</h5>
          <p class="text-muted">Ainda não há logs para exibir.</p>
        </div>
      </div>
    </div>
  </div>

  <!-- Mostrar conteúdo HTML quando não há logs (fallback) -->
  <div class="card-body p-0" *ngIf="logsListView.length === 0 && conteudoRelatorioFormatado">
    <div class="relatorio-container" style="background: #1e1e1e; color: #f8f9fa; padding: 15px; max-height: 450px; overflow-y: auto; border-left: 4px solid #17a2b8;">
      <div class="log-content" [innerHTML]="conteudoRelatorioFormatado"></div>
    </div>
  </div>

  <div class="card-footer border-top" style="background: #ffffff !important; border-color: #e9ecef !important;">
    <div class="d-flex justify-content-between align-items-center">
      <small class="text-muted d-flex align-items-center">
        <i class="fas fa-clock mr-1" style="color: #6c757d;"></i>
        Gerado em {{dataGeracaoRelatorio}}
      </small>
      <div>
        <button class="btn btn-sm btn-outline-secondary mr-2" (click)="copiarRelatorio()" kendoTooltip title="Copiar para área de transferência">
          <i class="fas fa-copy mr-1"></i> Copiar
        </button>
        <button class="btn btn-sm btn-outline-warning mr-2" (click)="limparLogs()" kendoTooltip title="Limpar todos os logs">
          <i class="fas fa-trash mr-1"></i> Limpar Log
        </button>
        <button class="btn btn-sm btn-outline-danger" (click)="fecharRelatorio()">
          <i class="fas fa-times mr-1"></i> Fechar
        </button>
      </div>
    </div>
  </div>
</div>

<div class="mb-2" *ngIf="dev">
  <div class="form-group mt-2">
    <label>Enviar mensagem Teste Chatbot:</label>
    <input type="text" class="form-control" (keyup.enter)="digitouMensagemBot($event)"/>
  </div>
</div>

<div class="k-i-loading ml-1 mr-1 mt-3" style="font-size: 50px; padding-bottom: 20px" *ngIf="!carregou" ></div>

<div class="row"  [hidden]="contato == null">
  <div class="col-12">
    <info-contato [contato]="contato" [exibirUltimaVisita]="true" [atendente]="atendente" [statusMia]="statusMia"
                  [desativarParaSempre]="desativarParaSempre"
                  [inserirtTag]="true" [empresa]="empresa" [exibirEndereco]="true" [limiteEndereco]="2">
    </info-contato>
  </div>

  <div class="col-12 mt-2">
    <div class="alert alert-success mb-0" role="alert" *ngIf="msg">
      <i class="mdi mdi-check-all mr-2"></i> {{msg}}  <i class="fa fa-print fa-lg cpointer ml-1" *ngIf="novoPedido.guid"
                                                         (click)="imprimaPedido(this.novoPedido)"> </i>
    </div>
  </div>

  <div class="col-12 mb-3" *ngIf="contato  && (contato.telefone || contato.id)">
    <acoes-modulos [contato]="contato" (salvouContato)="salveContato($event)"></acoes-modulos>
  </div>

  <div class="col-12">
    <info-pedidos [contato]="contato"  [empresa]="empresa"></info-pedidos>
  </div>

  <div class="col-12 ">
    <info-fidelidade *ngIf="contato" [cartoes]="contato.cartoes" [empresa]="empresa"></info-fidelidade>
  </div>

  <div class="col-12">
    <app-historico-modulos [contato]="contato" [empresa]="empresa" ></app-historico-modulos>
  </div>
</div>

<div  [hidden]="!carregou || contato != null">
  <div class="row">
    <div class="col">
      <ng-container *ngIf="!empresa.dark">
        <h5 class="mt-2">Este é um novo cliente</h5>
      </ng-container>

      <info-contato [contato]="contatoPorDados" [atendente]="atendente" [statusMia]="statusMia"
                    [exibirUltimaVisita]="true" [empresa]="empresa" [desativarParaSempre]="desativarParaSempre"></info-contato>
      <acoes-modulos [contato]="contatoPorDados" [novoContato]="true" (salvouContato)="salveContato($event)"></acoes-modulos>
    </div>
  </div>
</div>
