.acoes ::ng-deep .icone  {
  display: inline-block;
  fill: #fff;
  vertical-align: middle;
  svg {
    width: 24px !important;
    height: 24px !important;
  }
}


.card.contato {
  >.card-body{
    padding-right: 0;
  }
  .ultima-visita{
    top: 30px;position: relative;
  }
}

.plano-info{
  border-left: 1px solid #e5e5e5;
  margin-top: -25px;
  margin-bottom: -25px;
  padding-left: 0;

  .card-box{
    padding-top: 0;
    padding-bottom: 0;
    box-shadow: none;

    &.plano{
      border-bottom: 1px solid #e5e5e5;
      margin-top: 21px;
      padding-bottom: 21px;
      border-radius: 0;
      padding-left: 20px;
      display: table;
      width: 100%;
    }

    p-dropdown{
      display: inline-block;
      position: relative;
      top: 10px;
      margin-left: 15px;
    }
  }

  .pontos{

    span {
      font-size: 40px;
      font-weight: 500;
      line-height: 0.5em;
      position: relative;
      top: 3px;
    }
  }

  label{
    margin-bottom: 0;
    line-height: 18px;
    font-size: 16px;
  }

  .por-selo{
    .lista-selos{
      position: relative;   top: -10px;     display: block;
    }
    label{
      line-height: 30px;
      width: 56px;
    }
  }
}

.acoes .btn{
  margin-right:15px
}

.fa-whatsapp{
  font-size: 25px;
}

@media (max-width: 768px){
  .acoes ::ng-deep .icone.tam1,.icone {
    width: 100% !important;
    height: 25px !important;
    svg {
      width: 25px !important;
      height: 25px !important;
    }
  }

  .card.contato {
    >.card-body{
      padding: 1rem !important;
    }

    margin-bottom: 0px;
  }
  .plano-info{
    border: none;
    padding-top:0;
    margin-top: 0;
    margin-bottom:0;

    .card-box{
      &.plano{
        padding-bottom: 30px;
      }

      &.por-selo{
        padding-top: 10px;
      }
      p-dropdown{
        margin-left:0;
      }
    }

  }

  .acoes .btn{
    padding: 10px;
    float: left;
    width: 100%;
    height: 100%;
    margin-top: 5px;
    margin-right: 0px;
  }

  .card-box {
    padding: 1rem;
    .lista-selos{
      width: 100%;
      display: table;
      padding-top: 10px;
    }
  }
}


td a{
  color: inherit;
}

.tamanho-botao {
  width: 135px;
}

.botao-fundo {
  border: none;
  &.verde {
    background: url(/assets/fidelidade/Banner_01.png)
  }
  &.azul {
    background: url(/assets/fidelidade/Banner_02.png)
  }

  &.roxo {
    background: url(/assets/fidelidade/Banner_03.png)
  }

  &.laranja {
    background: url(/assets/fidelidade/Banner_04.png)
  }
}

// Estilos para o relatório de diagnóstico
.relatorio-container {
  position: relative;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace !important;

  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: #2d2d2d;
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: #17a2b8;
    border-radius: 4px;

    &:hover {
      background: #138496;
    }
  }

  .log-content {
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace !important;
    line-height: 1.6 !important;
    font-size: 12px;
    color: #f8f9fa;
  }
}

// Estilos para logs formatados
.log-formatted {
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace !important;
  
  // Classes base para diferentes tipos de logs
  .log-success {
    background: rgba(40, 167, 69, 0.15);
    border-left: 4px solid #28a745;
    padding: 10px 14px;
    margin: 6px 0;
    border-radius: 0 4px 4px 0;
    color: #90ee90;
    position: relative;
    transition: all 0.2s ease;
    
    &:hover {
      background: rgba(40, 167, 69, 0.25);
      border-left-width: 5px;
      padding-left: 13px;
    }
  }

  .log-error {
    background: rgba(220, 53, 69, 0.15);
    border-left: 4px solid #dc3545;
    padding: 10px 14px;
    margin: 6px 0;
    border-radius: 0 4px 4px 0;
    color: #ffb3b3;
    position: relative;
    transition: all 0.2s ease;
    
    &:hover {
      background: rgba(220, 53, 69, 0.25);
      border-left-width: 5px;
      padding-left: 13px;
    }
  }

  .log-warning {
    background: rgba(255, 193, 7, 0.15);
    border-left: 4px solid #ffc107;
    padding: 10px 14px;
    margin: 6px 0;
    border-radius: 0 4px 4px 0;
    color: #fff3cd;
    position: relative;
    transition: all 0.2s ease;
    
    &:hover {
      background: rgba(255, 193, 7, 0.25);
      border-left-width: 5px;
      padding-left: 13px;
    }
  }

  .log-processing {
    background: rgba(23, 162, 184, 0.15);
    border-left: 4px solid #17a2b8;
    padding: 10px 14px;
    margin: 6px 0;
    border-radius: 0 4px 4px 0;
    color: #b3e5fc;
    position: relative;
    transition: all 0.2s ease;
    
    &:hover {
      background: rgba(23, 162, 184, 0.25);
      border-left-width: 5px;
      padding-left: 13px;
    }
  }

  .log-info {
    background: rgba(108, 117, 125, 0.15);
    border-left: 4px solid #6c757d;
    padding: 10px 14px;
    margin: 6px 0;
    border-radius: 0 4px 4px 0;
    color: #e2e3e5;
    position: relative;
    transition: all 0.2s ease;
    
    &:hover {
      background: rgba(108, 117, 125, 0.25);
      border-left-width: 5px;
      padding-left: 13px;
    }
  }

  .log-header {
    color: #ffc107;
    font-weight: bold;
    font-size: 14px;
    margin: 12px 0 8px 0;
    text-shadow: 0 0 2px rgba(255, 193, 7, 0.5);
  }

  .log-title {
    color: #17a2b8;
    font-weight: bold;
    font-size: 16px;
    margin: 16px 0 12px 0;
    text-shadow: 0 0 2px rgba(23, 162, 184, 0.5);
  }

  .log-separator {
    color: #6c757d;
    margin: 8px 0;
    opacity: 0.7;
  }

  .log-divider {
    color: #495057;
    margin: 6px 0;
    opacity: 0.5;
  }

  .log-subdivider {
    color: #343a40;
    margin: 4px 0;
    opacity: 0.4;
  }

  .log-time {
    color: #b3e5fc;
    font-weight: 500;
  }

  .log-user {
    color: #ffb3ff;
    font-weight: 500;
  }

  .log-message {
    color: #e6ffe6;
    font-weight: 400;
    margin-left: 4px;
  }

  .log-text-message {
    color: #cce7ff;
    font-weight: 500;
    background: rgba(23, 162, 184, 0.2);
    padding: 6px 12px;
    border-radius: 4px;
    margin: 8px 0;
    display: block;
    border-left: 3px solid #17a2b8;
    font-style: italic;
    word-wrap: break-word;
    overflow-wrap: break-word;
    white-space: pre-wrap;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    
    &:hover {
      background: rgba(23, 162, 184, 0.3);
      border-left-width: 4px;
      transition: all 0.2s ease;
    }
  }

  .log-phone {
    color: #ffffb3;
    font-weight: 500;
  }

  .log-duration {
    color: #ffcc99;
    font-weight: 500;
  }
}

// Estilos para o ListView de logs
.relatorio-listview {
  background: #fafbfc;
  min-height: 400px;
  position: relative;
  
  .log-item-card {
    background: white;
    border: 1px solid #e9ecef;
    margin: 8px;
    padding: 16px;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.15s ease;
    
    &:hover {
      border-color: #dee2e6;
      transform: translateY(-1px);
      box-shadow: 0 2px 4px rgba(0,0,0,0.04);
    }
    
    &.log-success {
      border-left: 3px solid #28a745;
    }
    
    &.log-error {
      border-left: 3px solid #dc3545;
    }
    
    &.log-warning {
      border-left: 3px solid #ffc107;
    }
    
    &.log-processing {
      border-left: 3px solid #17a2b8;
    }
    
    &.log-info {
      border-left: 3px solid #6c757d;
    }
  }
  
  .log-item-header {
    font-size: 13px;
    
    .log-icon {
      font-size: 16px;
      opacity: 0.8;
    }
    
    .log-categoria {
      font-weight: 500;
      color: #6c757d;
      font-size: 12px;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }
    
    .log-time {
      font-size: 11px;
      color: #adb5bd;
    }
    
    .log-nome {
      font-weight: 600;
      color: #212529;
      font-size: 13px;
    }
    
    .log-telefone {
      font-size: 12px;
      color: #6c757d;
    }
  }
  
  .log-item-body {
    .log-mensagem {
      font-size: 14px;
      color: #212529;
      line-height: 1.5;
    }
    
    .log-texto-mensagem {
      background: #e3f2fd;
      border-radius: 6px;
      padding: 12px;
      border-left: 3px solid #2196f3;
      
      .texto-mensagem-label {
        font-size: 12px;
        color: #1976d2;
        font-weight: 500;
        margin-bottom: 4px;
      }
      
      .texto-mensagem-conteudo {
        font-size: 14px;
        color: #0d47a1;
        font-style: italic;
        word-wrap: break-word;
        white-space: pre-wrap;
        
        .text-muted {
          color: #6c757d !important;
          opacity: 0.8;
          font-size: 13px;
        }
      }
    }
    
    .log-detalhes {
      pre {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 4px;
        padding: 8px;
        font-size: 12px;
        color: #495057;
        white-space: pre-wrap;
        word-wrap: break-word;
      }
    }
  }
  
  // Customização do Kendo ListView
  .k-listview {
    border: none;
    background: transparent;
    
    .k-listview-content {
      padding: 0;
    }
  }
}

// Responsividade para o ListView
@media (max-width: 768px) {
  .relatorio-listview {
    .log-item-card {
      margin: 4px;
      padding: 12px;
      
      .log-item-header {
        font-size: 12px;
        
        .log-icon {
          font-size: 16px;
        }
        
        .d-flex {
          flex-direction: column;
          align-items: flex-start !important;
          
          .log-item-pessoa {
            text-align: left !important;
            margin-top: 4px;
          }
        }
      }
      
      .log-item-body {
        .log-mensagem {
          font-size: 13px;
        }
        
        .log-texto-mensagem {
          padding: 8px;
          
          .texto-mensagem-conteudo {
            font-size: 13px;
          }
        }
      }
    }
  }
  
  // Filtros responsivos
  .card-body {
    .row {
      .col-md-4 {
        margin-bottom: 8px;
      }
    }
  }
}

// Animação suave para aparecer o relatório
.card.shadow-sm {
  animation: slideDown 0.3s ease-out;
  border: 1px solid #e9ecef;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.04) !important;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Melhorar os botões
.btn-outline-secondary {
  border-color: #dee2e6;
  color: #6c757d;
  font-size: 13px;
  font-weight: 500;

  &:hover {
    background-color: #f8f9fa;
    border-color: #dee2e6;
    color: #495057;
  }
}

.btn-outline-danger {
  border-color: #f8d7da;
  color: #dc3545;
  font-size: 13px;
  font-weight: 500;

  &:hover {
    background-color: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
  }
}

.btn-primary {
  background-color: #007bff;
  border-color: #007bff;
  font-size: 13px;
  font-weight: 500;
  
  &:hover {
    background-color: #0056b3;
    border-color: #0056b3;
  }
}

// Responsividade para o relatório
@media (max-width: 768px) {
  .relatorio-container {
    max-height: 300px !important;
    font-size: 10px !important;
    padding: 10px !important;

    pre {
      font-size: 10px !important;
      line-height: 1.3 !important;
    }
  }

  .card-footer {
    .d-flex {
      flex-direction: column;
      align-items: flex-start !important;

      small {
        margin-bottom: 10px;
      }

      div {
        width: 100%;
        text-align: center;

        .btn {
          width: 45%;
          margin: 0 2.5%;
        }
      }
    }
  }
}

// Melhorar o header do card
.card-header.bg-info {
  background: #0056b3 !important;
  border-bottom: none;
  border-radius: 0;

  h6 {
    font-size: 14px;
    letter-spacing: 0.5px;
    color: #ffffff !important;
    opacity: 1;
  }

  .btn-outline-light {
    border: 1px solid rgba(255, 255, 255, 0.3);
    transition: all 0.2s ease;
    color: #ffffff !important;
    
    &:hover {
      background-color: rgba(255, 255, 255, 0.2);
      border-color: rgba(255, 255, 255, 0.5);
      transform: scale(1.05);
    }
  }
  
  // Garantir que todos os textos e ícones sejam brancos
  * {
    color: #ffffff !important;
  }
  
  i {
    color: #ffffff !important;
    opacity: 0.9;
  }
}

// Destacar texto da mensagem no topo do card
.log-texto-mensagem-destaque {
  background: #f8f9fa;
  border-radius: 4px;
  padding: 12px;
  border-left: 3px solid #007bff;
  margin-bottom: 12px;
  
  .texto-mensagem-conteudo-principal {
    display: flex;
    align-items: flex-start;
    gap: 8px;
    
    .texto-icone {
      font-size: 16px;
      color: #6c757d;
      opacity: 0.7;
      flex-shrink: 0;
    }
    
    .texto-conteudo {
      font-size: 14px;
      color: #212529;
      line-height: 1.5;
      word-wrap: break-word;
      white-space: pre-wrap;
      
      &.text-muted {
        color: #6c757d !important;
        font-style: italic;
        font-weight: normal;
      }
    }
  }
  
  .log-time {
    font-size: 12px;
    color: #6c757d;
    
    small {
      font-weight: 500;
    }
  }
}

// Statistics badges with colored backgrounds
.estatistica-badge {
  display: inline-flex;
  align-items: center;
  padding: 5px 10px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 600;
  margin-right: 6px;
  border: none;
  
  &.sucesso {
    background-color: #d4edda;
    color: #155724;
  }
  
  &.erro {
    background-color: #f8d7da;
    color: #721c24;
  }
  
  &.aviso {
    background-color: #fff3cd;
    color: #856404;
  }
  
  &.processamento {
    background-color: #d1ecf1;
    color: #0c5460;
  }
}

// Filtros modernos
.form-control-sm {
  border: 1px solid #e9ecef;
  border-radius: 4px;
  font-size: 13px;
  
  &:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.1rem rgba(0,123,255,.15);
  }
}

.small.text-muted {
  font-size: 11px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-weight: 600;
  color: #6c757d !important;
}

// Empty state styling
.relatorio-listview .empty-state-container {
  min-height: 400px;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fafbfc;
  
  .empty-state-content {
    text-align: center;
    padding: 60px 20px;
    
    i {
      color: #dee2e6;
      font-size: 48px;
      margin-bottom: 16px;
    }
    
    h5 {
      color: #212529;
      font-size: 18px;
      font-weight: 600;
      margin-bottom: 8px;
    }
    
    p {
      color: #6c757d;
      font-size: 14px;
      margin-bottom: 24px;
    }
    
    .btn {
      font-size: 14px;
      padding: 8px 16px;
      border-radius: 4px;
    }
  }
}