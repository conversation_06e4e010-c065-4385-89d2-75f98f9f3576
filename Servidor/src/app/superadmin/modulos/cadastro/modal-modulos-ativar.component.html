<kendo-dialog-titlebar (close)="fecheModal()">
  <div class="d-flex align-items-center">
    <i class="fas fa-cubes fa-lg text-primary mr-3"></i>
    <div>
      <h4 class="modal-title mb-0">Ativar Módulos</h4>
      <small class="text-muted">Selecione os módulos que deseja ativar para a empresa</small>
    </div>
  </div>
</kendo-dialog-titlebar>

<div class="modal-body">
  <!-- Controle de Visualização -->
  <div class="alert alert-info border-left-info" *ngIf="moduloInicialSelecionado">
    <div class="d-flex justify-content-between align-items-center">
      <div class="d-flex align-items-center">
        <i class="fas fa-info-circle fa-lg mr-3"></i>
        <div>
          <strong>{{ moduloInicialSelecionado.nome }}</strong> pré-selecionado
          <small class="d-block text-muted">Você pode selecionar módulos adicionais</small>
        </div>
      </div>
      <button type="button" class="btn btn-sm btn-outline-primary"
              (click)="toggleExibirTodosModulos()">
        <i [ngClass]="mostrarTodosModulos ? 'fa-eye-slash' : 'fa-eye'" class="fas mr-1"></i>
        {{ mostrarTodosModulos ? 'Mostrar apenas selecionado' : 'Ver todos os módulos' }}
      </button>
    </div>
  </div>

  <!-- Seleção de Módulos -->
  <div class="card mb-4">
    <div class="card-header bg-light">
      <h6 class="mb-0">
        <i class="fas fa-hand-pointer mr-2"></i>
        {{ mostrarTodosModulos ? 'Selecione os módulos para ativar:' : 'Módulo selecionado:' }}
        <span class="badge badge-primary ml-2">{{ modulosParaExibir.length }}</span>
      </h6>
    </div>
    <div class="card-body">
      <div class="row">
        <div class="col-lg-6 mb-3" *ngFor="let modulo of modulosParaExibir">
          <div class="card modulo-selecao h-100"
               [ngClass]="{'border-primary bg-light shadow-sm': modulo.selecionado, 'border-light': !modulo.selecionado}">
            <div class="card-body">
              <div class="custom-control custom-checkbox mb-3">
                <input type="checkbox" class="custom-control-input"
                       [id]="'modulo' + modulo.id" [(ngModel)]="modulo.selecionado"
                       (change)="calcularTotal()">
                <label class="custom-control-label font-weight-bold" [for]="'modulo' + modulo.id">
                  <i class="fas fa-cube mr-2"></i>{{ modulo.nome }}
                </label>
              </div>

              <p class="text-muted small mb-3">{{ modulo.descricao }}</p>

              <div class="pricing-display">
                <div class="d-flex justify-content-between align-items-center mb-2">
                  <span class="badge badge-success">
                    <i class="fas fa-calendar mr-1"></i>
                    {{ modulo.valorMensalidade | currency:'BRL':'symbol':'1.2-2' }}/mês
                  </span>
                  <span class="badge"
                        [ngClass]="modulo.valorAtivacao > 0 ? 'badge-primary' : 'badge-success'">
                    <i class="fas" [class]="modulo.valorAtivacao > 0 ? 'fa-bolt' : 'fa-gift'" class="mr-1"></i>
                    {{ modulo.valorAtivacao > 0 ? (modulo.valorAtivacao | currency:'BRL':'symbol':'1.2-2') : 'Grátis' }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Resumo da Fatura -->
  <div class="card border-warning mb-4" *ngIf="totalPagar > 0">
    <div class="card-header bg-warning text-dark">
      <h6 class="mb-0">
        <i class="fas fa-file-invoice-dollar mr-2"></i>
        Resumo da Fatura de Ativação
      </h6>
    </div>
    <div class="card-body">
      <!-- Módulos Selecionados -->
      <div class="modulo-fatura mb-4" *ngFor="let modulo of modulosSelecionados">
        <div class="card border-left-primary">
          <div class="card-header bg-light py-2">
            <h6 class="mb-0">
              <i class="fas fa-cube text-primary mr-2"></i>
              {{ modulo.nome }}
            </h6>
          </div>
          <div class="card-body">
            <div class="alert alert-success mb-3" *ngIf="!modulo.itensFaturaveis.length">
              <i class="fas fa-gift mr-2"></i>
              <strong>Ativação Gratuita</strong> - Este módulo será ativado imediatamente sem cobrança
            </div>

            <div class="item-fatura mb-3" *ngFor="let itemFatura of modulo.itensFaturaveis">
              <div class="row align-items-center">
                <div class="col-md-4">
                  <h6 class="font-weight-bold mb-1">{{itemFatura.descricao}}</h6>
                  <small class="text-muted">{{itemFatura.tipo}}</small>
                </div>

                <!-- Valor da Mensalidade -->
                <div class="col-md-4" *ngIf="!itemFatura.taxa">
                  <label class="form-label small text-success font-weight-bold">Valor Mensal</label>
                  <div class="input-group">
                    <div class="input-group-prepend">
                      <span class="input-group-text bg-success text-white">R$</span>
                    </div>
                    <input type="number" class="form-control text-right font-weight-bold"
                           [(ngModel)]="itemFatura.total"
                           (change)="atualizarValorMensalidade(modulo, itemFatura.total)"
                           step="0.01" min="0" placeholder="0,00">
                  </div>
                  <small class="text-muted" *ngIf="modulo.mesesRestantes > 1">
                    <i class="fas fa-info-circle mr-1"></i>
                    {{modulo.mesesRestantes }} meses até próxima fatura
                  </small>
                </div>

                <!-- Valor da Ativação -->
                <div class="col-md-4" *ngIf="itemFatura.taxa">
                  <label class="form-label small text-primary font-weight-bold">Taxa de Ativação</label>
                  <div class="input-group">
                    <div class="input-group-prepend">
                      <span class="input-group-text bg-primary text-white">R$</span>
                    </div>
                    <input type="number" class="form-control text-right font-weight-bold"
                           [(ngModel)]="itemFatura.total"
                           (change)="atualizarValorAtivacao(modulo, itemFatura.total)"
                           step="0.01" min="0" placeholder="0,00">
                  </div>
                  <small class="text-muted">Taxa única de ativação</small>
                </div>

                <div class="col-md-4" *ngIf="!itemFatura.taxa">
                  <div class="text-center">
                    <i class="fas fa-calendar-alt fa-2x text-success mb-1"></i>
                    <div class="small text-muted">Cobrança mensal</div>
                  </div>
                </div>
              </div>

            </div>
          </div>
        </div>
      </div>

      <!-- Resumo Total -->
      <div class="card bg-warning">
        <div class="card-body">
          <div class="row text-center">
            <div class="col-md-4">
              <div class="resumo-item">
                <i class="fas fa-calendar-alt fa-2x text-success mb-2"></i>
                <h6 class="font-weight-bold">Total Mensalidades</h6>
                <div class="h4 font-weight-bold text-success">
                  {{ totalMensalidades | currency:'BRL':'symbol':'1.2-2' }}
                </div>
                <small class="text-muted" *ngIf="modulosSelecionados[0].mesesRestantes > 1">
                  <i class="fas fa-info-circle mr-1"></i>
                  Cobrança proporcional até próximo vencimento: <b>{{modulosSelecionados[0].dataProximoVencimento}}</b>
                </small>
              </div>
            </div>
            <div class="col-md-4">
              <div class="resumo-item">
                <i class="fas fa-bolt fa-2x text-primary mb-2"></i>
                <h6 class="font-weight-bold">Total Ativações</h6>
                <div class="h4 font-weight-bold text-primary">
                  {{ totalAtivacao | currency:'BRL':'symbol':'1.2-2' }}
                </div>
                <small class="text-muted">
                  <i class="fas fa-info-circle mr-1"></i>
                  Taxas únicas por módulo
                </small>
              </div>
            </div>
            <div class="col-md-4">
              <div class="resumo-item">
                <i class="fas fa-file-invoice-dollar fa-2x text-dark mb-2"></i>
                <h6 class="font-weight-bold">Total da Fatura</h6>
                <div class="h3 font-weight-bold text-dark">
                  {{ totalPagar | currency:'BRL':'symbol':'1.2-2' }}
                </div>
                <small class="text-muted">
                  <i class="fas fa-info-circle mr-1"></i>
                  Valor total a ser cobrado
                </small>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Informações Importantes -->
  <div class="alert alert-info border-left-info" *ngIf="totalPagar > 0">
    <h6 class="alert-heading">
      <i class="fas fa-info-circle mr-2"></i>
      Como funciona a cobrança:
    </h6>
    <ul class="mb-0 small">
      <li><strong>Mensalidades:</strong> Valor calculado proporcionalmente pelos meses restantes na assinatura atual</li>
      <li><strong>Ativações:</strong> Taxas únicas cobradas apenas na primeira ativação do módulo</li>
      <li><strong>Descontos:</strong> Você pode ajustar os valores acima para aplicar descontos personalizados</li>
    </ul>
  </div>

  <!-- Módulos Gratuitos -->
  <div class="alert alert-success border-left-success" *ngIf="modulosSelecionadosGratuitos.length > 0">
    <h6 class="alert-heading">
      <i class="fas fa-gift mr-2"></i>
      Ativação Gratuita
    </h6>
    <p class="mb-0">
      <strong>{{ modulosSelecionadosGratuitos.length }}</strong>
      módulo(s) selecionado(s) serão ativados imediatamente sem cobrança.
    </p>
  </div>

  <!-- Mensagens de Erro -->
  <div class="alert alert-danger alert-dismissible fade show" role="alert" *ngIf="mensagemErro">
    <i class="fas fa-exclamation-circle mr-2"></i>
    {{ mensagemErro }}
    <button type="button" class="close" (click)="limpeMensagens()" aria-label="Close">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
</div>

<div class="modal-footer bg-light">
  <div class="d-flex justify-content-between align-items-center w-100">
    <div class="selection-summary">
      <span class="text-muted">
        <i class="fas fa-cubes mr-1"></i>
        {{ modulosSelecionados.length }} módulo(s) selecionado(s)
      </span>
    </div>

    <div class="action-buttons">
      <button type="button" class="btn btn-secondary mr-2"
              (click)="fecheModal()" [disabled]="processandoAtivacao">
        <i class="fas fa-times mr-1"></i>
        Cancelar
      </button>
      <button type="button" class="btn btn-primary btn-lg"
              (click)="confirmarAtivacao()"
              [disabled]="modulosSelecionados.length === 0 || processandoAtivacao">
        <i class="fas fa-spinner fa-spin mr-1" *ngIf="processandoAtivacao"></i>
        <i class="fas fa-check mr-1" *ngIf="!processandoAtivacao"></i>
        {{ processandoAtivacao ? 'Processando Ativação...' : 'Confirmar Ativação' }}
        <span class="badge badge-light ml-2" *ngIf="!processandoAtivacao && totalPagar > 0">
          {{ totalPagar | currency:'BRL':'symbol':'1.2-2' }}
        </span>
      </button>
    </div>
  </div>
</div>
