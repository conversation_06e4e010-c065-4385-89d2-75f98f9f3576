import {Component, Input, OnInit} from "@angular/core";
import {ModulosService} from "../../../services/modulos.service";
import {DialogRef, DialogService} from "@progress/kendo-angular-dialog";
import {KendoPopupUtils} from "../../../lib/KendoPopupUtils";
import {ModalModulosEmpresaComponent} from "./modal-modulos-ativar.component";

declare var $: any;

@Component({
  selector: 'app-modulos-empresa',
  templateUrl: './modulos-empresa.component.html',
  styleUrls: ['./modulos-empresa.component.scss']
})
export class ModulosEmpresaComponent implements OnInit {
  @Input() empresa: any = {};
  @Input() usuario: any = {};

  salvando = false;
  mensagemErro: string;
  mensagemSucesso: string;
  modulos: any[] = [];
  modulosAtivos: any[] = [];
  modulosDisponiveis: any[] = [];
  faturasPendentes: any[] = [];
  carregando = true;
  empresaBloqueada: boolean;
  modulo: any;
  listaStatus = [
    {nome: "Novo", status: "0"},
    {nome: "Em preparação", status: "1"} ,
    {nome: "Pronto", status: "2"},
    {nome: "Saiu para entrega", status: "3"},
    {nome: "Entregue", status: "4"},
    {nome: "Cancelado", status: "5"},
    {nome: "Devolvido", status: "6"}
  ]

  constructor(
    private modulosService: ModulosService,
    private dialogService: DialogService
  ) { }

  ngOnInit() {
    this.carregueModulos();

    if(this.empresa.statusPedidoAoAceitar)
      for(let i = 0; i < this.listaStatus.length; i++)
        if(this.empresa.statusPedidoAoAceitar === this.listaStatus[i].status)
          this.empresa.objetoStatusPedidoAoAceitar = this.listaStatus[i]


  }

  carregueModulos(): void {
    this.carregando = true;
    this.modulos = [];
    this.modulosAtivos = [];
    this.modulosDisponiveis = [];
    this.limpeMensagens();

    // Buscar módulos com valores calculados baseados no contrato
    this.modulosService.obtenhaModulosComValoresCalculados(this.empresa.id).then((resposta: any) => {
      this.empresaBloqueada = resposta.bloqueada;
      this.modulos = resposta.modulos || [];
      this.faturasPendentes = resposta.faturasPendentes || []
      this.organizarModulos();
      this.carregando = false
    }).catch((erro: any) => {
      console.error('Erro ao carregar módulos:', erro);
      this.mensagemErro = 'Erro ao carregar módulos. Tente novamente.';
      this.carregando = false;
    });
  }

  organizarModulos(): void {
    this.modulosAtivos = [];
    this.modulosDisponiveis = [];

    // Garantir que temos um array válido
    if (!this.modulos || !Array.isArray(this.modulos)) {
      this.modulos = [];
      return;
    }

    // Separar módulos ativos dos disponíveis
    this.modulos.forEach((modulo) => {
      if (modulo.ativo) {
        this.modulosAtivos.push({...modulo, ativo: true});
      } else {
        // Usar valores calculados se disponíveis, senão usar originais
        const moduloDisponivel = {
          ...modulo,
          selecionado: false,
          valorMensalidadeEditavel: modulo.valorMensalidadeCalculado || modulo.valorMensalidade || 0,
          valorAtivacaoEditavel: modulo.valorAtivacaoCalculado || modulo.valorAtivacao || 0,
          // Garantir que campos de contrato estejam definidos
          tipoAssinatura: modulo.tipoAssinatura || 'mensal',
          // Manter valores originais para comparação
          valorMensalidadeCalculado: modulo.valorMensalidadeCalculado || modulo.valorMensalidade || 0,
          valorAtivacaoCalculado: modulo.valorAtivacaoCalculado || modulo.valorAtivacao || 0
        };
        this.modulosDisponiveis.push(moduloDisponivel);
      }
    });
  }

  abrirModalAtivacao(): any {
    // Verificar se empresa está bloqueada
    if (this.verificarBloqueioEmpresa()) {
      return;
    }

    const windowRef: DialogRef = this.dialogService.open({
      content: ModalModulosEmpresaComponent,
      minWidth: 250,
      width: window.innerWidth > 900 ? 900 : window.innerWidth,
      cssClass: 'bsModal',
      maxHeight:  window.innerHeight - 100

    });

    KendoPopupUtils.abriuPopupNgBootstrap(windowRef)
    let tela: any =  windowRef.content.instance;
    tela.empresa = this.empresa;
    tela.modulosDisponiveis = this.modulosDisponiveis;

    windowRef.result.subscribe( (result: any) => {
        if(result  && result.modulosAtivados)
          this.carregueModulos();

      },
      (a) => {      });


    return tela;
  }

  ativarModuloUnico(modulo: any): void {
    // Verificar se empresa está bloqueada
    if (this.verificarBloqueioEmpresa()) {
      return;
    }

    // Pré-selecionar o módulo clicado
    const moduloSelecionado = this.modulosDisponiveis.find(m => m.id === modulo.id);

    if(moduloSelecionado){
      let tela: any = this.abrirModalAtivacao();

      if(tela){
        setTimeout(() => {
          moduloSelecionado.selecionado = true;
          tela.modulosSelecionados = [moduloSelecionado];
          tela.moduloInicialSelecionado = moduloSelecionado;
          tela.calcularTotal();
          tela.mostrarTodosModulos = false; // Inicialmente mostra apenas o selecionado
        }, 0)
      }
    }
  }

  desativarModulo(modulo: any): void {
    if (!confirm(`Deseja realmente desativar o módulo "${modulo.nome}"?`)) {
      return;
    }

    this.salvando = true;
    this.limpeMensagens();

    this.modulosService.desativarModuloNaEmpresa(this.empresa.id, modulo.id).then((resposta: any) => {
      this.salvando = false;
      this.carregueModulos();
    }).catch((erro: any) => {
      this.salvando = false;
      console.error('Erro ao desativar módulo:', erro);
      this.mensagemErro = 'Erro ao desativar módulo: '  + (erro.message || erro)
    });
  }

  atualizarEmpresaModulos(novosModulos: any[]): void {
    // Atualizar a lista local
    novosModulos.forEach(modulo => {
      this.modulosAtivos.push({...modulo, ativo: true});
      const index = this.modulosDisponiveis.findIndex(m => m.id === modulo.id);
      if (index > -1) {
        this.modulosDisponiveis.splice(index, 1);
      }
    });

    // Atualizar empresa
    if (!this.empresa.modulos) {
      this.empresa.modulos = [];
    }
    this.empresa.modulos.push(...novosModulos);
  }

  removerModuloAtivo(modulo: any): void {
    // Remover da lista de ativos
    const indexAtivo = this.modulosAtivos.findIndex(m => m.id === modulo.id);
    if (indexAtivo > -1) {
      this.modulosAtivos.splice(indexAtivo, 1);
    }

    // Adicionar na lista de disponíveis com todos os campos necessários
    const moduloDisponivel = {
      ...modulo,
      selecionado: false,
      valorMensalidadeEditavel: modulo.valorMensalidadeCalculado || modulo.valorMensalidade || 0,
      valorAtivacaoEditavel: modulo.valorAtivacaoCalculado || modulo.valorAtivacao || 0,
      tipoAssinatura: modulo.tipoAssinatura || 'mensal',
      valorMensalidadeCalculado: modulo.valorMensalidadeCalculado || modulo.valorMensalidade || 0,
      valorAtivacaoCalculado: modulo.valorAtivacaoCalculado || modulo.valorAtivacao || 0
    };
    this.modulosDisponiveis.push(moduloDisponivel);

    // Remover da empresa
    if (this.empresa.modulos) {
      const indexEmpresa = this.empresa.modulos.findIndex((m: any) => m.id === modulo.id);
      if (indexEmpresa > -1) {
        this.empresa.modulos.splice(indexEmpresa, 1);
      }
    }
  }

  limpeMensagens(): void {
    this.mensagemErro = '';
    this.mensagemSucesso = '';
  }

  verificarBloqueioEmpresa(): boolean {
    if (this.empresaBloqueada) {
      this.mensagemErro = 'Empresa está bloqueada e não pode ativar novos módulos. Entre em contato com o suporte.';
      return true;
    }
    return false;
  }

  verificarStatusFatura(fatura: any) {
     fatura.sincronizando = true;
     this.modulosService.sincronizeFaturaAtivacao(fatura).then( (resposta: any) => {
       fatura.sincronizando = false;
       if(resposta.status !== fatura.status ){
         this.carregueModulos();
       }


     }).catch((err) => {
       fatura.sincronizando = false;
       alert(err)
     })
  }


  escolheuStatusPedidoAoAceitar() {
    if(!this.empresa.objetoStatusPedidoAoAceitar)
      this.empresa.statusPedidoAoAceitar = null

    this.empresa.statusPedidoAoAceitar = this.empresa.objetoStatusPedidoAoAceitar.status
  }

  salvarConfiguracoesModulo(modulo: any) {
    this.salvando = true;
    this.limpeMensagens();
    this.modulosService.atualizeConfigModulo(this.empresa.id, this.empresa)
      .then(() => {
        this.mensagemSucesso = 'Configurações salvas com sucesso!';
      })
      .catch(erro => {
        this.mensagemErro = 'Erro ao salvar configurações: ' + erro;
      })
      .finally(() => {
        this.salvando = false;
      });
  }

  /**
   * Alterna a exibição das configurações de um módulo
   */
  toggleConfiguracoes(modulo: any): void {
    try {
      // Inicializa a propriedade se não existir
      if (modulo.mostrarConfiguracoes === undefined) {
        modulo.mostrarConfiguracoes = false;
      }
      
      // Alterna o estado
      modulo.mostrarConfiguracoes = !modulo.mostrarConfiguracoes;
      
      console.log(`Configurações do módulo ${modulo.nome}: ${modulo.mostrarConfiguracoes ? 'Expandidas' : 'Recolhidas'}`);
    } catch (error) {
      console.error('Erro ao alternar configurações do módulo:', error);
      this.mensagemErro = 'Erro ao exibir configurações do módulo';
    }
  }

  /**
   * Verifica se o módulo tem configurações disponíveis
   */
  temConfiguracoes(modulo: any): boolean {
    if (!modulo) {
      return false;
    }

    const modulosComConfiguracoes = [
      'pedidos',
      'controle de estoque', 
      'cardápio',
      'app'
    ];
    
    // Google Maps tem ID 10 e também tem configurações
    return modulosComConfiguracoes.includes(modulo.nome?.toLowerCase()) || modulo.id === 10;
  }

  /**
   * Recolhe todas as configurações de módulos
   */
  recolherTodasConfiguracoes(): void {
    if (this.modulosAtivos && this.modulosAtivos.length > 0) {
      this.modulosAtivos.forEach(modulo => {
        modulo.mostrarConfiguracoes = false;
      });
    }
  }

  /**
   * Expande todas as configurações de módulos
   */
  expandirTodasConfiguracoes(): void {
    if (this.modulosAtivos && this.modulosAtivos.length > 0) {
      this.modulosAtivos.forEach(modulo => {
        if (this.temConfiguracoes(modulo)) {
          modulo.mostrarConfiguracoes = true;
        }
      });
    }
  }

}
