h5{
  color: #fff;
  font-size: 17px;
}

// Estilos para a tela de ativação de módulos

.card {
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid #e9ecef;
  background: #ffffff;


  h6{
    font-size: 1.0em;
    color: #495057;
  }

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    border-color: #dee2e6;
  }

  &.h-100 {
    height: 100% !important;

    .card-body {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
    }
  }

  // Headers dos painéis principais mantêm as cores
  .card-header {
    &.bg-success {
      background: linear-gradient(135deg, #28a745, #20c997) !important;
      border-bottom: none;
    }

    &.bg-primary {
      background: linear-gradient(135deg, #007bff, #6610f2) !important;
      border-bottom: none;
    }

    &.bg-warning {
      background: linear-gradient(135deg, #ffc107, #fd7e14) !important;
      border-bottom: none;
      color: #212529 !important;
    }
  }
}

// Estilos para os cards de módulos individuais
.modulo-card {
  &.ativo,
  &.disponivel {
    .card {
      // Usar estilo padrão neutro
      border-color: #e9ecef;
      background: #ffffff;

      &:hover {
        border-color: #adb5bd;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
      }
    }
  }
}

// Botões com animações
.btn {
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
    box-shadow: none !important;

    &:hover {
      transform: none;
      box-shadow: none;
    }
  }

  &.btn-lg {
    padding: 0.75rem 1.5rem;
    font-size: 1.1rem;
    font-weight: 600;

    .badge {
      animation: pulse 2s infinite;
    }
  }

  &.btn-outline-primary {
    border-width: 2px;

    &:hover:not(:disabled) {
      background: linear-gradient(135deg, #007bff, #0056b3);
      border-color: #0056b3;
    }
  }

  &.btn-outline-danger {
    border-width: 2px;

    &:hover:not(:disabled) {
      background: linear-gradient(135deg, #dc3545, #c82333);
      border-color: #c82333;
    }
  }
}

// Badges e indicadores
.badge {
  font-size: 0.75rem;
  padding: 0.4rem 0.6rem;

  &.badge-success {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: #fff !important;
  }

  &.badge-secondary {
    background: linear-gradient(135deg, #6c757d, #495057);
    color: #fff !important;
  }

  &.badge-info {
    background: linear-gradient(135deg, #6c757d, #495057);
    font-size: 0.65rem;
    padding: 0.25rem 0.4rem;
    border-radius: 0.375rem;
    color: #fff !important;
  }

  &.badge-light {
    color: #495057 !important;
    background: rgba(255, 255, 255, 0.8);
    border: 1px solid #dee2e6;
  }

  &.badge-primary {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: #fff !important;
  }

  &.badge-warning {
    background: linear-gradient(135deg, #ffc107, #fd7e14);
    color: #212529 !important;
  }
}

// Modal customizado
.modal {
  .modal-header {
    &.bg-primary {
      background: linear-gradient(135deg, #007bff, #6610f2) !important;
      border-bottom: none;
    }
  }

  .modal-content {
    border: none;
    border-radius: 0.75rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  }

  .close {
    &.text-white {
      color: #fff !important;
      opacity: 0.8;

      &:hover {
        opacity: 1;
      }
    }
  }
}

// Cards com seleção no modal
.card.border-primary.bg-light {
  background: linear-gradient(135deg, #e3f2fd, #bbdefb) !important;
  border-color: #007bff !important;
  border-width: 2px;

  .custom-control-label {
    font-weight: 600;
    color: #0056b3;
  }
}

// Alertas personalizados
.alert {
  border: none;
  border-radius: 0.5rem;
  padding: 1rem 1.25rem;

  &.alert-success {
    background: linear-gradient(135deg, #d4edda, #c3e6cb);
    color: #155724 !important;
    border-left: 4px solid #28a745;
  }

  &.alert-danger {
    background: linear-gradient(135deg, #f8d7da, #f1b0b7);
    color: #721c24 !important;
    border-left: 4px solid #dc3545;

    .fas.fa-lock {
      color: #dc3545;
      font-size: 1.1rem;
    }
  }

  &.alert-info {
    background: linear-gradient(135deg, #d1ecf1, #bee5eb);
    color: #0c5460 !important;
    border-left: 4px solid #17a2b8;
  }

  &.alert-warning {
    background: linear-gradient(135deg, #fff3cd, #ffeaa7);
    color: #856404 !important;
    border-left: 4px solid #ffc107;
  }
}

// Spinner de loading
.spinner-border {
  &.text-primary {
    color: #007bff !important;
  }
}

// Estados de carregamento
.loading-overlay {
  position: relative;

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 0.5rem;
    z-index: 10;
  }
}

// Ícones com animações - CORRIGIDO
.fa, .fas {
  transition: all 0.3s ease;

  &.fa-spin {
    animation: fa-spin 1s infinite linear;
  }

  // Ícones de ativação gratuita mais visíveis - FORÇAR APARIÇÃO
  &.fa-gift {
    color: #28a745 !important;
    font-weight: bold !important;
    text-shadow: 0 1px 2px rgba(40, 167, 69, 0.3) !important;
    display: inline-block !important;
    opacity: 1 !important;
    visibility: visible !important;
    font-size: 1em !important;
  }

  &.fa-bolt {
    color: #007bff !important;
    text-shadow: 0 1px 2px rgba(0, 123, 255, 0.3) !important;
    display: inline-block !important;
    opacity: 1 !important;
    visibility: visible !important;
    font-size: 1em !important;
  }

  &.fa-calendar {
    color: #28a745 !important;
  }
}

// Animações personalizadas
@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Aplicar fadeIn aos cards
.modulo-card {
  animation: fadeIn 0.5s ease-in-out;

  &:nth-child(1) { animation-delay: 0.1s; }
  &:nth-child(2) { animation-delay: 0.2s; }
  &:nth-child(3) { animation-delay: 0.3s; }
  &:nth-child(4) { animation-delay: 0.4s; }
  &:nth-child(5) { animation-delay: 0.5s; }
  &:nth-child(6) { animation-delay: 0.6s; }
}

// Responsividade aprimorada
@media (max-width: 768px) {
  .btn-lg {
    font-size: 1rem;
    padding: 0.5rem 1rem;
  }

  .card-title {
    font-size: 1rem;
  }

  .modal-dialog {
    margin: 0.5rem;
  }
}

// Estados hover para melhor UX
.custom-control-input:checked ~ .custom-control-label::before {
  background: linear-gradient(135deg, #007bff, #0056b3);
  border-color: #007bff;
}

.custom-control-label::before {
  border-width: 2px;
  transition: all 0.3s ease;
}

.custom-control-label:hover::before {
  border-color: #007bff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

// Estilos para valores monetários
.valor-monetario {
  font-family: 'Roboto Mono', monospace;
  font-weight: 600;
  color: #495057;

  &.valor-gratis {
    color: #28a745 !important;
    text-shadow: 0 1px 2px rgba(40, 167, 69, 0.2);
  }

  &.valor-pago {
    color: #495057;
  }
}

// Estilos para inputs editáveis de valores
.input-group-sm {
  .form-control {
    font-family: 'Roboto Mono', monospace;
    font-size: 0.8rem;

    &:focus {
      border-color: #007bff;
      box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }
  }

  .input-group-text {
    font-size: 0.8rem;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-color: #ced4da;
    color: #495057;
  }
}

// Informações de contrato
.contrato-info {
  background: linear-gradient(135deg, #e8f4fd, #d1ecf1);
  border-radius: 0.375rem;
  padding: 0.5rem;
  margin-bottom: 0.5rem;
  border-left: 3px solid #17a2b8;

  .fas {
    color: #17a2b8;
  }
}

// Comparação de valores (original vs editado)
.valor-comparacao {
  font-size: 0.75rem;
  font-style: italic;

  &.valor-alterado {
    color: #fd7e14;
    font-weight: 600;

    &::before {
      content: "✏️ ";
      margin-right: 0.25rem;
    }
  }
}

// Melhorias no layout do resumo da fatura
.resumo-fatura {
  background: linear-gradient(135deg, #fff8e1, #fff3c4);
  border: 2px solid #ffc107;
  border-radius: 0.75rem;

  .card-header {
    &.bg-light {
      background: linear-gradient(135deg, #f8f9fa, #e9ecef) !important;
      border-bottom: 1px solid #dee2e6;
      border-radius: 0.375rem 0.375rem 0 0;
    }
  }

  .total-fatura {
    font-size: 1.5rem;
    font-weight: 700;
    color: #495057 !important;
    text-shadow: none;
  }

  // Cards individuais dos módulos no resumo
  .card.border-light {
    border: 1px solid #e9ecef !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    margin-bottom: 1rem;

    &:hover {
      border-color: #007bff !important;
      box-shadow: 0 2px 6px rgba(0, 123, 255, 0.15);
      transform: translateY(-1px);
    }

    .card-header {
      border-bottom: 1px solid #dee2e6;

      h6 {
        margin-bottom: 0.25rem;
        color: #495057;
      }
    }

    .input-group {
      max-width: 150px;

      .form-control {
        font-family: 'Roboto Mono', monospace;
        font-weight: 600;
        font-size: 0.9rem;

        &:focus {
          border-color: #0056b3;
          box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }
      }

      .input-group-text {
        font-weight: 600;
        font-size: 0.8rem;

        &.bg-success {
          background: linear-gradient(135deg, #28a745, #20c997) !important;
          color: white !important;
          border-color: #28a745;
        }

        &.bg-primary {
          background: linear-gradient(135deg, #007bff, #0056b3) !important;
          color: white !important;
          border-color: #007bff;
        }
      }
    }

    // Indicadores de alteração de valor
    .text-muted small {
      font-style: italic;

      .fas {
        &.fa-percentage {
          color: #28a745;
        }
      }
    }

    // Separador entre mensalidade e ativação
    hr {
      margin: 0.75rem 0;
      border-color: #dee2e6;
    }
  }

  // Valores comparativos
  .h6 {
    margin-bottom: 0.25rem;
    font-size: 0.9rem;

    &.text-primary {
      color: #495057 !important;
      font-weight: 600;
    }

    &.text-success {
      color: #495057 !important;
      font-weight: 600;
    }

    &.text-muted {
      color: #6c757d !important;
      opacity: 1;
    }
  }

  // Card de resumo total
  .card.bg-warning {
    background: linear-gradient(135deg, #fff3cd, #ffeaa7) !important;
    border: 2px solid #ffc107;
    border-radius: 0.5rem;
    margin-top: 1rem;

    .card-body {
      padding: 1.25rem;
    }

    .h3, .h4, .h5 {
      margin-bottom: 0.5rem;
      text-shadow: none;
      color: #495057 !important;

      &.text-success {
        color: #495057 !important;
      }

      &.text-primary {
        color: #495057 !important;
      }

      &.text-dark {
        color: #212529 !important;
      }
    }

    hr {
      border-color: #ffc107;
      margin: 1rem 0;
    }
  }
}

// Estilos para controle de visualização de módulos
.controle-visualizacao {
  .alert {
    border-radius: 0.5rem;
    border: 1px solid #bee5eb;

    .btn {
      transition: all 0.3s ease;

      &:hover {
        transform: scale(1.05);
      }
    }
  }
}

// Contador de módulos no header
.badge-contador {
  background: linear-gradient(135deg, #6c757d, #495057);
  font-size: 0.7rem;
  padding: 0.25rem 0.5rem;
  animation: fadeIn 0.3s ease-in-out;
  color: #fff !important;
}

// Botões de alternar visualização
.btn-toggle-modulos {
  transition: all 0.3s ease;
  border-radius: 0.375rem;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 6px rgba(0, 123, 255, 0.3);
  }

  .fas {
    transition: transform 0.3s ease;
  }

  &:hover .fas {
    transform: scale(1.1);
  }
}

// Mensagem de módulos ocultos
.modulos-ocultos-info {
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border-radius: 0.5rem;
  padding: 1rem;
  border: 1px dashed #6c757d;

  .btn {
    background: linear-gradient(135deg, #007bff, #0056b3);
    border: none;
    color: white;

    &:hover {
      background: linear-gradient(135deg, #0056b3, #004085);
      transform: translateY(-1px);
    }
  }
}

// Modal personalizado
.modal-body {
  max-height: 80vh;
  overflow-y: auto;
  padding: 1.5rem;
}

.modal-footer {
  border-top: 1px solid #e9ecef;
  padding: 1rem 1.5rem;
}

// Header do modal
kendo-dialog-titlebar {
  background: linear-gradient(45deg, #007bff, #0056b3);
  color: white;
  padding: 1rem 1.5rem;
  
  .modal-title {
    font-weight: 600;
    font-size: 1.3rem;
  }
  
  small {
    opacity: 0.9;
  }
}

// Seleção de módulos
.modulo-selecao {
  transition: all 0.3s ease;
  cursor: pointer;
  
  &:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 25px rgba(0, 123, 255, 0.15);
  }
  
  &.border-primary {
    border-color: #007bff !important;
    background: linear-gradient(135deg, rgba(0, 123, 255, 0.05), rgba(0, 123, 255, 0.02));
  }
  
  .custom-control-input:checked ~ .custom-control-label {
    color: #007bff;
    font-weight: 700;
  }
  
  .custom-control-label {
    cursor: pointer;
    transition: all 0.3s ease;
    
    &:before {
      border: 2px solid #007bff;
    }
    
    &:after {
      background-color: #007bff;
    }
  }
}

// Pricing display - CORRIGIDO PARA MOSTRAR ÍCONES COM COR BRANCA
.pricing-display {
  .badge {
    font-size: 0.8rem;
    padding: 0.4em 0.8em;
    
    &.badge-success {
      background: linear-gradient(45deg, #28a745, #20c997) !important;
      color: #fff !important;
      
      // Ícone de presente com cor branca
      .fa-gift {
        color: #fff !important;
        margin-right: 0.25rem !important;
      }
    }
    
    &.badge-primary {
      background: linear-gradient(45deg, #007bff, #6610f2) !important;
      color: #fff !important;
      
      // Ícone de raio com cor branca
      .fa-bolt {
        color: #fff !important;
        margin-right: 0.25rem !important;
      }
    }
  }
}

// Força ícones específicos em qualquer contexto
i.fa-gift, .fa-gift {
  color: #28a745 !important;
  display: inline-block !important;
  opacity: 1 !important;
  visibility: visible !important;
  font-weight: 900 !important;
  
  // Se estiver dentro de badge, usar cor branca
  .badge & {
    color: #fff !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2) !important;
  }
}

i.fa-bolt, .fa-bolt {
  color: #007bff !important;
  display: inline-block !important;
  opacity: 1 !important;
  visibility: visible !important;
  font-weight: 900 !important;
  
  // Se estiver dentro de badge, usar cor branca
  .badge & {
    color: #fff !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2) !important;
  }
}

// Força ícones no contexto específico dos módulos
.modulo-selecao {
  // ... código existente ...
  
  .pricing-display {
    .badge {
      // Garante que os ícones apareçam
      i {
        display: inline-block !important;
        opacity: 1 !important;
        visibility: visible !important;
        font-weight: 900 !important;
        margin-right: 0.25rem !important;
      }
    }
  }
}

// Seleção de módulos com ícones forçados
.card.modulo-selecao {
  .badge {
    position: relative;
    
    i {
      position: relative;
      z-index: 1;
      display: inline-block !important;
      opacity: 1 !important;
      visibility: visible !important;
    }
  }
}

// Fatura de ativação
.modulo-fatura {
  .card {
    margin-bottom: 1.5rem;
    border-radius: 10px;
    overflow: hidden;
  }
  
  .border-left-primary {
    border-left: 5px solid #007bff !important;
  }
  
  .card-header {
    background: linear-gradient(90deg, rgba(0, 123, 255, 0.1), rgba(0, 123, 255, 0.05));
    border-bottom: 1px solid rgba(0, 123, 255, 0.2);
  }
}

// Item de fatura
.item-fatura {
  .form-label {
    font-size: 0.8rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 0.3rem;
  }
  
  .input-group {
    .input-group-text {
      font-weight: bold;
      border: none;
    }
    
    input {
      border-left: none;
      font-size: 1.1rem;
      
      &:focus {
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        border-color: #007bff;
      }
    }
  }
}

// Resumo total
.resumo-item {
  padding: 1rem;
  
  i {
    opacity: 0.8;
  }
  
  h6 {
    color: #495057 !important;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-weight: 600;
  }
  
  .h3, .h4 {
    font-weight: 700;
    margin: 0.5rem 0;
    color: #495057 !important;
    
    &.text-success {
      color: #28a745 !important;
    }
    
    &.text-primary {
      color: #007bff !important;
    }
    
    &.text-dark {
      color: #212529 !important;
    }
  }
  
  small {
    font-size: 0.75rem;
    line-height: 1.3;
    color: #6c757d !important;
  }
}

// Alertas personalizados
.border-left-info {
  border-left: 4px solid #17a2b8 !important;
  background: linear-gradient(90deg, rgba(23, 162, 184, 0.08), rgba(23, 162, 184, 0.03));
}

.border-left-success {
  border-left: 4px solid #28a745 !important;
  background: linear-gradient(90deg, rgba(40, 167, 69, 0.08), rgba(40, 167, 69, 0.03));
}

.alert-heading {
  font-weight: 600;
  font-size: 1rem;
}

// Footer do modal
.modal-footer {
  background: #f8f9fa;
  
  .selection-summary {
    font-size: 0.9rem;
    color: #6c757d;
  }
  
  .action-buttons {
    .btn {
      transition: all 0.3s ease;
      
      &.btn-lg {
        padding: 0.75rem 1.5rem;
        font-size: 1.05rem;
        font-weight: 600;
      }
      
      .badge {
        font-size: 0.75rem;
        font-weight: normal;
      }
    }
  }
}

// Controle de visualização
.btn-toggle-modulos {
  transition: all 0.3s ease;
  
  &:hover {
    transform: scale(1.05);
  }
}

// Cards de módulos
.card {
  border-radius: 10px;
  transition: all 0.3s ease;
  
  &.border-warning {
    border-color: #ffc107 !important;
    box-shadow: 0 4px 15px rgba(255, 193, 7, 0.2);
  }
}

// Botões com estados
.btn {
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.3s ease;
  
  &:hover:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
  }
  
  &:active {
    transform: translateY(0);
  }
  
  &.btn-primary {
    background: linear-gradient(45deg, #007bff, #0056b3);
    border: none;
    
    &:hover {
      background: linear-gradient(45deg, #0056b3, #004085);
    }
  }
  
  &.btn-secondary {
    background: linear-gradient(45deg, #6c757d, #5a6268);
    border: none;
    
    &:hover {
      background: linear-gradient(45deg, #5a6268, #495057);
    }
  }
}

// Scrollbar personalizada
.modal-body::-webkit-scrollbar {
  width: 10px;
}

.modal-body::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 5px;
}

.modal-body::-webkit-scrollbar-thumb {
  background: linear-gradient(45deg, #007bff, #0056b3);
  border-radius: 5px;
  
  &:hover {
    background: linear-gradient(45deg, #0056b3, #004085);
  }
}

// Responsividade do modal
@media (max-width: 991.98px) {
  .modal-body {
    padding: 1rem;
  }
  
  .col-lg-6 {
    margin-bottom: 1rem;
  }
  
  .resumo-item {
    padding: 0.5rem;
    margin-bottom: 1rem;
  }
}

@media (max-width: 575.98px) {
  .modal-footer {
    flex-direction: column;
    
    .d-flex {
      flex-direction: column !important;
      align-items: stretch !important;
    }
    
    .selection-summary {
      margin-bottom: 1rem;
      text-align: center;
    }
    
    .action-buttons {
      .btn {
        margin-bottom: 0.5rem;
        
        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
  
  .item-fatura {
    .row {
      .col-md-4 {
        margin-bottom: 1rem;
      }
    }
  }
}

// Animações
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.modulo-fatura {
  animation: slideInUp 0.4s ease forwards;
}

// Estados de loading
.fa-spin {
  animation: fa-spin 1s infinite linear;
}

@keyframes fa-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// FORÇA GLOBAL DE ÍCONES - adicione no final do arquivo

// Regras muito específicas para sobrescrever qualquer CSS que esconda os ícones
.modal-body {
  .card {
    .badge {
      .fa, .fas, i {
        display: inline-block !important;
        opacity: 1 !important;
        visibility: visible !important;
        font-weight: 900 !important;
        
        &.fa-gift {
          color: #fff !important;
          text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
        }
        
        &.fa-bolt {
          color: #fff !important;  
          text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
        }
      }
    }
  }
}

// Se mesmo assim não aparecer, use esta regra mais agressiva
.modal-body .badge i:before,
.modal-body .badge .fa:before,
.modal-body .badge .fas:before {
  content: attr(data-fa-content) !important;
  display: inline-block !important;
  font-family: "Font Awesome 5 Free", "Font Awesome 5 Pro" !important;
  font-weight: 900 !important;
  opacity: 1 !important;
  visibility: visible !important;
}

// Força conteúdo específico dos ícones
.modal-body .badge .fa-gift:before {
  content: "\f06b" !important; // Código Unicode do ícone fa-gift
}

.modal-body .badge .fa-bolt:before {
  content: "\f0e7" !important; // Código Unicode do ícone fa-bolt
}

.icon-ativacao {
  font-size: 1.1em;
  display: inline-block !important;
  opacity: 1 !important;
  visibility: visible !important;
}
