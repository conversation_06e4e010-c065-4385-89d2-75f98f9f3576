import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SuperadminComponent } from './superadmin.component';
import {SuperAdminRoutingModule} from "./superadmin-routing.module";
import { MenuComponent } from './menu/menu.component';
import { CadEmpresasComponent } from './cad-empresas/cad-empresas.component';
import {FormsModule} from "@angular/forms";
import {FidelidadeModule} from "../fidelidade/fidelidade.module";
import { ListaEmpresasComponent } from './lista-empresas/lista-empresas.component';
import {ExcelModule, GridModule} from "@progress/kendo-angular-grid";
import {MaskedTextBoxModule, NumericTextBoxModule} from "@progress/kendo-angular-inputs";
import {DateInputsModule, DatePickerModule, TimePickerModule} from "@progress/kendo-angular-dateinputs";
import {DropDownListModule, DropDownsModule} from "@progress/kendo-angular-dropdowns";
import { CadTipoDePontuacaoComponent } from './cad-tipo-de-pontuacao/cad-tipo-de-pontuacao.component';

import {UploadModule} from "@progress/kendo-angular-upload";
import { CadFotoComponent } from './cad-foto/cad-foto.component';
import { ImageCropperModule } from 'ngx-image-cropper';
import {TelaConfiguracoesComponent} from "./tela-configuracoes/tela-configuracoes.component";
import {GridHorariosEditService} from "./cad-empresas/grid-horarios-edit-service";
import {HttpClient, HttpClientJsonpModule} from "@angular/common/http";
import { InputsModule } from '@progress/kendo-angular-inputs';
import {DialogModule, WindowModule} from '@progress/kendo-angular-dialog';
import {TooltipModule} from "@progress/kendo-angular-tooltip";
import { TelaImportarContatosComponent } from './tela-importar-contatos/tela-importar-contatos.component';
import { TelaContratoComponent } from './tela-contrato/tela-contrato.component';
import {CNPJValidador, NomeConmpletoValidator} from "../directives/form-validadores.directive";
import { CadLancamentoComponent } from './cad-lancamento/cad-lancamento.component';
import {ButtonModule} from "@progress/kendo-angular-buttons";
import {CompartilhadoModule} from "../compartilhado/compartilhado.module";
import { TelaListaLeadsComponent } from './tela-lista-leads/tela-lista-leads.component';
import {CurrencyMaskModule} from "ng2-currency-mask";
import { ListaPlanosComponent } from './lista-planos/lista-planos.component';
import { CadVantagemComponent } from './cad-vantagem/cad-vantagem.component';
import { CadPlanoempresarialComponent } from './cad-planoempresarial/cad-planoempresarial.component';
import { CadPlanovantagensComponent } from './cad-planovantagens/cad-planovantagens.component';
import {SortableModule} from "@progress/kendo-angular-sortable";
import { CadEmpresaPagamentoComponent } from './cad-empresa-pagamento/cad-empresa-pagamento.component';
import { GridEmpresasComponent } from './grid-empresas/grid-empresas.component';
import { TelaGerenciarRecebimentosComponent } from './tela-gerenciar-recebimentos/tela-gerenciar-recebimentos.component';
import { GridFaturasComponent } from './grid-faturas/grid-faturas.component';
import { StatusUsoEmpresaComponent } from './status-uso-empresa/status-uso-empresa.component';
import {SiteEmpresaModule} from "../site-empresa/site-empresa.module";
import { TabFinanceiroComponent } from './tab-financeiro/tab-financeiro.component';
import { UploadMultiplasImagensComponent } from './upload-multiplas-imagens/upload-multiplas-imagens.component';
import { HistoricoOperacoesComponent } from './historico-operacoes/historico-operacoes.component';
import {PagerModule} from "@progress/kendo-angular-pager";
import { ListaDeCampanhasComponent } from './lista-de-campanhas/lista-de-campanhas.component';
import { GridNfseComponent } from './grid-nfse/grid-nfse.component';
import { CadGrupoLojasComponent } from './cad-grupo-lojas/cad-grupo-lojas.component';
import { ListaGrupoLojasComponent } from './cad-grupo-lojas/lista-grupo-lojas/lista-grupo-lojas.component';
import {LayoutModule, PanelBarModule} from "@progress/kendo-angular-layout";
import { CadEmpresaGrupoComponent } from './cad-grupo-lojas/cad-empresa-grupo/cad-empresa-grupo.component';
import { TelaEscolherEmpresaComponent } from './cad-grupo-lojas/tela-escolher-empresa/tela-escolher-empresa.component';
import { GridCancelamentosEmpresasComponent } from './grid-cancelamentos-empresas/grid-cancelamentos-empresas.component';
import { TelaResumoFinanceiroEcleticaComponent } from './tela-resumo-financeiro-ecletica/tela-resumo-financeiro-ecletica.component';
import {LabelModule} from "@progress/kendo-angular-label";
import { PedidosIntegradosComponent } from './pedidos-integrados/pedidos-integrados.component';
import {MonitoradorPedidosIntegrados} from "./MonitorPedidosIntegrados";
import { ListaFormasPagamentosComponent } from './lista-formas-pagamentos/lista-formas-pagamentos.component';
import { CadBandeiraComponent } from './cad-bandeira/cad-bandeira.component';
import { CadFormapagamentoPdvComponent } from './cad-formapagamento-pdv/cad-formapagamento-pdv.component';
import { CadCepsCustomizadosComponent } from './cad-ceps-customizados/cad-ceps-customizados.component';
import {ModulosComponent} from "./modulos/modulos.component";

import {ModulosEmpresaComponent} from "./modulos/cadastro/modulos-empresa.component";
import {FormModuloComponent} from "./modulos/cadastro/form-modulo.component";
import {ModalModulosEmpresaComponent} from "./modulos/cadastro/modal-modulos-ativar.component";
import {GridFaturaComponent} from "./cad-empresa-pagamento/grid-fatura.component";


@NgModule({
    declarations: [SuperadminComponent,
        MenuComponent,
        CadEmpresasComponent,
        ListaEmpresasComponent,
        CadTipoDePontuacaoComponent,
        TelaConfiguracoesComponent,
        CadFotoComponent, ModulosComponent, FormModuloComponent, ModulosEmpresaComponent, ModalModulosEmpresaComponent,
        TelaImportarContatosComponent,
        TelaContratoComponent, CNPJValidador, CadLancamentoComponent,
        TelaListaLeadsComponent, ListaPlanosComponent, CadVantagemComponent,
        CadPlanoempresarialComponent, CadPlanovantagensComponent, CadEmpresaPagamentoComponent, GridEmpresasComponent,
        TelaGerenciarRecebimentosComponent, GridFaturasComponent, StatusUsoEmpresaComponent, GridFaturaComponent,
        TabFinanceiroComponent, StatusUsoEmpresaComponent, HistoricoOperacoesComponent, ListaDeCampanhasComponent,
        GridNfseComponent, CadGrupoLojasComponent, ListaGrupoLojasComponent, CadEmpresaGrupoComponent, TelaEscolherEmpresaComponent,
        GridCancelamentosEmpresasComponent, TelaResumoFinanceiroEcleticaComponent, PedidosIntegradosComponent,
        ListaFormasPagamentosComponent,
        CadBandeiraComponent,
        CadFormapagamentoPdvComponent,
        CadCepsCustomizadosComponent
    ],
    imports: [
        CommonModule, DialogModule, TooltipModule, DateInputsModule,
        UploadModule,
        SuperAdminRoutingModule,
        FormsModule,
        MaskedTextBoxModule,
        FidelidadeModule,
        LayoutModule,
        DatePickerModule, TimePickerModule,
        DropDownListModule,
        NumericTextBoxModule,
        ImageCropperModule, InputsModule,
        HttpClientJsonpModule,
        WindowModule, ButtonModule, DialogModule,
        CompartilhadoModule, CurrencyMaskModule,
        GridModule,
        DropDownsModule, SortableModule, SiteEmpresaModule, PagerModule, PanelBarModule, LabelModule, ExcelModule
    ],
    providers: [
        MonitoradorPedidosIntegrados,
        {
            deps: [HttpClient],
            provide: GridHorariosEditService,
            useFactory: (jsonp: HttpClient) => () => new GridHorariosEditService(jsonp)
        }
    ]
})
export class SuperadminModule { }
