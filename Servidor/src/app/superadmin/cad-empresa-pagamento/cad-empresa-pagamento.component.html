<h4 class="page-title">
  <button class="btn btn-outline-blue btn-rounded" (click)="voltar()">
    <i class="fa fa-arrow-left ct-point" ></i>
  </button>&nbsp;&nbsp; <i class="fa fa-money-check"></i>
  <span  > <PERSON><PERSON><PERSON> </span>

</h4>

<div class="card-box">
  <form [ngClass]="{'needs-validation': !frm.submitted, 'was-validated': frm.submitted}"
        novalidate #frm="ngForm" (ngSubmit)="onSubmit()" >

    <fieldset>
      <legend>Dados da empresa</legend>

      <div class="row" *ngIf="!naoPossuiCnpj">
        <div class="form-group mb-3 col-4">
          <label for="cnpj">CNPJ</label>
          <kendo-maskedtextbox     class="form-control" autocomplete="off" cnpjValido  mask="00.000.000/0000-00"
                                   id="cnpj" name="cnpj" [(ngModel)]="empresa.cnpj" #cnpj="ngModel" [disabled]="buscandoEmpresa"
                                   placeholder="CNPJ da Empresa"   [required]="!naoPossuiCnpj"  (ngModelChange)="alterouCnpj()" >

          </kendo-maskedtextbox>
          <div class="invalid-feedback">
            <p *ngIf="cnpj.errors?.required">CNPJ é obrigatório</p>
            <p *ngIf="cnpj.errors?.cnpjInvalido">CNPJ inválido</p>
          </div>
        </div>

        <div class="form-group mb-3 col-5">
          <label for="email">Não possui CNPJ?</label>
          <div>
            <button class="btn btn-light" (click)="cadastrarResponsavel()">Cadastrar cpf</button>
          </div>

        </div>
      </div>
      <div class="row" *ngIf="naoPossuiCnpj">
        <div class="form-group mb-3 col-3">
          <label for="cpf">Cpf do Responsável</label>

          <kendo-maskedtextbox class="form-control" autocomplete="off" cpfValido mask="000.000.000-00"
                               id="cpf" name="cpfResponsavel" [(ngModel)]="responsavel.cpf" #cpfResponsavel="ngModel"
                               placeholder="Cpf do responsável" [required]="naoPossuiCnpj"     appAutoFocus   >

          </kendo-maskedtextbox>
          <div class="invalid-feedback">
            <p *ngIf="cpfResponsavel.errors?.required">CPF é obrigatório</p>
            <p *ngIf="cpfResponsavel.errors?.cpfInvalido">CPF é invalido</p>
          </div>
        </div>

        <div class="form-group mb-3 col-4">
          <label  >Nome do Responsável</label>
          <input type="text" class="form-control" autocomplete="off"
                 name="nomeResponsavel" [(ngModel)]="responsavel.nome" #nomeResponsavel="ngModel"
                 placeholder="Nome do responsável" value=""  [required]="naoPossuiCnpj"    >
          <div class="invalid-feedback">
            <p *ngIf="nomeResponsavel.errors?.required">Nome é obrigatório</p>
          </div>
        </div>
        <div class="form-group mb-3 col-2">
          <label for="email">Tem o CNPJ?</label>
          <div>
            <button class="btn btn-light" (click)="cadastrarCNPJ()">CNPJ</button>
          </div>

        </div>

      </div>


      <div class="row">
        <div class="form-group mb-3 col-6">
          <label for="nome">Nome: </label>
          <input type="text" class="form-control" autocomplete="off"
                 id="nome" name="nome" [(ngModel)]="empresa.nome" #nome="ngModel"
                 placeholder="Nome da Empresa" value="" required>
          <div class="invalid-feedback">
            <p *ngIf="nome.errors?.required">Nome é obrigatório</p>
          </div>
        </div>

        <div class="form-group mb-3 col-3">
          <label for="dominio">Domínio</label>
          <input type="text" class="form-control" autocomplete="off"
                 id="dominio" name="dominio" [(ngModel)]="empresa.dominio"
                 placeholder="Domínio" value="" required>
          <div class="invalid-feedback">
            <p  >Domínio é obrigatório</p>
          </div>
        </div>

      </div>

      <div class="row">

        <div class="form-group mb-3 col-5">
          <label for="email">E-mail cobrança</label>
          <input type="email" class="form-control" autocomplete="off"
                 id="email" name="email" [(ngModel)]="empresa.email" #email="ngModel"
                 placeholder="Email da empresa"    required    >

          <div class="invalid-feedback">
            <p *ngIf="email.errors?.required">Email é obrigatório</p>

          </div>
        </div>

        <div class="form-group mb-3 col-3">
          <label for="whatsapp">Whatsapp   </label>
          <kendo-maskedtextbox mask="(00) 0-0000-000#"     type="text" class="form-control" autocomplete="off"
                               id="whatsapp" name="whatsapp" [(ngModel)]="empresa.whatsapp" #whatsapp="ngModel"
                               placeholder="Whatsapp da Empresa" value="" required></kendo-maskedtextbox>
          <div class="invalid-feedback">
            <p *ngIf="whatsapp.errors?.required">Whatsapp é obrigatório</p>
            <p *ngIf="whatsapp.errors?.patternError">Número de Whatsapp inválido</p>
          </div>
        </div>


      </div>

      <div class="row cep">
        <div class="form-group mb-3 col-3">
          <label for="nome">CEP</label>
          <div class="" style="position: relative;">
            <kendo-maskedtextbox    (change)="alterou($event)"  [disabled]="buscandoCEP"
                   type="text" class="form-control" autocomplete="off"
                   id="cep" name="cep" [(ngModel)]="empresa.cep" #nome="ngModel"
                   placeholder="Informe o CEP."   mask="00.000-000" ></kendo-maskedtextbox>

            <i class="k-icon k-i-loading" *ngIf="buscandoCEP"></i>
          </div>
        </div>
      </div>
      <div class="row cep">
        <div class="form-group mb-3 col-4">
          <label for="nome">Logradouro</label>
          <input type="text" class="form-control" autocomplete="off"
                 name="logradouro" [(ngModel)]="enderecoCompleto.logradouro" #logradouro="ngModel"
                 placeholder="Logradouro" required  [disabled]="buscandoCEP">
          <div class="invalid-feedback">
            <p *ngIf="logradouro.errors?.required">Logradouro é obrigatório</p>
          </div>
        </div>

        <div class="form-group mb-3 col-3">
          <label for="nome">Bairro</label>
          <input type="text" class="form-control" autocomplete="off"
                   name="bairro" [(ngModel)]="enderecoCompleto.bairro" #bairro="ngModel"
                 placeholder="Bairro" required  [disabled]="buscandoCEP">
          <div class="invalid-feedback">
            <p *ngIf="bairro.errors?.required">Bairro é obrigatório</p>
          </div>
        </div>

        <div class="form-group mb-3 col-3">
          <label for="nome">Localidade</label>
          <input type="text" class="form-control" autocomplete="off"
                 name="localidade" [(ngModel)]="enderecoCompleto.localidade" #localidade="ngModel"
                 placeholder="Cidade e UF" required     [disabled]="true">
          <div class="invalid-feedback">
            <p *ngIf="localidade.errors?.required">Localidade é obrigatório</p>
          </div>
        </div>

      </div>
    </fieldset>



    <div *ngIf="!empresaJaTemContrato">
      <div class="row" *ngIf="!gerarAssinatura && !gerarFatura">
        <div class="col-12">
          <label> Qual tipo de pagamento vai gerar?</label>
          <div>
            <button type="button" class="btn btn-outline-blue" (click)="selecioneGerarAssinatura()">
              Assinatura ( Mensalidade + Taxa de adesão)
            </button>

            <button type="button" class="btn btn-outline-blue ml-2" (click)=" selecioneGerarFatura()">
              Somente Adesão
            </button>
          </div>

        </div>
      </div>

      <fieldset *ngIf="gerarFatura">
        <legend>Dados da adesão</legend>

        <div class="row">
          <div class="form-group col-4"  >
            <label  >Taxa de Adesão</label>
            <kendo-numerictextbox   [min]="0"  name="taxaAdesao"   [format]="'n2'"  class="form-control"
                                    [(ngModel)]="faturaAdesao.taxaAdesao" required>
            </kendo-numerictextbox>
          </div>

          <div class="form-group col-4">
            <label>Formas de pagamento:</label>

            <kendo-multiselect  name="novaFormaPagamento" [(ngModel)]="faturaAdesao.formaDePagamento" [data]="formasDePagamento"
                                placeholder="Selecione uma forma de pagamento" class="form-control"
                                required >
            </kendo-multiselect>

            <div class="invalid-feedback" >
              Informe uma forma de pagamento
            </div>

          </div>
        </div>

        <div class="mt-4" *ngIf="!faturaAdesao.codigo">
          <button type="submit" class="btn btn-primary waves-effect waves-light" [disabled]="salvando"  >
            <i class="k-i-loading k-icon" *ngIf="salvando"></i>
            Gerar link pagamento</button>
          <button type="button" class="btn btn-light waves-effect ml-2" data-dismiss="modal" (click)="cancele()">Cancelar</button>
        </div>

        <div class="mt-4" *ngIf="faturaAdesao.codigo">
          <div class="alert alert-success mb-2">Cobrança gerada com sucesso!</div>
          <a href="{{faturaAdesao.url}}"
             class="btn btn-outline-blue"
             target="_blank">
            Acessar link pagamento
          </a>
        </div>

      </fieldset>

      <fieldset *ngIf="gerarAssinatura" >
        <legend>Dados do contrato</legend>

        <div class="row mb-2">
          <div class="form-group col-8">
            <label for="plano">Plano:</label>

            <kendo-dropdownlist id="plano" name="plano" [(ngModel)]="contrato.plano" [data]="planos"
                                [filterable]="true"     [kendoDropDownFilter]="filterSettings"
                                placeholder="Selecione um plano" class="form-control" textField="nome"
                                required  (ngModelChange)="selecionouPlano()">

              <ng-template kendoDropDownListItemTemplate let-dataItem>
                <span class="template">{{ dataItem.nome }}</span> -  {{ dataItem.valor | currency: "BRL" }}
              </ng-template>
            </kendo-dropdownlist>

            <div class="invalid-feedback" >
              Plano é obrigatório
            </div>

            <label class="text-muted k-text-info mt-1" *ngIf="contrato.plano.id">
              * Valor {{contrato.plano.periodoTexto}} do plano: <b>{{(contrato.plano.valor) | currency:"BRL"}}</b>
            </label>

          </div>

          <div class="form-group col-4">
            <label for="plano">Formas de pagamento:</label>

            <kendo-multiselect  name="novaFormaPagamento" [(ngModel)]="contrato.formaDePagamento" [data]="formasDePagamento" required
                                placeholder="Selecione as forma de pagamento" class="form-control"
                                required (ngModelChange)="alterouFormasPagamento()" >
            </kendo-multiselect>

            <div class="invalid-feedback" >
              Informe pelo menos uma forma de pagamento
            </div>

            <label class="text-muted k-text-info mt-1" *ngIf="contrato.plano.descontoCartao && !contrato.valorNegociado">
              *<b>{{contrato.plano.descontoCartao | currency: "BRL"}} desconto</b>   no cartão de crédito
            </label>

          </div>
        </div>

        <div class="row mb-2">

          <div class=" form-group col-4" >
            <label  >Dia do vencimento</label>
            <kendo-dropdownlist name="diaVencimento"   [(ngModel)]="contrato.diaVencimento" [data]="diasVencimento" required
                                placeholder="Selecione um dia" class="form-control"  >
            </kendo-dropdownlist>
            <div class="invalid-feedback" >
              Informe um dia do vencimento das faturas
            </div>

          </div>

          <div class="form-group  col-4">
            <label for="limiteContatosNegociado">Contatos negociados</label>
            <kendo-numerictextbox id="limiteContatosNegociado" [min]="0" class="" name="limiteContatosNegociado"   [format]="'n2'"  class="form-control"
                                  [(ngModel)]="contrato.limiteContatosNegociado">
            </kendo-numerictextbox>

          </div>

        </div>

        <div class="row mb-2" *ngIf="pagarNoCartao()">

          <div class="form-group col-4"  *ngIf="contrato.parcelar && false">
            <label for="numeroParcelas">Número de parcelas</label>
            <kendo-numerictextbox id="numeroParcelas" [min]="1" [max]="12" class="" name="numeroParcelas" [disabled]   [format]="'n0'"  class="form-control"
                                  [(ngModel)]="contrato.numeroParcelas" [required]="contrato.parcelar">
            </kendo-numerictextbox>

            <div class="invalid-feedback" >
              Informe o número de parcelas (1 até 12x sem juros)
            </div>
          </div>
        </div>

        <app-grid-fatura *ngIf="contrato.plano?.id" #gridFatura [contrato]="contrato"></app-grid-fatura>

        <div class="mt-4" *ngIf="!assinatura.id">
          <button type="submit" class="btn btn-primary waves-effect waves-light" [disabled]="salvando"  >
            <i class="k-i-loading k-icon" *ngIf="salvando"></i>
            Gerar link pagamento</button>
          <button type="button" class="btn btn-light waves-effect ml-2" data-dismiss="modal" (click)="cancele()">Cancelar</button>
        </div>

        <div class="mt-4" *ngIf="assinatura.codigo">
          <div class="alert alert-success mb-2">Assinatura gerada com sucesso!</div>
          <a href="https://{{empresa.dominio}}.promokit.com.br/admin/assinatura/pagamento/{{assinatura.codigo}}"
             class="btn btn-outline-blue"
             target="_blank">
            Acessar link pagamento assinatura
          </a>
        </div>

      </fieldset>
    </div>

    <div class="alert alert-danger mt-2" role="alert" *ngIf="empresaJaTemContrato">
      Empresa já tem contrato ativo.
      <button type="button" class="btn btn-danger ml-2" (click)="gerarNovoContrato()">Gerar um novo contrato</button>
      <button type="button" class="close" data-dismiss="alert" aria-label="Fechar">
        <span aria-hidden="true">&times;</span>
      </button>
    </div>

    <div class="alert alert-danger mt-2" role="alert" *ngIf="mensagemErro">
      <i class="mdi mdi-check-all mr-2"></i> {{mensagemErro}}
      <button type="button" class="close" data-dismiss="alert" aria-label="Fechar">
        <span aria-hidden="true">&times;</span>
      </button>
    </div>
  </form>
</div>
