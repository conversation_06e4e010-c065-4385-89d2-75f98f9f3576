import {Component, Input, OnInit} from "@angular/core";
import {ModulosService} from "../../services/modulos.service";

interface SubscriptionItem {
  $id: any;
  quantidade: number;
  descricao: string;
  valor: number;
  total: number;
  recorrente: boolean;
  adesao: boolean;
  tipo?: 'adesao' | 'modulo' | 'modulo-taxa' | 'desconto' | 'livre';
  moduloId?: number;
  taxaAtivacao?: boolean;
}

@Component({
  selector: 'app-grid-fatura',
  templateUrl: './grid-fatura.component.html',
  styleUrls: ['./grid-fatura.component.scss']
})
export class GridFaturaComponent implements OnInit {
  public items: SubscriptionItem[] = [];
  taxaAdesaoPadrao = 200;
  @Input() contrato: any = {}
  fatura: any = { taxaAdesao: this.taxaAdesaoPadrao};
  modulos: any[] = [];

  constructor(private modulosService: ModulosService) {
  }

  ngOnInit() {
    this.carregueModulos();
  }

  async carregueModulos() {
    try {
      this.modulos = await this.modulosService.listeModulos({ pagos: true});
    } catch (error) {
      console.error('Erro ao carregar módulos:', error);
      this.modulos = [];
    }
  }

  addItem() {
    const novoItem: SubscriptionItem = {
      $id: Date.now(),
      quantidade: 1,
      descricao: '',
      valor: 0,
      total: 0,
      recorrente: false,
      adesao: false,
      tipo: undefined // Sem tipo inicialmente para forçar seleção
    };
    this.items.push(novoItem);
  }

  // Verifica se já existe uma taxa de adesão
  jaTemAdesao(): boolean {
    return this.items.some(item => item.tipo === 'adesao');
  }

  selecioneTipo(item: SubscriptionItem, tipo: 'adesao' | 'modulo' | 'desconto' | 'livre') {
    item.tipo = tipo;

    if (tipo === 'adesao') {
      // Adesão: não pode mexer na qtde, descrição nem marcar como recorrente
      item.descricao = 'Taxa de adesão';
      item.valor = this.taxaAdesaoPadrao;
      item.quantidade = 1;
      item.recorrente = false;
      item.adesao = true;
      this.updateTotal(item);
    } else if (tipo === 'desconto') {
      // Desconto: sempre valor negativo
      item.descricao = '';
      item.valor = 0;
      item.quantidade = 1;
      item.recorrente = true;
    } else if (tipo === 'modulo') {
      // Módulo: quantidade vem do plano, dropdown para seleção
      item.quantidade = this.contrato.plano?.intervalo || 1;
      item.recorrente = true;
      item.valor = 0;
      item.descricao = '';
    } else if (tipo === 'livre') {
      // Livre: sem restrições
      item.descricao = '';
      item.valor = 0;
      item.quantidade = 1;
      item.recorrente = false;
    }

    this.updateTotal(item);
  }

  selecioneModulo(item: SubscriptionItem, modulo: any) {
    // Remove taxa de ativação do módulo anterior (se existir)
    if (item.moduloId) {
      const indexTaxaAnterior = this.items.findIndex(i =>
        i.moduloId === item.moduloId && i.tipo === 'modulo-taxa'
      );
      if (indexTaxaAnterior !== -1) {
        this.items.splice(indexTaxaAnterior, 1);
      }
    }

    if (modulo) {
      item.moduloId = modulo.id;
      item.descricao = `Módulo ${modulo.nome}`;
      item.valor = modulo.valorMensalidade;
      item.quantidade = this.contrato.plano?.intervalo || 1;

      this.updateTotal(item);

      // Adiciona item de taxa de ativação (se tiver valor de ativação)
      if (modulo.valorAtivacao && modulo.valorAtivacao > 0) {
        const itemTaxaAtivacao: SubscriptionItem = {
          $id: this.generateId(),
          quantidade: 1, // Sempre 1 para taxa de ativação
          descricao: `Taxa de Ativação ${modulo.nome}`,
          valor: modulo.valorAtivacao,
          total: modulo.valorAtivacao,
          recorrente: false, // Taxa de ativação não é recorrente
          adesao: false,
          tipo: 'modulo-taxa',
          moduloId: modulo.id,
          taxaAtivacao: true
        };

        this.updateTotal(itemTaxaAtivacao);
        this.items.push(itemTaxaAtivacao);
      }
    }
  }

  updateTotal(item: SubscriptionItem) {
    if (item.tipo === 'desconto') {
      // Garante que desconto seja sempre negativo
      item.valor = Math.abs(item.valor) * -1;
    }
    item.total = item.quantidade * item.valor;
  }

  removeItem(index: number) {
    const item = this.items[index];

    // Não permite remover itens do tipo modulo-taxa
    if (item.tipo === 'modulo-taxa') {
      alert('Taxa de ativação não pode ser removida. Remova o módulo principal.');
      return;
    }

    // Se remover um módulo (mensalidade), remove também a taxa de ativação correspondente
    if (item.tipo === 'modulo' && !item.taxaAtivacao && item.moduloId) {
      const indexTaxaAtivacao = this.items.findIndex(i =>
        i.moduloId === item.moduloId && i.tipo === 'modulo-taxa'
      );

      if (indexTaxaAtivacao !== -1) {
        // Remove primeiro o índice maior para não afetar o menor
        if (indexTaxaAtivacao > index) {
          this.items.splice(indexTaxaAtivacao, 1);
          this.items.splice(index, 1);
        } else {
          this.items.splice(index, 1);
          this.items.splice(indexTaxaAtivacao, 1);
        }
      } else {
        this.items.splice(index, 1);
      }
    } else {
      this.items.splice(index, 1);
    }
  }

  // Verifica se algum item ainda não tem tipo selecionado
  temItemSemTipo(): boolean {
    return this.items.some(item => !item.tipo);
  }

  // Verifica se o item pode ser removido
  podeRemoverItem(item: SubscriptionItem): boolean {
    return item.tipo !== 'modulo-taxa';
  }

  obtenhaValorPrimeiroPagamento() {
    return this.items.reduce((total, item) => {
      if (!item.recorrente) {
        return total + item.total;
      }
      return total + (this.contrato.plano?.valor || 0);
    }, 0);
  }

  obtenhaValorPagamentosRecorrentes() {
    return this.items.reduce((total, item) => {
      if (item.recorrente) {
        return total + item.total;
      }
      return total;
    }, 0) + (this.contrato.plano?.valor || 0);
  }

  selecinouNovoPlano() {
    this.contrato.taxaAdesao = this.taxaAdesaoPadrao;
    this.items = [];

    if(this.contrato.plano){
      this.contrato.limiteContatosNegociado = this.contrato.plano.limiteContatos
      this.addItemTaxaPadraoAdesao();
      this.addItemDesconto();
    }
  }

  addItemTaxaPadraoAdesao() {
    this.items.push({
      $id: Date.now(),
      quantidade: 1,
      descricao: 'Taxa de adesão',
      valor: this.taxaAdesaoPadrao,
      total: this.taxaAdesaoPadrao,
      recorrente: false,
      adesao: true,
      tipo: 'adesao'
    });
  }

  addItemDesconto() {
    let valorDescontoPadrao = this.contrato.plano.descontoPadrao || 0;

    if(valorDescontoPadrao){
      let descricao = `Desconto ${this.contrato.plano.periodoTexto || ''}`.trim()

      this.items.push({
        $id: this.generateId(),
        quantidade: 1,
        descricao: descricao,
        valor: -valorDescontoPadrao,
        total: -valorDescontoPadrao,
        recorrente: true,
        adesao: false,
        tipo: 'desconto'
      });
    }
  }

  obtenhaValorNegociado() {
    let valorPlano = this.contrato.plano.valor;

    let totalDescontos =
      this.items.filter((item: any) => item.recorrente && item.total < 0).reduce((soma: number, item: any) => soma + item.total, 0)

    return valorPlano + totalDescontos;
  }

  // Gera um ID único para cada item
  private generateId(): any {
    return Math.random().toString(36).substr(2, 9);
  }

  podeAlterarComoRecorrente(dataItem: SubscriptionItem){
    return dataItem.tipo === 'adesao' || dataItem.taxaAtivacao || dataItem.tipo === 'modulo' || dataItem.tipo === 'modulo-taxa';
  }
}
