.grid-fatura {
  margin-bottom: 1rem;
}

.valorDestaque {
  color: #007bff;
  font-size: 1.1em;
}

.btn-group-vertical {
  display: flex;
  flex-direction: column;
  width: 100%;

  .btn-tipo {
    margin-bottom: 0.5rem;
    width: 100%;
    font-size: 0.875rem;
    padding: 0.5rem;
    border-radius: 0.375rem;
    font-weight: 500;
    transition: all 0.2s ease-in-out;

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    }

    &:active {
      transform: translateY(0);
    }

    &:last-child {
      margin-bottom: 0;
    }

    i {
      font-size: 0.8rem;
    }
  }
}

.form-group {
  margin-bottom: 0;
}

.k-grid {
  .k-grid-header {
    th {
      background-color: #f8f9fa;
      font-weight: 600;
    }
  }

  .k-grid-content {
    td {
      vertical-align: middle;
      padding: 0.5rem;
    }
  }
}

// Badges para tipos
.badge {
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;

  &.badge-success {
    background-color: #28a745;
    color: white;
  }

  &.badge-warning {
    background-color: #ffc107;
    color: #212529;
  }

  &.badge-danger {
    background-color: #dc3545;
    color: white;
  }

  &.badge-info {
    background-color: #17a2b8;
    color: white;
  }

  &.badge-secondary {
    background-color: #6c757d;
    color: white;
  }
}

// Estilos para botões de tipo
.btn-tipo {
  &.btn-warning {
    background-color: #ffc107;
    border-color: #ffc107;
    color: #212529;

    &:hover {
      background-color: #e0a800;
      border-color: #d39e00;
    }
  }

  &.btn-success {
    background-color: #28a745;
    border-color: #28a745;

    &:hover {
      background-color: #218838;
      border-color: #1e7e34;
    }
  }

  &.btn-danger {
    background-color: #dc3545;
    border-color: #dc3545;

    &:hover {
      background-color: #c82333;
      border-color: #bd2130;
    }
  }

  &.btn-info {
    background-color: #17a2b8;
    border-color: #17a2b8;

    &:hover {
      background-color: #138496;
      border-color: #117a8b;
    }
  }
}

// Estilos para diferentes tipos de linha
.k-grid tr {
  &[data-tipo="desconto"] {
    background-color: #f8d7da;
  }

  &[data-tipo="adesao"] {
    background-color: #fff3cd;
  }

  &[data-tipo="modulo"] {
    background-color: #d4edda;
  }

  &[data-tipo="modulo-taxa"] {
    background-color: #e2e3e5;
    border-left: 3px solid #6c757d;
  }
}

// Tooltips de ajuda
.help-tooltip {
  color: #6c757d;
  cursor: help;

  &:hover {
    color: #007bff;
  }
}

// Campos desabilitados
.k-input[disabled],
.k-numerictextbox[disabled] .k-input {
  background-color: #e9ecef;
  cursor: not-allowed;
  opacity: 0.6;
}

// Alert customizado
.alert {
  border-radius: 0.5rem;

  ul {
    padding-left: 1.5rem;
  }

  li {
    margin-bottom: 0.25rem;
  }
}

// Ajustes responsivos
@media (max-width: 768px) {
  .k-grid {
    font-size: 0.875rem;

    .k-grid-content td {
      padding: 0.25rem;
    }
  }

  .btn-group-vertical .btn-tipo {
    font-size: 0.75rem;
    padding: 0.375rem;
  }
}
