<div class="kendo-grid-container" *ngIf="contrato.plano?.id">
  <h5><PERSON><PERSON><PERSON> módu<PERSON>, descontos ou outros itens na assinatura
    <button class="btn btn-blue btn-xs" type="button" (click)="addItem()">Adicionar Item</button>
  </h5>
  <kendo-grid [data]="items" class="grid-fatura">
    <kendo-grid-column field="tipo" title="Tipo" [width]="80">
      <ng-template kendoGridCellTemplate let-dataItem>
        <!-- Seleção de tipo quando ainda não foi selecionado -->
        <div *ngIf="!dataItem.tipo" class="btn-group-vertical">
          <!-- Bot<PERSON> Adesão - só aparece se não existir adesão -->
          <button *ngIf="!jaTemAdesao()"   type="button"
                  class="btn btn-warning mb-1 btn-tipo"
                  (click)="selecioneTipo(dataItem, 'adesao')"
                  kendoTooltip title="Taxa de adesão (não editável)">
            <i class="fa fa-star mr-1"></i>Adesão
          </button>

          <button class="btn btn-success mb-1 btn-tipo"  type="button"
                  (click)="selecioneTipo(dataItem, 'modulo')"
                  kendoTooltip title="Módulo com mensalidade e ativação">
            <i class="fa fa-puzzle-piece mr-1"></i>Módulo
          </button>

          <button class="btn btn-danger mb-1 btn-tipo"  type="button"
                  (click)="selecioneTipo(dataItem, 'desconto')"
                  kendoTooltip title="Desconto (sempre valor negativo)">
            <i class="fa fa-percent mr-1"></i>Desconto
          </button>

          <button class="btn btn-info btn-tipo"  type="button"
                  (click)="selecioneTipo(dataItem, 'livre')"
                  kendoTooltip title="Item livre (editável)">
            <i class="fa fa-edit mr-1"></i>Livre
          </button>
        </div>
        <!-- Exibe o tipo selecionado -->
        <div *ngIf="dataItem.tipo">
          <span [ngClass]="{
            'badge-success': dataItem.tipo === 'modulo',
            'badge-warning': dataItem.tipo === 'adesao',
            'badge-danger': dataItem.tipo === 'desconto',
            'badge-info': dataItem.tipo === 'livre',
            'badge-secondary': dataItem.tipo === 'modulo-taxa'
          }" class="badge">
            <span *ngIf="dataItem.tipo === 'modulo-taxa'">Taxa</span>
            <span *ngIf="dataItem.tipo !== 'modulo-taxa'">{{dataItem.tipo | titlecase}}</span>
          </span>
          <small *ngIf="dataItem.tipo === 'modulo-taxa'" class="text-muted d-block">Ativação</small>
        </div>
      </ng-template>
    </kendo-grid-column>

    <!-- Colunas só aparecem quando há pelo menos um item com tipo selecionado -->
    <ng-container *ngIf="!temItemSemTipo()">
      <kendo-grid-column field="recorrente" title="Recorrente" [width]="100">
        <ng-template kendoGridHeaderTemplate>
          <i class="fa fa-question-circle help-tooltip ml-1"
             kendoTooltip
             title="Se marcado, o valor será cobrado mensalmente. Taxa de adesão, ativação e modulo-taxa não são recorrentes."></i>
          Recorrente
        </ng-template>
        <ng-template kendoGridCellTemplate let-dataItem>
          <div *ngIf="dataItem.tipo">
            <input type="checkbox" style="height: 20px;width: 20px;"
                   [(ngModel)]="dataItem.recorrente"
                   [disabled]="podeAlterarComoRecorrente(dataItem)"
                   name="recorrente{{dataItem.$id}}" />
          </div>
        </ng-template>
      </kendo-grid-column>

      <kendo-grid-column field="descricao" title="Descrição" [width]="200">
        <ng-template kendoGridHeaderTemplate>
          Descrição
          <i class="fa fa-question-circle help-tooltip ml-1"
             kendoTooltip
             title="Descrição do item. Para módulos, selecione da lista. Para adesão e taxa de ativação é fixa."></i>
        </ng-template>
        <ng-template kendoGridCellTemplate let-dataItem>
          <div *ngIf="dataItem.tipo" class="form-group mb-0">
            <!-- Dropdown para seleção de módulo -->
            <kendo-dropdownlist *ngIf="dataItem.tipo === 'modulo'"
              [data]="modulos" required
              [textField]="'nome'"
              [valueField]="'id'"
              [value]="dataItem.moduloId"
              (valueChange)="selecioneModulo(dataItem, $event)"
              placeholder="Selecione o módulo"
              class="form-control">
            </kendo-dropdownlist>

            <!-- Campo texto (editável ou não dependendo do tipo) -->
            <input *ngIf="dataItem.tipo !== 'modulo'  "
                   kendoTextBox
                   [disabled]="dataItem.tipo === 'adesao' || dataItem.tipo === 'modulo-taxa'"
                   name="descricao{{dataItem.$id}}"
                   placeholder="Digite a descrição"
                   [(ngModel)]="dataItem.descricao"
                   class="k-input form-control"
                   required />
          </div>
        </ng-template>
      </kendo-grid-column>

      <kendo-grid-column field="quantidade" title="Qtde" [width]="75">
        <ng-template kendoGridHeaderTemplate>
          Qtde
          <i class="fa fa-question-circle help-tooltip ml-1"
             kendoTooltip
             title="Quantidade de itens. Para adesão, ativação e modulo-taxa é sempre 1. Para módulos, vem do plano."></i>
        </ng-template>
        <ng-template kendoGridCellTemplate let-dataItem>
          <div *ngIf="dataItem.tipo" class="form-group mb-0">
            <kendo-numerictextbox [min]="1"
                                  [disabled]="dataItem.tipo === 'adesao' || dataItem.tipo === 'modulo-taxa' ||  dataItem.tipo === 'modulo'"
                                  format="n0"
                                  class="k-input form-control"
                                  name="quantidade{{dataItem.$id}}"
                                  [(ngModel)]="dataItem.quantidade"
                                  (valueChange)="updateTotal(dataItem)"
                                  required>
            </kendo-numerictextbox>
          </div>
        </ng-template>
      </kendo-grid-column>

      <kendo-grid-column field="valor" title="Valor" [width]="120">
        <ng-template kendoGridHeaderTemplate>
          Valor Unitário
          <i class="fa fa-question-circle help-tooltip ml-1"
             kendoTooltip
             title="Valor por unidade. Descontos são sempre negativos. Adesão, ativação e modulo-taxa têm valor fixo."></i>
        </ng-template>
        <ng-template kendoGridCellTemplate let-dataItem>
          <div *ngIf="dataItem.tipo" class="form-group mb-0">
            <kendo-numerictextbox class="form-control"
                                 name="valor{{dataItem.$id}}"
                                 (valueChange)="updateTotal(dataItem)"
                                 [format]="'c'"
                                 [(ngModel)]="dataItem.valor"
                                 required>
            </kendo-numerictextbox>
          </div>
        </ng-template>
      </kendo-grid-column>

      <kendo-grid-column field="total" title="Total" [width]="90">
        <ng-template kendoGridHeaderTemplate>
          Total
          <i class="fa fa-question-circle help-tooltip ml-1"
             kendoTooltip
             title="Quantidade × Valor Unitário"></i>
        </ng-template>
        <ng-template kendoGridCellTemplate let-dataItem>
          <div *ngIf="dataItem.tipo">
            <span [ngClass]="{
              'text-success': dataItem.total > 0 && dataItem.tipo !== 'desconto',
              'text-danger': dataItem.total < 0 || dataItem.tipo === 'desconto',
              'text-warning': dataItem.tipo === 'adesao',
              'text-info': dataItem.tipo === 'modulo-taxa'
            }">
              {{ dataItem.total | currency: "BRL" }}
            </span>
          </div>
        </ng-template>
      </kendo-grid-column>

      <kendo-grid-column title="Ações" [width]="80">
        <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
          <div *ngIf="dataItem.tipo">
            <button *ngIf="podeRemoverItem(dataItem)"
                    class="k-button k-button-icon"
                    kendoTooltip
                    title="Remover item"
                    (click)="removeItem(rowIndex)">
              <i class="fa fa-trash text-danger"></i>
            </button>
            <span *ngIf="!podeRemoverItem(dataItem)"
                  class="text-muted"
                  kendoTooltip
                  title="Taxa de ativação não pode ser removida">
              <i class="fa fa-lock"></i>
            </span>
          </div>
        </ng-template>
      </kendo-grid-column>
    </ng-container>
  </kendo-grid>

  <div class="alert alert-info mt-3">
    <h6><i class="fa fa-info-circle"></i> Informações importantes:</h6>
    <ul class="mb-0">
      <li><strong>Taxa de Adesão:</strong> Valor fixo cobrado apenas no primeiro pagamento</li>
      <li><strong>Desconto:</strong> Sempre com valor negativo, reduz o valor total</li>
      <li><strong>Módulo:</strong> Pode gerar cobrança de mensalidade (recorrente) e taxa de ativação (única vez)</li>
      <li><strong>Taxa de Ativação:</strong> Gerada automaticamente quando um módulo tem taxa de ativação - não pode ser removida separadamente</li>
      <li><strong>Recorrente:</strong> Itens marcados serão cobrados mensalmente</li>
    </ul>
  </div>
</div>

<div class="row">
  <div class="mb-2 col">
    <h5 class="mb-1">Valor 1° Pagamento:
      <span class="valorDestaque">
        <b>{{obtenhaValorPrimeiroPagamento() | currency: "BRL"}}</b>
      </span>
    </h5>
    <h5 class="mb-1">Valor a partir 2° Pagamento:
      <span class="valorDestaque">
        <b>{{obtenhaValorPagamentosRecorrentes() | currency: "BRL"}}</b>
      </span>
    </h5>
    <h5 *ngIf="this.contrato.limiteContatosNegociado">
      Limite contatos: <b>{{this.contrato.limiteContatosNegociado}}</b>
    </h5>
  </div>
</div>
