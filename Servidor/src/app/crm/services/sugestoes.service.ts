import { Injectable } from '@angular/core';
import { Observable, of, throwError } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { catchError, map } from 'rxjs/operators';
import { ContextoConversa } from './conversas.service';

export interface SugestaoMensagem {
  texto: string;
  confianca?: number;
  palavraChave?: string;
}

@Injectable({
  providedIn: 'root'
})
export class SugestoesService {
  // Mapa de palavras-chave para sugestões de mensagens
  private mapaPalavrasChave: Record<string, string[]> = {
    'preço': [
      'Nossos planos começam a partir de R$99/mês. Posso explicar os detalhes de cada um?',
      'Temos condições especiais neste mês. Posso enviar uma proposta personalizada?'
    ],
    'demo': [
      'Ótimo! Posso agendar uma demonstração para amanhã às 14h?',
      'Podemos fazer uma demonstração hoje mesmo. Qual horário seria melhor para você?'
    ],
    'dúvida': [
      'Entendo sua dúvida. Nosso sistema resolve isso de forma simples, permitindo que você...',
      'É uma dúvida comum. Vou te explicar como funciona:'
    ],
    'concorrente': [
      'Diferente da concorrência, nosso sistema oferece integração completa com WhatsApp e mais recursos de automação.',
      'Nosso diferencial é a facilidade de uso e suporte 24/7, algo que a concorrência não oferece.'
    ],
    'interessado': [
      'Que ótimo! Posso lhe mostrar uma demonstração agora mesmo?',
      'Excelente! Vamos agendar uma reunião para mostrar como podemos atender suas necessidades?'
    ],
    'valor': [
      'O investimento é bem acessível comparado ao retorno que você terá. Posso detalhar melhor?',
      'Nosso sistema se paga em poucos meses pelo aumento de conversão que proporciona.'
    ],
    'problema': [
      'Entendo o desafio que você está enfrentando. Nossa solução foi desenvolvida especificamente para resolver isso.',
      'Muitos clientes tinham esse mesmo problema e conseguiram resultados incríveis com nossa plataforma.'
    ],
    'prazo': [
      'A implementação é rápida, em média 3 dias úteis para ter tudo funcionando.',
      'Podemos iniciar imediatamente após a contratação. Todo o processo leva menos de uma semana.'
    ]
  };

  // Sugestões por etapa do funil
  private sugestoesPorEtapa: Record<string, Record<string, string[]>> = {
    'Prospecção': {
      'Consultivo': [
        'Oi Ricardo! Que legal conhecer você! Vi que tem uma pizzaria. Quantas mesas vocês atendem por dia? Nosso cardápio digital pode multiplicar isso!',
        'Olá! Adoro pizzarias familiares. Vocês já pensaram em como seria se cada mesa pudesse fazer pedidos sem chamar o garçom? Parece ficção científica, né?',
        'Ricardo, parabéns pela pizzaria! Uma pergunta: seus clientes reclamam da demora no atendimento nos fins de semana?'
      ],
      'Empático': [
        'Olá Ricardo! Como está o movimento na pizzaria? Sei que gerenciar o fluxo de clientes pode ser desafiador, especialmente em horários de pico!',
        'Oi! Adoro pizza! Imagino que o maior desafio seja manter a qualidade do atendimento quando a casa está cheia, né?',
        'Olá! Sou apaixonado por negócios familiares como pizzarias. Conte um pouco sobre como começou sua jornada!'
      ]
    },
    'Qualificação': {
      'Consultivo': [
        'Entendi suas necessidades! Nossa solução permite que os clientes façam pedidos diretamente pelo celular escaneando um QR code na mesa. Isso reduz erros em 70% e aumenta a rotatividade das mesas.',
        'Baseado no seu volume de 30 mesas, nossa plataforma pode aumentar sua eficiência em até 40%. O melhor é que você paga apenas uma pequena taxa mensal, sem investimento inicial.',
        'Nossos clientes relatam um aumento médio de 22% no valor do ticket quando usam nosso cardápio digital. Os adicionais são muito mais fáceis de vender!'
      ],
      'Técnico': [
        'Nossa plataforma integra com 95% dos sistemas de PDV do mercado, incluindo o que você usa. A implementação leva apenas 24 horas e não precisa de nenhum hardware adicional.',
        'A sincronização com seu estoque é em tempo real. Quando um item acaba, ele é automaticamente removido do cardápio, evitando frustração dos clientes.',
        'Nossas APIs são abertas e documentadas, permitindo integração com qualquer sistema de gestão ou contabilidade que você já use.'
      ]
    },
    'Objeção': {
      'Consultivo': [
        'Entendo sua preocupação com o custo. A maioria dos nossos clientes recupera o investimento em menos de 45 dias. Uma pizzaria do seu porte economiza em média R$3.200/mês só com redução de erros e otimização da equipe.',
        'O plano mais básico custa menos que um delivery por dia. E você pode fazer um teste gratuito por 30 dias para comprovar o retorno antes de decidir.',
        'Comparado ao custo de contratar mais um garçom (salário + encargos + benefícios), nossa solução é 80% mais econômica e funciona 24/7 sem reclamar!'
      ],
      'Técnico': [
        'Nossa plataforma não exige compra de tablets ou hardware. Os clientes usam o próprio celular, e oferecemos opções flexíveis de pagamento, incluindo mensalidade sem contrato de fidelidade.',
        'Você sabia que restaurantes com sistemas de autoatendimento reduzem o custo operacional em até 30%? Nossos relatórios detalhados permitem identificar todas as economias geradas.',
        'Nossa API processa mais de 500 mil pedidos por dia com 99.99% de uptime garantido em contrato. O suporte técnico está disponível 24/7 sem custo adicional.'
      ]
    },
    'Fechamento': {
      'Consultivo': [
        'Excelente! Vamos formalizar? Posso enviar o contrato hoje mesmo e agendar a implementação para a próxima semana. Assim você já começa a ver resultados antes do fim do mês.',
        'Com base na nossa conversa, recomendo o plano Profissional. Ele inclui todas as funcionalidades que você precisa agora, e você pode fazer upgrade quando sua operação crescer.',
        'Para começarmos, preciso apenas que você preencha este formulário com os dados básicos do restaurante. Nossa equipe fará todo o resto!'
      ],
      'Urgente': [
        'Temos uma promoção especial que termina hoje: 30% de desconto nos 3 primeiros meses. Posso garantir esse valor se fecharmos agora.',
        'Consigo antecipar sua implementação para esta semana se confirmarmos hoje. Assim você já aproveita o movimento do final de semana com o sistema rodando!',
        'Acabei de confirmar com nosso time de implementação que podemos fazer a configuração expressa em 24h se assinarmos hoje. O que acha?'
      ]
    }
  };

  // Sugestões genéricas para quando não há contexto específico
  private sugestoesGenericas: string[] = [
    'Olá! Como posso ajudar você hoje?',
    'Temos várias soluções que podem atender suas necessidades. Podemos conversar mais sobre seu negócio?',
    'Gostaria de agendar uma demonstração para conhecer melhor nossa plataforma?',
    'Estou à disposição para esclarecer qualquer dúvida sobre nossos serviços.',
    'Nossos clientes têm obtido resultados excelentes com nossa solução. Posso compartilhar alguns casos de sucesso?'
  ];

  constructor(private http: HttpClient) { }

  /**
   * Gera sugestões com base no contexto da conversa e parâmetros de configuração
   */
  gerarSugestoes(contexto: ContextoConversa): Observable<SugestaoMensagem[]> {
    console.log('SugestoesService - gerarSugestoes, contexto:', contexto);

    try {
      const sugestoes: SugestaoMensagem[] = [];
      const etapa = contexto.etapaFunil || 'Prospecção';
      const tom = contexto.tomConversa || 'Consultivo';

      // Verifica se temos sugestões específicas para esta combinação de etapa e tom
      if (this.sugestoesPorEtapa[etapa] && this.sugestoesPorEtapa[etapa][tom]) {
        this.sugestoesPorEtapa[etapa][tom].forEach(texto => {
          sugestoes.push({ texto });
        });
      }

      // Se não encontrou sugestões específicas para o tom, tenta usar qualquer tom disponível
      if (sugestoes.length === 0 && this.sugestoesPorEtapa[etapa]) {
        const tomsDisponiveis = Object.keys(this.sugestoesPorEtapa[etapa]);
        if (tomsDisponiveis.length > 0) {
          const tomAlternativo = tomsDisponiveis[0];
          this.sugestoesPorEtapa[etapa][tomAlternativo].forEach(texto => {
            sugestoes.push({ texto });
          });
        }
      }

      // Se ainda não tem sugestões, verifica palavras-chave nas mensagens
      if (sugestoes.length === 0 && contexto.mensagens && contexto.mensagens.length > 0) {
        const ultimasMensagens = contexto.mensagens
          .slice(-3)
          .map(msg => msg.texto.toLowerCase());

        for (const [palavraChave, respostas] of Object.entries(this.mapaPalavrasChave)) {
          for (const mensagem of ultimasMensagens) {
            if (mensagem.includes(palavraChave)) {
              respostas.forEach(resposta => {
                sugestoes.push({
                  texto: resposta,
                  palavraChave
                });
              });
              break;
            }
          }
        }
      }

      // Se ainda não tem sugestões, usa genéricas
      if (sugestoes.length === 0) {
        this.sugestoesGenericas.forEach(texto => {
          sugestoes.push({ texto });
        });
      }

      // Limita a 3 sugestões e remove duplicatas
      const sugestoesFiltradas = this.removerDuplicatas(sugestoes).slice(0, 3);
      console.log('Sugestões geradas:', sugestoesFiltradas);

      return of(sugestoesFiltradas);
    } catch (error) {
      console.error('Erro ao gerar sugestões:', error);
      // Em caso de erro, retorna algumas sugestões padrão
      return of([
        { texto: 'Olá! Como posso ajudar?' },
        { texto: 'Gostaria de saber mais sobre nossos produtos?' },
        { texto: 'Podemos agendar uma demonstração se preferir.' }
      ]);
    }
  }

  /**
   * Remove sugestões duplicadas com base no texto
   */
  private removerDuplicatas(sugestoes: SugestaoMensagem[]): SugestaoMensagem[] {
    const textosUnicos = new Set<string>();
    return sugestoes.filter(sugestao => {
      if (textosUnicos.has(sugestao.texto)) {
        return false;
      }
      textosUnicos.add(sugestao.texto);
      return true;
    });
  }

  /**
   * Método para gerar sugestões usando IA via API
   */
  gerarSugestoesComIA(contexto: ContextoConversa): Observable<SugestaoMensagem[]> {
    const payload = {
      telefone: contexto.telefone || '',
      mensagens: contexto.mensagens || [],
      faseSpin: contexto.etapaFunil === 'auto' ? 'auto' : this.mapearEtapaParaFaseSpin(contexto.etapaFunil),
      produto: contexto.produto || 'Meu Cardápio',
      tomConversa: this.mapearTomConversa(contexto.tomConversa)
    };

    console.log('[SugestoesService] Enviando request para API:', payload);
    return this.http.post<any>('/api/whatsapp/sugestao-resposta', payload)
      .pipe(
        map(resposta => {
          console.log('[SugestoesService] Resposta da API:', resposta);
          
          // Novo formato com múltiplas sugestões e fase detectada
          if (resposta.sucesso && resposta.data) {
            // Retornar o objeto completo se tiver fase sugerida ou observações
            if (resposta.data.faseSugerida || resposta.data.observacoes) {
              return {
                sugestoes: resposta.data.sugestoes.map((sug: any) => ({
                  texto: sug.texto,
                  confianca: sug.confianca,
                  faseSpin: sug.faseSpin
                })),
                faseSugerida: resposta.data.faseSugerida,
                observacoes: resposta.data.observacoes
              };
            }
            
            // Se não tiver fase sugerida, retornar apenas array (compatibilidade)
            if (resposta.data.sugestoes) {
              return resposta.data.sugestoes.map((sug: any) => ({
                texto: sug.texto,
                confianca: sug.confianca,
                faseSpin: sug.faseSpin
              }));
            }
          }
          
          // Compatibilidade com formato antigo
          if (resposta.sucesso && resposta.data?.sugestao) {
            return [{
              texto: resposta.data.sugestao.texto,
              confianca: resposta.data.sugestao.confianca
            }];
          }
          
          // Retornar array vazio se não conseguiu gerar sugestão
          console.error('[SugestoesService] API não retornou sugestão válida');
          return [];
        }),
        catchError(erro => {
          console.error('Erro ao gerar sugestões com IA:', erro);
          // Em caso de erro, propagar o erro
          throw erro;
        })
      );
  }

  /**
   * Detecta a fase SPIN automaticamente baseado nas mensagens
   */
  detectarFaseSpin(mensagens: any[]): Observable<string> {
    return this.http.post<any>('/api/whatsapp/detectar-fase-spin', { mensagens })
      .pipe(
        map(resposta => {
          if (resposta.sucesso && resposta.data?.faseSpin) {
            return resposta.data.faseSpin;
          }
          return 'situacao'; // Default
        }),
        catchError(erro => {
          console.error('Erro ao detectar fase SPIN:', erro);
          return of('situacao');
        })
      );
  }

  /**
   * Detecta e salva a fase SPIN no lead
   */
  detectarESalvarFaseSpin(telefone: string, mensagens: any[]): Observable<any> {
    return this.http.post<any>('/api/whatsapp/detectar-e-salvar-fase-spin', { telefone, mensagens })
      .pipe(
        map(resposta => {
          if (resposta.sucesso) {
            return resposta.data;
          }
          throw new Error(resposta.erro || 'Erro ao detectar e salvar fase SPIN');
        }),
        catchError(erro => {
          console.error('Erro ao detectar e salvar fase SPIN:', erro);
          return throwError(erro);
        })
      );
  }

  /**
   * Salva o uso de uma sugestão para analytics
   */
  salvarUsoSugestao(dados: {
    telefone: string;
    faseSpin: string;
    sugestaoGerada: string;
    sugestaoUsada: boolean;
    tempoResposta: number;
  }): Observable<any> {
    return this.http.post('/api/whatsapp/salvar-uso-sugestao', dados)
      .pipe(
        catchError(erro => {
          console.error('Erro ao salvar uso de sugestão:', erro);
          return of({ sucesso: false });
        })
      );
  }

  /**
   * Mapeia etapa do funil para fase SPIN
   */
  private mapearEtapaParaFaseSpin(etapa?: string): string {
    const mapeamento: Record<string, string> = {
      'Prospecção': 'situacao',
      'Qualificação': 'problema',
      'Objeção': 'implicacao',
      'Fechamento': 'necessidade'
    };
    return mapeamento[etapa || ''] || 'situacao';
  }

  /**
   * Mapeia tom de conversa para formato da API
   */
  private mapearTomConversa(tom?: string): string {
    const tomsValidos = ['formal', 'informal', 'tecnico'];
    const tomLower = (tom || 'formal').toLowerCase();

    // Mapeamentos específicos
    if (tomLower === 'consultivo') return 'formal';
    if (tomLower === 'empático') return 'informal';

    return tomsValidos.includes(tomLower) ? tomLower : 'formal';
  }

  /**
   * Obtém sugestões offline com base no contexto
   */
  private obterSugestoesOffline(contexto: ContextoConversa): SugestaoMensagem[] {
    const sugestoes: SugestaoMensagem[] = [];

    // Tenta usar as sugestões pré-definidas primeiro
    const etapa = contexto.etapaFunil || 'Prospecção';
    const tom = contexto.tomConversa || 'Consultivo';

    if (this.sugestoesPorEtapa[etapa] && this.sugestoesPorEtapa[etapa][tom]) {
      this.sugestoesPorEtapa[etapa][tom].forEach(texto => {
        sugestoes.push({ texto });
      });
    }

    // Se não encontrou, usa genéricas
    if (sugestoes.length === 0) {
      this.sugestoesGenericas.slice(0, 3).forEach(texto => {
        sugestoes.push({ texto });
      });
    }

    return sugestoes;
  }

  /**
   * Gera mensagens de rapport/atratividade para abordagem outbound
   */
  gerarMensagensRapport(contexto: {
    telefone: string;
    nomeContato?: string;
    empresa?: string;
    tipoAbordagem: 'direta' | 'indireta' | 'consultiva';
    produto?: string;
    modoRapport?: boolean;
    ultimaMensagem?: string;
  }): Observable<SugestaoMensagem[]> {
    const payload = {
      telefone: contexto.telefone,
      nomeContato: contexto.nomeContato || '',
      empresa: contexto.empresa || '',
      tipoAbordagem: contexto.tipoAbordagem,
      produto: contexto.produto || 'Meu Cardápio',
      modoRapport: true,
      ultimaMensagem: contexto.ultimaMensagem
    };

    console.log('[SugestoesService] Gerando mensagens de rapport:', payload);

    return this.http.post<any>('/api/whatsapp/gerar-rapport', payload)
      .pipe(
        map(resposta => {
          console.log('[SugestoesService] Resposta rapport da API:', resposta);
          
          // Novo formato com múltiplas sugestões
          if (resposta.sucesso && resposta.data?.sugestoes) {
            return resposta.data.sugestoes.map((sug: any) => ({
              texto: sug.texto,
              confianca: sug.confianca || 0.85
            }));
          }
          
          // Compatibilidade com formato antigo (converter para array)
          if (resposta.sucesso && resposta.data?.mensagem) {
            return [{
              texto: resposta.data.mensagem,
              confianca: resposta.data.confianca || 0.9
            }];
          }
          
          // Retornar array vazio se não conseguiu gerar mensagens
          console.error('[SugestoesService] API não retornou mensagens de rapport válidas');
          return [];
        }),
        catchError(erro => {
          console.error('Erro ao gerar mensagens de rapport:', erro);
          // Em caso de erro, propagar o erro
          throw erro;
        })
      );
  }

  /**
   * Obtém mensagem de rapport offline (fallback)
   */
  private obterMensagemRapportOffline(contexto: {
    nomeContato?: string;
    empresa?: string;
    tipoAbordagem: 'direta' | 'indireta' | 'consultiva';
  }): { texto: string; confianca: number } {
    const nome = contexto.nomeContato || 'você';
    const empresa = contexto.empresa;

    const mensagensRapport = {
      direta: [
        `Oi ${nome}! 👋 Vi que sua empresa está crescendo - isso é ótimo! Tenho uma solução que pode ajudar a escalar ainda mais rápido. Posso te mostrar em apenas 5 minutos?`,
        `${nome}, notei que empresas como ${empresa || 'a sua'} estão economizando até 3h por dia com nossa solução. Que tal descobrir como fazer o mesmo? 🚀`,
        `Oi ${nome}! Separei 3 cases de sucesso de empresas similares à ${empresa || 'sua'}. Vale a pena dar uma olhada - pode ser um divisor de águas! Posso enviar?`
      ],
      indireta: [
        `Oi ${nome}! Como estão as coisas por aí? 😊 Vi algumas novidades interessantes no mercado de ${empresa ? 'seu segmento' : 'gestão'} que podem te interessar...`,
        `${nome}, espero que esteja tendo um ótimo dia! 🌟 Estava pensando em alguns desafios comuns que empresas enfrentam nessa época do ano. Como vocês estão lidando com isso?`,
        `Oi! Acabei de ler um artigo sobre tendências em ${empresa ? 'seu mercado' : 'gestão empresarial'} e lembrei de você. Algumas insights bem interessantes! Gostaria de compartilhar?`
      ],
      consultiva: [
        `${nome}, percebi que muitas empresas ${empresa ? 'do seu porte' : ''} estão buscando formas de otimizar processos. Qual tem sido seu maior desafio operacional atualmente?`,
        `Oi ${nome}! Tenho ajudado empresas a resolver questões específicas de gestão. Curioso(a) para saber: qual área da sua operação consome mais tempo hoje?`,
        `${nome}, uma pergunta rápida: se pudesse resolver apenas UM problema na ${empresa || 'sua empresa'} hoje, qual seria? Talvez eu possa ajudar com algumas ideias...`
      ]
    };

    const mensagens = mensagensRapport[contexto.tipoAbordagem];
    const textoEscolhido = mensagens[Math.floor(Math.random() * mensagens.length)];

    return {
      texto: textoEscolhido,
      confianca: 0.7
    };
  }

  /**
   * Gera mensagem de apresentação profissional
   */
  gerarMensagemApresentacao(contexto: {
    telefone: string;
    nomeContato?: string;
    empresa?: string;
    produto?: string;
    lead?: any;
  }): Observable<SugestaoMensagem[]> {
    const payload = {
      telefone: contexto.telefone,
      nomeContato: contexto.nomeContato || '',
      empresa: contexto.empresa || '',
      produto: contexto.produto || 'Meu Cardápio',
      modoApresentacao: true,
      lead: contexto.lead
    };

    console.log('[SugestoesService] Gerando mensagem de apresentação:', payload);

    return this.http.post<any>('/api/whatsapp/gerar-apresentacao', payload)
      .pipe(
        map(resposta => {
          console.log('[SugestoesService] Resposta apresentação da API:', resposta);
          
          // Novo formato com múltiplas sugestões
          if (resposta.sucesso && resposta.data?.sugestoes) {
            return resposta.data.sugestoes.map((sug: any) => ({
              texto: sug.texto,
              confianca: sug.confianca || 0.9
            }));
          }
          
          // Retornar array vazio se não conseguiu gerar mensagens
          console.error('[SugestoesService] API não retornou mensagens de apresentação válidas');
          return [];
        }),
        catchError(erro => {
          console.error('Erro ao gerar mensagem de apresentação:', erro);
          // Em caso de erro, propagar o erro
          throw erro;
        })
      );
  }
}
