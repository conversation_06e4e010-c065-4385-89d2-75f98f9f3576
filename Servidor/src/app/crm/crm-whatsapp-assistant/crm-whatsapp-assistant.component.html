<!-- WhatsApp Assistant - Layout Mobile Compacto -->
<div class="whatsapp-assistant">

  <!-- Loading -->
  <div *ngIf="carregandoDados" class="loading-screen">
    <div class="spinner-border" role="status">
      <span class="sr-only">Carregando...</span>
    </div>
    <p>Carregando dados do contato...</p>
  </div>

  <!-- Layout Principal -->
  <div *ngIf="!carregandoDados" class="assistant-layout">

    <!-- Header Verde Simples -->
    <div class="whatsapp-header">
      <div class="header-content">
        <div class="header-left">
          <i class="fe-message-circle header-icon"></i>
          <span class="header-title">Assistente de Vendas</span>
        </div>
        <div class="header-right">
          <button class="settings-btn">
            <i class="fe-settings"></i>
          </button>
          <button class="menu-btn">
            <i class="fe-more-vertical"></i>
          </button>
        </div>
      </div>
    </div>

    <!-- Cards Container -->
    <div class="cards-container">

      <!-- Card Principal do Lead -->
      <div class="lead-card">
        <div class="lead-content">
          <div class="lead-avatar">
            <div class="avatar-circle">
              {{ getInitials(contato.nomeResponsavel || contato.empresa || nomeWhatsApp) }}
            </div>
          </div>
          <div class="lead-info">
            <h1 class="lead-name">{{ contato.nomeResponsavel || contato.empresa || nomeWhatsApp || 'NOVO CONTATO' }}</h1>
            <div class="lead-status">
              <span class="status-badge" [class.registered]="contato.id">
                {{ contato.id ? 'Lead Cadastrado' : 'Novo Lead' }}
              </span>
            </div>
          </div>
          <div class="lead-score" *ngIf="contato.score">
            <span class="score-badge">{{ contato.score }}% Score</span>
          </div>
        </div>
      </div>

      <!-- Card Detalhes -->
      <div class="detail-card">
        <div class="card-header">
          <i class="fe-user"></i>
          <span>Detalhes</span>
        </div>
        <div class="detail-grid">
          <div class="detail-item">
            <div class="detail-icon empresa">
              <i class="fe-briefcase"></i>
            </div>
            <div class="detail-content">
              <div class="detail-label">EMPRESA</div>
              <div class="detail-value">{{ contato.crmEmpresa?.nome || contato.empresa || 'GAMAN' }}</div>
            </div>
          </div>
          
          <div class="detail-item">
            <div class="detail-icon telefone">
              <i class="fe-phone"></i>
            </div>
            <div class="detail-content">
              <div class="detail-label">TELEFONE</div>
              <div class="detail-value">{{ contato.telefone || '556234348879' }}</div>
            </div>
          </div>
          
          <div class="detail-item">
            <div class="detail-icon segmento">
              <i class="fe-tag"></i>
            </div>
            <div class="detail-content">
              <div class="detail-label">SEGMENTO</div>
              <div class="detail-value">{{ contato.segmento || 'Restaurante' }}</div>
            </div>
          </div>
          
          <div class="detail-item" *ngIf="contato.instagramHandle">
            <div class="detail-icon instagram">
              <i class="fe-instagram"></i>
            </div>
            <div class="detail-content">
              <div class="detail-label">INSTAGRAM</div>
              <div class="detail-value">{{ contato.instagramHandle || '@gamanjapanesefood' }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- Card Links da Empresa -->
      <div class="links-card" *ngIf="contato.links && contato.links.length > 0">
        <div class="card-header">
          <i class="fe-link"></i>
          <span>Links da Empresa</span>
        </div>
        <div class="links-grid">
          <div class="link-item" *ngFor="let link of contato.links">
            <div class="link-icon" [ngClass]="getLinkIconClass(link.tipo)">
              <i [class]="getLinkIcon(link.tipo)"></i>
            </div>
            <div class="link-content">
              <div class="link-label">{{ getLinkLabel(link.tipo) }}</div>
              <a [href]="link.url" target="_blank" class="link-value">{{ getLinkDisplayText(link.url) }}</a>
            </div>
          </div>
        </div>
      </div>

      <!-- Card Métricas -->
      <div class="metrics-card">
        <div class="card-header">
          <i class="fe-activity"></i>
          <span>Métricas</span>
        </div>
        <div class="metrics-row">
          <div class="metric-circle fase">
            <i class="fe-target"></i>
            <div class="metric-info">
              <div class="metric-label">FASE</div>
              <div class="metric-value">{{ getFaseDisplayName(faseSpin) }}</div>
            </div>
          </div>
          
          <div class="metric-circle resposta">
            <i class="fe-clock"></i>
            <div class="metric-info">
              <div class="metric-label">RESPOSTA</div>
              <div class="metric-value">2 min</div>
            </div>
          </div>
          
          <div class="metric-circle potencial">
            <i class="fe-trending-up"></i>
            <div class="metric-info">
              <div class="metric-label">POTENCIAL</div>
              <div class="metric-value">Alto</div>
            </div>
          </div>
          
          <div class="metric-circle prioridade">
            <i class="fe-zap"></i>
            <div class="metric-info">
              <div class="metric-label">PRIORIDADE</div>
              <div class="metric-value">Hot</div>
            </div>
          </div>
        </div>
      </div>

      <!-- Card Status CRM -->
      <div class="status-crm-card">
        <div class="card-header">
          <div class="header-left">
            <i class="fe-link"></i>
            <span>Status CRM</span>
          </div>
          <div class="header-right" *ngIf="contato.idBitrix">
            <a [href]="getBitrixUrl()" target="_blank" class="bitrix-btn">
              <i class="fe-grid"></i>
              Abrir no Bitrix
            </a>
          </div>
        </div>
        <div class="status-crm-content">
          <div class="status-indicator" *ngIf="contato.idBitrix">
            <span class="status-dot"></span>
            <span class="status-text">Sincronizado com Bitrix24 • Lead #{{ contato.idBitrix }}</span>
          </div>
          <div class="status-indicator" *ngIf="!contato.idBitrix">
            <span class="status-dot offline"></span>
            <span class="status-text">Lead não cadastrado no CRM</span>
          </div>
        </div>
      </div>

      <!-- Card Modo -->
      <div class="mode-card">
        <div class="card-header">
          <i class="fe-settings"></i>
          <span>Modo</span>
        </div>
        <div class="mode-options">
          <button class="btn btn-block btn-outline-danger mode-action-btn rapport-btn" (click)="gerarRapport()">
            <i class="fe-heart mr-1"></i>
            Gerar Rapport
          </button>
          
          <label class="mode-checkbox">
            <input type="checkbox" [(ngModel)]="modoApresentacao" (change)="toggleModoApresentacao()">
            <span class="checkmark"></span>
            Apresentação Profissional
          </label>
        </div>
      </div>

      <!-- Card Fase da Venda SPIN -->
      <div class="spin-card" *ngIf="!modoApresentacao">
        <div class="card-header">
          <i class="fe-target"></i>
          <span>Fase da Venda (SPIN)</span>
        </div>
        
        <!-- Timeline Horizontal SPIN -->
        <div class="spin-timeline">
          <div class="timeline-container">
            
            <!-- Auto (oculto na timeline visual, mas mantido funcionalmente) -->
            <div class="timeline-step auto-step" 
                 [class.active]="faseSpin === 'auto'"
                 (click)="mudarFaseSpin('auto')"
                 *ngIf="faseSpin === 'auto'">
              <div class="step-circle auto">
                <i class="fe-cpu"></i>
              </div>
              <div class="step-label">Auto</div>
            </div>
            
            <!-- Situação -->
            <div class="timeline-step" 
                 [class.completed]="isStepCompleted('situacao')"
                 [class.active]="faseSpin === 'situacao'"
                 (click)="mudarFaseSpin('situacao')">
              <div class="step-circle">
                <i class="fe-check" *ngIf="isStepCompleted('situacao')"></i>
                <span *ngIf="!isStepCompleted('situacao')">1</span>
              </div>
              <div class="step-label">Situação</div>
            </div>
            
            <!-- Linha de conexão 1 -->
            <div class="timeline-line" [class.completed]="isStepCompleted('situacao')"></div>
            
            <!-- Problema -->
            <div class="timeline-step"
                 [class.completed]="isStepCompleted('problema')"
                 [class.active]="faseSpin === 'problema'"
                 (click)="mudarFaseSpin('problema')">
              <div class="step-circle">
                <i class="fe-check" *ngIf="isStepCompleted('problema')"></i>
                <span *ngIf="!isStepCompleted('problema')">2</span>
              </div>
              <div class="step-label">Problema</div>
            </div>
            
            <!-- Linha de conexão 2 -->
            <div class="timeline-line" [class.completed]="isStepCompleted('problema')"></div>
            
            <!-- Implicação -->
            <div class="timeline-step"
                 [class.completed]="isStepCompleted('implicacao')"
                 [class.active]="faseSpin === 'implicacao'"
                 (click)="mudarFaseSpin('implicacao')">
              <div class="step-circle">
                <i class="fe-check" *ngIf="isStepCompleted('implicacao')"></i>
                <span *ngIf="!isStepCompleted('implicacao')">3</span>
              </div>
              <div class="step-label">Implicação</div>
            </div>
            
            <!-- Linha de conexão 3 -->
            <div class="timeline-line" [class.completed]="isStepCompleted('implicacao')"></div>
            
            <!-- Necessidade -->
            <div class="timeline-step"
                 [class.completed]="isStepCompleted('necessidade')"
                 [class.active]="faseSpin === 'necessidade'"
                 (click)="mudarFaseSpin('necessidade')">
              <div class="step-circle">
                <i class="fe-check" *ngIf="isStepCompleted('necessidade')"></i>
                <span *ngIf="!isStepCompleted('necessidade')">4</span>
              </div>
              <div class="step-label">Necessidade</div>
            </div>
            
          </div>
        </div>

        <!-- Tom da Conversa -->
        <div class="tom-section">
          <label class="tom-label">Tom:</label>
          <select class="tom-select" [(ngModel)]="tipoTom">
            <option value="formal">Formal</option>
            <option value="informal">Informal</option>
            <option value="tecnico">Técnico</option>
          </select>
        </div>
      </div>


    </div>

    <!-- Botão Principal -->
    <div class="main-action">
      <button class="generate-btn" [disabled]="carregando" (click)="gerarSugestao()">
        <span *ngIf="!carregando && !modoApresentacao">
          <i class="fe-edit mr-1"></i>
          Gerar Sugestão de Resposta
        </span>
        <span *ngIf="!carregando && modoApresentacao">
          <i class="fe-user-check mr-1"></i>
          Gerar Apresentação Profissional
        </span>
        <span *ngIf="carregando">
          <i class="fe-loader rotating mr-1"></i>
          {{ carregando && modoApresentacao ? 'Criando apresentação...' : 'Analisando conversa...' }}
        </span>
      </button>
    </div>

    <!-- Card Sequência de Mensagens (quando disponível) -->
    <div class="suggestions-card" *ngIf="sugestoes.length > 0 && !carregando">
      <div class="card-header">
        <i class="fe-message-square"></i>
        <span>Sequência de Mensagens</span>
        <div class="suggestions-badges">
          <span class="suggestion-count">{{ sugestoes.length }} mensagens</span>
        </div>
      </div>

      <div class="message-sequence">
        <!-- Loop para exibir todas as mensagens -->
        <div class="message-item" *ngFor="let sugestao of sugestoes; let i = index">
          <div class="message-header">
            <span class="message-number">{{ i + 1 }}ª Mensagem:</span>
            <button class="action-btn copy-individual" (click)="copiarSugestao(i)">
              <i class="fe-copy"></i>
              Copiar
            </button>
          </div>
          <div class="message-content">
            <textarea class="message-textarea"
                      [value]="sugestao.texto"
                      rows="3"
                      placeholder="Mensagem aparecerá aqui..."
                      readonly></textarea>
          </div>
        </div>
        
        <!-- Ações globais -->
        <div class="sequence-actions">
          <button class="action-btn refresh" (click)="regenerarSugestao()" [disabled]="carregando">
            <i class="fe-refresh-cw"></i>
            Gerar Nova Sequência
          </button>
        </div>
      </div>
    </div>

    <!-- Card Captura Rápida (colapsível) -->
    <div class="collapse" id="capturaRapida">
      <div class="capture-card">
        <div class="card-header">
          <i class="fe-user-plus"></i>
          <span>Cadastrar Novo Lead</span>
        </div>
        <div class="capture-form">
          <div class="form-group">
            <label>Nome do contato</label>
            <input type="text" class="form-input" [value]="nomeWhatsApp" readonly>
          </div>
          <div class="form-group">
            <label>Nome da empresa</label>
            <input type="text" class="form-input" placeholder="Ex: Restaurante do João" [(ngModel)]="novoLead.empresa">
          </div>
          <div class="form-group">
            <label>Email</label>
            <input type="email" class="form-input" placeholder="<EMAIL>" [(ngModel)]="novoLead.email">
          </div>
          <div class="form-group">
            <label>Telefone</label>
            <input type="text" class="form-input" [value]="contato.telefone" readonly>
          </div>
          <div class="form-group">
            <label>Observações</label>
            <textarea class="form-input" rows="3" 
                      placeholder="Ex: Interessado em cardápio digital, tem 2 lojas..."
                      [(ngModel)]="novoLead.observacoes"></textarea>
          </div>
          <button class="save-btn" (click)="cadastrarLeadRapido()">
            <i class="fe-save mr-1"></i>
            Salvar e Cadastrar Lead
          </button>
        </div>
      </div>
    </div>

    <!-- Alerta de Erro -->
    <div class="error-alert" *ngIf="erro">
      <i class="fe-alert-triangle"></i>
      <span>{{ erro }}</span>
    </div>

  </div>
</div>