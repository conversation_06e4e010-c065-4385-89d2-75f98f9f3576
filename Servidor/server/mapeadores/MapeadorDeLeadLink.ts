import { MapeadorBasico } from "./MapeadorBasico";

export default class MapeadorDeLeadLink extends MapeadorBasico {
  constructor() {
    super('LeadLink');
    // CRM independente - desativar contexto multi-tenant
    this.desativeMultiCliente();
  }

  // Buscar links por lead
  async buscarPorLead(crmLeadId: number): Promise<any[]> {
    console.log('MAPEADOR_LINK DEBUG: Buscando links para crmLeadId:', crmLeadId);
    console.log('MAPEADOR_LINK DEBUG: Tipo do crmLeadId:', typeof crmLeadId);

    // Verificar se o parâmetro está sendo passado corretamente
    const parametros = { crmLeadId };
    console.log('MAPEADOR_LINK DEBUG: Parâmetros da consulta:', parametros);

    const resultado = await this.listeAsync(parametros);
    console.log('MAPEADOR_LINK DEBUG: Resultado da consulta:', resultado);
    console.log('MAPEADOR_LINK DEBUG: Total de links encontrados:', resultado?.length || 0);

    // Verificar se todos os links retornados são do lead correto
    if (resultado && resultado.length > 0) {
      const linksCorretos = resultado.filter(link => link.crmLeadId === crmLeadId);
      const linksIncorretos = resultado.filter(link => link.crmLeadId !== crmLeadId);
      console.log('MAPEADOR_LINK DEBUG: Links corretos:', linksCorretos.length);
      console.log('MAPEADOR_LINK DEBUG: Links incorretos:', linksIncorretos.length);
      if (linksIncorretos.length > 0) {
        console.log('MAPEADOR_LINK DEBUG: IDs incorretos:', [...new Set(linksIncorretos.map(l => l.crmLeadId))]);
      }
    }

    return resultado;
  }

  // Buscar link específico por lead e tipo
  async buscarPorLeadETipo(crmLeadId: number, tipo: string): Promise<any> {
    const links = await this.listeAsync({ crmLeadId, tipo });
    return links.length > 0 ? links[0] : null;
  }

  // Remover todos os links de um lead
  async removerPorLead(crmLeadId: number): Promise<void> {
    return this.gerenciadorDeMapeamentos.atualizeAsync(
      this.metodo('removaPorLead'), 
      { crmLeadId }
    );
  }

  // Remover link por tipo
  async removerPorTipo(crmLeadId: number, tipo: string): Promise<void> {
    return this.gerenciadorDeMapeamentos.atualizeAsync(
      this.metodo('removaPorTipo'), 
      { crmLeadId, tipo }
    );
  }

  // Salvar múltiplos links de uma vez
  async salvarLinks(crmLeadId: number, links: any[]): Promise<void> {
    console.log('MAPEADOR_LINK: Salvando links para lead ID:', crmLeadId);
    console.log('MAPEADOR_LINK: Tipo do crmLeadId:', typeof crmLeadId);
    console.log('MAPEADOR_LINK: Links a serem salvos:', links);

    if (!crmLeadId || crmLeadId === null || crmLeadId === undefined) {
      throw new Error(`crmLeadId inválido: ${crmLeadId}`);
    }

    // Primeiro remove todos os links existentes
    await this.removerPorLead(crmLeadId);

    // Depois insere os novos links
    for (const link of links) {
      link.crmLeadId = crmLeadId;
      console.log('MAPEADOR_LINK: Inserindo link:', link);
      await this.insiraSync(link);
    }

    console.log('MAPEADOR_LINK: Todos os links foram salvos com sucesso');
  }
}
