import { MapeadorBasico } from "./MapeadorBasico";

export default class MapeadorDeLeadLink extends MapeadorBasico {
  constructor() {
    super('LeadLink');
    // CRM independente - desativar contexto multi-tenant
    this.desativeMultiCliente();
  }

  // Buscar links por lead
  async buscarPorLead(crmLeadId: number): Promise<any[]> {
    return this.listeAsync({ crmLeadId: crmLeadId });
  }

  // Buscar link específico por lead e tipo
  async buscarPorLeadETipo(crmLeadId: number, tipo: string): Promise<any> {
    const links = await this.listeAsync({ crmLeadId, tipo });
    return links.length > 0 ? links[0] : null;
  }

  // Remover todos os links de um lead
  async removerPorLead(crmLeadId: number): Promise<void> {
    return this.gerenciadorDeMapeamentos.atualizeAsync(
      this.metodo('removaPorLead'),
      { crmLeadId }
    );
  }

  // Remover link por tipo
  async removerPorTipo(crmLeadId: number, tipo: string): Promise<void> {
    return this.gerenciadorDeMapeamentos.atualizeAsync(
      this.metodo('removaPorTipo'),
      { crmLeadId, tipo }
    );
  }

  // Salvar múltiplos links de uma vez
  async salvarLinks(crmLeadId: number, links: any[]): Promise<void> {
    console.log('MAPEADOR_LINK: Salvando links para lead ID:', crmLeadId);
    console.log('MAPEADOR_LINK: Tipo do crmLeadId:', typeof crmLeadId);
    console.log('MAPEADOR_LINK: Links a serem salvos:', links);

    if (!crmLeadId || crmLeadId === null || crmLeadId === undefined) {
      throw new Error(`crmLeadId inválido: ${crmLeadId}`);
    }

    // Primeiro remove todos os links existentes
    await this.removerPorLead(crmLeadId);

    // Depois insere os novos links
    for (const link of links) {
      link.crmLeadId = crmLeadId;
      console.log('MAPEADOR_LINK: Inserindo link:', link);
      await this.insiraSync(link);
    }

    console.log('MAPEADOR_LINK: Todos os links foram salvos com sucesso');
  }
}
