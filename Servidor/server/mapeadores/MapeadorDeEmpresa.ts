import {MapeadorBasicoComCache} from './MapeadorBasicoComCache';
import {Empresa} from "../domain/Empresa";
import {<PERSON><PERSON><PERSON>} from "../domain/Modulo";
import {Cardapio} from "../domain/delivery/Cardapio";
import {HorarioFuncionamento} from "../domain/HorarioFuncionamento";
import {MapeadorFormaDeEntregaEmpresa} from "./MapeadorFormaDeEntregaEmpresa";
import {MapeadorDeCategoria} from "./MapeadorDeCategoria";
import {FormaDeEntregaEmpresa} from "../domain/delivery/FormaDeEntregaEmpresa";
import {IntegracaoPedidoFidelidade} from "../domain/IntegracaoPedidoFidelidade";
import {FactoryIntegracaoDelivery} from "../domain/integracoes/FactoryIntegracaoDelivery";
import {PausaProgramada} from "../domain/delivery/PausaProgramada";
import {CacheService} from "../service/CacheService";
import {DominioDaEmpresa} from "../domain/DominioDaEmpresa";
import {InformacoesDominioEmpresa} from "../domain/utils/InformacoesDominioEmpresa";
import {FormaDePagamento} from "../domain/delivery/FormaDePagamento";
import {TaxaCobranca} from "../domain/delivery/TaxaCobranca";
import {ConfigWhatsapp} from "../domain/ConfigWhatsapp";
import {MapeadorDePromocao} from "./MapeadorDePromocao";
import {MapeadorDeFormaDePagamento} from "./MapeadorDeFormaDePagamento";
import {IntegracaoFidelidade} from "../domain/integracoes/IntegracaoFidelidade";
import {Rede} from "../domain/Rede";
import {IntegracaoOpendelivery} from "../domain/integracoes/IntegracaoOpendelivery";
import {MapeadorDeDisponibilidade} from "./MapeadorDeDisponibilidade";
import {Disponibilidade} from "../domain/Disponibilidade";
import {MapeadorDeFormaDePagamentoIntegrada} from "./MapeadorDeFormaDePagamentoIntegrada";
import {MapeadorIntegracaoOpenDelivery} from "./MapeadorIntegracaoOpenDelivery";
import {Catalogo} from "../domain/catalogo/Catalogo";
import { IntegracaoOpendeliveryLogistica } from '../domain/integracoes/IntegracaoOpendeliveryLogistica';
import {IntegracaoUberdirect} from "../domain/integracoes/IntegracaoUberdirect";
import {IntegracaoFoodyDelivery} from "../domain/integracoes/IntegracaoFoodyDelivery";
import {IntegracaoIfood} from "../domain/integracoes/IntegracaoIfood";
import {IntegracaoGatewayPagamento} from "../domain/integracoes/IntegracaoGatewayPagamento";
import { TemaPersonalizado } from '../domain/TemaPersonalizado';
import {MeucardapioPay} from "../domain/integracoes/MeucardapioPay";

export class MapeadorDeEmpresa extends MapeadorBasicoComCache {

  constructor() {
    super('empresa', false);
  }


  selecioneSync(query: any): Promise<any> {
    return new Promise( async (resolve, reject) => {
      let inicio = new Date().getTime();

      let empresa: Empresa =  await super.selecioneSync(query);
      console.log('tempo carregar empresa: ' + ( new Date().getTime() - inicio) / 1000);

      if(empresa) {
        let contexto =  require('domain').active.contexto;
        contexto.idEmpresa = empresa.id;

        /*
        const lista = [];
        if( empresa.camposAdicionais ) {
          for (let adicional of empresa.camposAdicionais) {
            let novoAdicional = MapeadorDeProduto.carregueCampos(adicional);

            if (novoAdicional) {
              lista.push(novoAdicional);
            }
          }

          empresa.camposAdicionais = lista;
        }
         */
        inicio = new Date().getTime();
        empresa.formasDeEntrega = await new MapeadorFormaDeEntregaEmpresa().listeAsync({ idEmpresa: empresa.id});
        empresa.catalogo.categorias = await new MapeadorDeCategoria(empresa.catalogo).listeCachePorIdPai(
          this.obtenhaIdChaveCatalogo(empresa), "idCatalogo", empresa.id);
        empresa.formasDePagamento = await new MapeadorDeFormaDePagamento().listeAsync({});

        empresa.formasDePagamento.forEach((forma: FormaDePagamento) => {
          forma.setDadosFormaPdv(empresa.integracaoDelivery ? empresa.integracaoDelivery.sistema : null);
          if(empresa.integracaoOpenDeliveryAtiva())
            forma.setDadosOpendelivery();
        })

        empresa.promocoes = await new MapeadorDePromocao().listeAsync({});
        empresa.catalogo.disponibilidades =
          await new MapeadorDeDisponibilidade( empresa.catalogo).listeAsync({idCatalogo:  empresa.catalogo.id});

        if(empresa.integracaoDelivery)
          empresa.integracaoDelivery.formasDePagamento =
            await new MapeadorDeFormaDePagamentoIntegrada().listeAsync({ sistema:   empresa.integracaoDelivery.sistema})

        if(empresa.integracaoOpenDeliveryAtiva()){
          let formasPagamentosOpendelivery: Array<any>  =  await new MapeadorIntegracaoOpenDelivery().listeMetodosPagamento( )

          if(empresa.integracaoOpendelivery)
            empresa.integracaoOpendelivery.formasDePagamento = formasPagamentosOpendelivery;

          if (empresa.integracaoOpendeliveryLogistica)
            empresa.integracaoOpendeliveryLogistica.formasDePagamento = formasPagamentosOpendelivery
        }


        console.log('tempo carregar  formas/categoria empresa: ' + ( new Date().getTime() - inicio) / 1000);
      }

      return resolve(empresa)
    });

  }

  private obtenhaIdChaveCatalogo(empresa: Empresa) {
    return empresa.catalogo.id + "_" + empresa.id;
  }

  private obtenhaChaveCatalogos(catalogo: Empresa) {
    return catalogo.id + "_"
  }

  selecioneBusca(query: any): Promise<any> {
    return this.gerenciadorDeMapeamentos.selecioneVariosAsync(this.metodo('selecioneBusca'), query);
  }

  async removaDasCaches(empresa: any, catalogo: boolean = false){
    await super.removaDasCaches(empresa);
    if(catalogo)
      await new MapeadorDeCategoria(empresa.catalogo).removaCacheDeLista(this.obtenhaIdChaveCatalogo(empresa), 'idCatalogo')
  }

  async removaCacheCatalogos(catalogo: any){
    let chave = this.obtenhaChaveCatalogos(catalogo);

    await new MapeadorDeCategoria(catalogo).removaCacheDeLista(chave, 'idCatalogo')
  }

  async removaCacheEmpresasDoCatalogo(catalogo: any){
    return new Promise( async (resolve, reject) => {
      this.gerenciadorDeMapeamentos.selecioneVariosAsync(this.metodo("selecioneEmpresasQueUsamCatalogo"),
        {idCatalogo: catalogo.id}, false).then(async (empresas: any) => {
        for (let empresa of empresas)
          await this.removaDasCaches(empresa)

        resolve(true);
      })
    })
  }

  async removaListaDeCategoriasDaCache(empresa: any) {
    console.log(String(`removendo cache de categorias da empresa: ${empresa.id} , ${empresa.dominio || empresa.hostname} `))
    await new MapeadorDeCategoria(empresa.catalogo).removaCacheDeLista(this.obtenhaIdChaveCatalogo(empresa), 'idCatalogo')
  }

  async removaListaDeCategoriasDaCacheDoCatalogo(catalogo: any) {
      return new Promise( async (resolve, reject) => {
        if(catalogo.id)
          this.gerenciadorDeMapeamentos.selecioneVariosAsync("empresa.selecioneEmpresasQueUsamCatalogo",
            {idCatalogo: catalogo.id}, false).then(async (empresas: any) => {
            for(let i = 0; i < empresas.length; i++) {
              let empresa = empresas[i]
              empresa.catalogo = catalogo;
              console.log('*removendo cache de produtos da empresa  ' + empresa.id + '*')
              this.removaListaDeCategoriasDaCache(empresa)
            }

            resolve(true);
          });

      });
  }

  async crieObjetoDaCache(json: any){
    let empresa = new Empresa();

    Object.assign(empresa, json);

    empresa.catalogo = Object.assign(new Catalogo(), empresa.catalogo);

    if( empresa.dadosRede ) {
      empresa.dadosRede = Object.assign(new Rede(), empresa.dadosRede);
    }
    if( empresa.cardapio )
      empresa.cardapio = Object.assign(new Cardapio(null), empresa.cardapio);

    if( empresa.temaPersonalizado )
      empresa.temaPersonalizado = Object.assign(new TemaPersonalizado(), empresa.temaPersonalizado);

    if( empresa.configWhatsapp && empresa.configWhatsapp.id ) {
      empresa.configWhatsapp = new ConfigWhatsapp();
      Object.assign(empresa.configWhatsapp, json.configWhatsapp);
    } else {
      empresa.configWhatsapp = null;
    }

    if(empresa.horariosFuncionamento && empresa.horariosFuncionamento.length > 0) {
      for(let i = 0; i < empresa.horariosFuncionamento.length; i++) {
        let dadosHorario = empresa.horariosFuncionamento[i]
        empresa.horariosFuncionamento[i] = new HorarioFuncionamento( null,null, null, null, null)
        Object.assign(empresa.horariosFuncionamento[i], dadosHorario)
      }
    }

    if(empresa.pausasProgramadas && empresa.pausasProgramadas.length > 0) {
      for(let i = 0; i < empresa.pausasProgramadas.length; i++) {
        let dadosPausa = empresa.pausasProgramadas[i]

        empresa.pausasProgramadas[i] = new PausaProgramada(null, null, null)
        Object.assign(empresa.pausasProgramadas[i], dadosPausa)
      }
    }

    for( let i = 0; i < empresa.formasDePagamento.length; i++ ) {
      empresa.formasDePagamento[i] = Object.assign(new FormaDePagamento(), empresa.formasDePagamento[i]);

      const forma = empresa.formasDePagamento[i];

      if(forma.taxaCobranca)
        forma.taxaCobranca = new TaxaCobranca(forma.taxaCobranca.id,
                                forma.taxaCobranca.percentual, forma.taxaCobranca.valor, forma.taxaCobranca.ativa)


      if( forma.configMeioDePagamento )
        forma.apagueCredenciais();

    }

    if(json.integracaoPedidoFidelidade)
      empresa.integracaoPedidoFidelidade = IntegracaoPedidoFidelidade.novaDaCache(json.integracaoPedidoFidelidade)

    if(json.integracaoDelivery)
      empresa.integracaoDelivery =  FactoryIntegracaoDelivery.novaDaCache(json.integracaoDelivery)

    if(json.integracaoFoodyDelivery)
      empresa.integracaoFoodyDelivery = Object.assign(new IntegracaoFoodyDelivery(null), json.integracaoFoodyDelivery);

     if(json.integracaoOpendeliveryLogistica)
      empresa.integracaoOpendeliveryLogistica = Object.assign(new IntegracaoOpendeliveryLogistica(), json.integracaoOpendeliveryLogistica)

    if(json.integracaoUberdirect)
      empresa.integracaoUberdirect = Object.assign(new IntegracaoUberdirect(null), json.integracaoUberdirect)

    if(json.integracoesIfood)
      empresa.integracoesIfood = json.integracoesIfood.map((integracao: any) => Object.assign(new IntegracaoIfood(null), integracao))

    if(json.formasDeEntrega){
      empresa.formasDeEntrega = [];
      json.formasDeEntrega.forEach( (forma: any) => {
        empresa.formasDeEntrega.push( FormaDeEntregaEmpresa.novaDaCache(forma))
      })
    }

    if(json.integracaoGatewayPagamento)
       empresa.integracaoGatewayPagamento = Object.assign(new IntegracaoGatewayPagamento(null, null, null, null),
         json.integracaoGatewayPagamento )

    if(json.meucardapioPay)
      empresa.meucardapioPay = Object.assign(new MeucardapioPay(null, null), json.meucardapioPay);

    empresa.tokenApiSuperLink = json.tokenApiSuperLink;
    empresa.catalogo.categorias = await new MapeadorDeCategoria(empresa.catalogo).listeCachePorIdPai( this.obtenhaIdChaveCatalogo(empresa)
      , "idCatalogo", empresa.id)

    if(empresa.integracaoFidelidade)
      empresa.integracaoFidelidade = Object.assign( new IntegracaoFidelidade( ), empresa.integracaoFidelidade)

    if(empresa.integracaoOpendelivery)
      empresa.integracaoOpendelivery = Object.assign( new IntegracaoOpendelivery( ), empresa.integracaoOpendelivery)

   if(empresa.catalogo.disponibilidades){
     for(let i = 0; i < empresa.catalogo.disponibilidades.length; i++)
       empresa.catalogo.disponibilidades[i] = Disponibilidade.novaDaCache(empresa.catalogo.disponibilidades[i])
   }

    return empresa;

  }

  selecioneTodasDTO(): Promise<any> {
    return this.gerenciadorDeMapeamentos.selecioneVariosAsync(this.metodo('selecioneTodasDTO'), {});
  }

  selecioneTodosDominiosAtivos(): Promise<any> {
    return this.gerenciadorDeMapeamentos.selecioneVariosAsync(this.metodo('selecioneDominios'), {});
  }

  async listeProximosBloqueios() {
    return this.gerenciadorDeMapeamentos.selecioneVariosAsync(this.metodo('selecioneProximosBloqueios'), {});

  }


  selecioneTodasComResumo(query: any): Promise<any> {
    return this.gerenciadorDeMapeamentos.selecioneVariosAsync(this.metodo('selecioneComResumo'), query);
  }

  selecioneTodosClientes(query: any) {
    query.naoFiltrarPorEmpresa = true;

    return new Promise( (resolve, reject) => {
      this.gerenciadorDeMapeamentos.selecioneVarios('contato.selecione', query,  (objs: any) => {
        resolve(objs);
      });
    });
  }


  obtenhaLimiteContatos(empresa: any): any{
    let query = { idEmpresa: empresa.id};
    return new Promise( (resolve, reject) => {
      this.gerenciadorDeMapeamentos.selecioneUm(this.metodo('selecioneLimiteContatos'),  query,  (obj: any) => {
           resolve(obj);
      });
    });
  }

  atualizeLocalizacao(obj: any) {
    this.removaDasCaches(obj)
    return this.gerenciadorDeMapeamentos.atualizeAsync(this.metodo('atualizeLocalizacao'), obj);
  }

  atualizeAceitarPedido(obj: any) {
    this.removaDasCaches(obj)
    return this.gerenciadorDeMapeamentos.atualizeAsync(this.metodo('atualizeAceitarPedido'), obj);
  }

  atualizeCatalogo(obj: any) {
    this.removaDasCaches(obj)
    return this.gerenciadorDeMapeamentos.atualizeAsync(this.metodo('atualizeCatalogo'), obj);
  }

  atualizeModeloCatalogo(obj: any) {
    this.removaDasCaches(obj)
    return this.gerenciadorDeMapeamentos.atualizeAsync(this.metodo('atualizeModeloCatalogo'), obj);
  }


  atualizeCapa(obj: any){
    this.removaDasCaches(obj)
    return this.gerenciadorDeMapeamentos.atualizeAsync(this.metodo('atualizeCapa'), obj);
  }

  atualizeFavicon(obj: any){
    this.removaDasCaches(obj)
    return this.gerenciadorDeMapeamentos.atualizeAsync(this.metodo('atualizeFavicon'), obj);
  }


  atualizeTema(obj: any) {
    this.removaDasCaches(obj)
    return this.gerenciadorDeMapeamentos.atualizeAsync(this.metodo('atualizeTema'), obj);
  }

  atualizeEnviarLinksBotao(obj: any) {
    this.removaDasCaches(obj)
    return this.gerenciadorDeMapeamentos.atualizeAsync(this.metodo('atualizeEnviarLinksBotao'), obj);
  }

  atualizeResponsavel(obj: any){
    this.removaDasCaches(obj)
    return this.gerenciadorDeMapeamentos.atualizeAsync(this.metodo('atualizeResponsavel'), obj);
  }

  async atualizeConfigModulosLoja(obj: any) {
    await this.removaDasCaches(obj)
    await this.gerenciadorDeMapeamentos.atualizeAsync(this.metodo('atualizeConfigModulosLoja'), obj)
  }

  atualizeLogo(obj: any){
    this.removaDasCaches(obj)
    return this.gerenciadorDeMapeamentos.atualizeAsync(this.metodo('atualizeLogo'), obj);
  }

  atualizeScriptsMarketing(obj: any) {
    this.removaDasCaches(obj);
    return this.gerenciadorDeMapeamentos.atualizeAsync(this.metodo('atualizeScriptsMarketing'), obj);
  }

  atualizeMeioEnvio(obj: any) {
    return this.gerenciadorDeMapeamentos.atualizeAsync(this.metodo('atualizeMeioEnvio'), obj);
  }

  atualizeDadosBloqueio(empresa: any) {
    this.removaDasCaches(empresa)
    return this.gerenciadorDeMapeamentos.atualizeAsync(this.metodo('atualizeBloqueada'), empresa);
  }

  atualizeSegmento(empresa: any) {
    this.removaDasCaches(empresa)
    return this.gerenciadorDeMapeamentos.atualizeAsync(this.metodo('atualizeSegmento'), empresa);
  }

  atualizeCliente(empresa: any) {
    this.removaDasCaches(empresa)
    return this.gerenciadorDeMapeamentos.atualizeAsync(this.metodo('atualizeCliente'), empresa);
  }

  atualizeConfiguracoesClassificacao(obj: any){
    return this.gerenciadorDeMapeamentos.atualizeAsync(this.metodo('atualizeConfiguracoesClassificacoes'), obj);
  }

  atualizeNomeCategoriaDestaques(empresa: any) {
    return this.gerenciadorDeMapeamentos.atualizeAsync(this.metodo('atualizeNomeCategoriaDestaques'), empresa);
  }

  atualizeExibirBandeiras(empresa: any) {
    return this.gerenciadorDeMapeamentos.atualizeAsync(this.metodo('atualizeExibirBandeiras'), empresa);
  }


  insiraResumoPedidosNovos() {
    return this.gerenciadorDeMapeamentos.atualizeAsync(this.metodo('insiraResumoPedidosNovos'), {});
  }

  atualizeResumoPedidos24s() {
    return this.gerenciadorDeMapeamentos.atualizeAsync(this.metodo('recalculeResumoPedidos24s'), {});
  }

  atualizeResumoPedidos7d() {
    return this.gerenciadorDeMapeamentos.atualizeAsync(this.metodo('recalculeResumoPedidos7d'), {});
  }

  atualizeResumoPedidosMes() {
    return this.gerenciadorDeMapeamentos.atualizeAsync(this.metodo('recalculeResumoPedidosMes'), {});
  }

  async atualizeConfiguracoesMesas(empresa: any){
    await this.removaDasCaches(empresa);

    return this.gerenciadorDeMapeamentos.atualizeAsync(this.metodo('atualizeConfiguracoesMesas'),
      empresa);
  }

  atualizeRemovido(empresa: any){
    return this.gerenciadorDeMapeamentos.atualizeAsync(this.metodo('atualizeRemovido'),
      empresa);
  }

  removaModulos(empresa: Empresa, modulo: Modulo) {
    return this.gerenciadorDeMapeamentos.atualizeAsync(this.metodo('removaModulo'),
      { empresa: empresa.id, modulo: modulo.id});
  }

  removaCamposExtras(empresa: Empresa){
    return this.gerenciadorDeMapeamentos.atualizeAsync(this.metodo('removaCampos'),
      { empresa: empresa.id });
  }

  removaFormasDeEntrega(empresa: Empresa){
    return this.gerenciadorDeMapeamentos.atualizeAsync(this.metodo('removaFormasRetiradas'),
      { empresa: empresa.id });
  }

  async insiraModulo(empresa: Empresa, modulo: any) {
    return this.gerenciadorDeMapeamentos.insiraAsync(this.metodo('insiraModulo'),
      { empresa: empresa.id, modulo: modulo.id});
  }

  async insiraCamposExtras(empresa: Empresa, campos: any) {

    for( let i = 0; i < campos.length; i++){
       let campo: any = campos[i];

       await this.gerenciadorDeMapeamentos.insiraAsync(this.metodo('insiraCampoExtra'),
         {empresa: empresa.id, campo: campo.id, opcional:   campo.opcional});
    }


  }

  async insiraFormasDeEntregas(empresa: Empresa, formas: any) {

    for( let i = 0; i < formas.length; i++){
      const forma = formas[i];
      const taxa = forma.taxa ? forma.taxa : {};

      await this.gerenciadorDeMapeamentos.insiraAsync(this.metodo('insiraFormaEntrega'),
         {empresa: empresa.id, forma: forma.id, taxa: taxa });
    }
  }

  atualizeSempreReceberPedidos(empresa: any) {
    this.removaDasCaches(empresa);
    return this.gerenciadorDeMapeamentos.atualizeAsync(this.metodo('atualizeSempreReceberPedidos'), empresa);
  }

  atualizeFusoHorario(empresa: any) {
    this.removaDasCaches(empresa);
    return this.gerenciadorDeMapeamentos.atualizeAsync(this.metodo('atualizeFusoHorario'), empresa);
  }

  atualizeImprimirTXT(empresa: any) {
    this.removaDasCaches(empresa);
    return this.gerenciadorDeMapeamentos.atualizeAsync(this.metodo('atualizeImprimirTXT'), empresa);
  }

  atualizeConfigImpressao(empresa: any) {
    this.removaDasCaches(empresa)
    return this.gerenciadorDeMapeamentos.atualizeAsync(this.metodo('atualizeConfigImpressao'), empresa);
  }

  removaConfigImpressao(empresa: any) {
    return this.gerenciadorDeMapeamentos.atualizeAsync(this.metodo('removaConfigImpressao'), empresa);
  }

  async atualizeSaldoMensagens(empresa: Empresa) {
    this.removaDasCaches(empresa)
    return this.gerenciadorDeMapeamentos.atualizeAsync(this.metodo('atualizeSaldoMensagens'), empresa);
  }

  adicioneLinkNaCache(link: string, empresa: any) {
    CacheService.insiraJson('link:' + link, {
      id: empresa.id,
      subdominio: empresa.dominio
    })
  }

  obtenhaInformacoesDoLink(link: string) {
    return new Promise<any>((resolve )  => {
      CacheService.getJson('link:' + link).then((objeto: any) => {
        if(objeto){
          return resolve(objeto)

        }else{
          this.selecioneNoBancoPorLink({link: link}).then( (obj: any) => {
            if(obj) CacheService.insiraJson('link:' + link , obj, this.tempoCache);
            return resolve(obj)
          })
        }


        if(!objeto) resolve(null)
      })

    })
  }

  removaLinkDaEmpresa(empresa: Empresa) {
    return new Promise((resolve) => {
      let linkAntigo = empresa.urlDaEmpresa

      if(!linkAntigo) return resolve(null);

      CacheService.removaChave("link:" + linkAntigo.hostname)

      this.gerenciadorDeMapeamentos.atualizeAsync('dominioDaEmpresa.remova', empresa).then(() => {
        this.removaDasCaches(empresa)
        resolve(null);
      })
    })
  }

  atualizeLinkDaEmpresa(empresa: Empresa, novo: DominioDaEmpresa) {
    return new Promise((resolve) => {
      let linkAntigo = empresa.urlDaEmpresa

      if(linkAntigo)
        CacheService.removaChave("link:" + linkAntigo.hostname)

      this.gerenciadorDeMapeamentos.atualizeAsync('dominioDaEmpresa.remova', empresa).then(() => {
        empresa.urlDaEmpresa = novo
        if(!novo)  {
          this.removaDasCaches(empresa)
          return resolve(null);
        }
        novo.empresa = empresa
        this.gerenciadorDeMapeamentos.insiraGraph('dominioDaEmpresa.insira',
          novo).then(() => {
          CacheService.insiraJson('link:' + novo.hostname, {
            id: empresa.id,
            subdominio: empresa.dominio,
            catalogo: novo.urlCatalogo,
            cardapio: novo.urlCardapio
          }, this.tempoCache)
          this.removaDasCaches(empresa)
          resolve(null)
        });

      })
    })
  }

  listeLojasChinas(){
    return this.gerenciadorDeMapeamentos.selecioneVariosAsync(this.metodo('listeLojasChinas'), {})
  }


  listeLojasGendais(){
    return this.gerenciadorDeMapeamentos.selecioneVariosAsync(this.metodo('listeLojasGendais'), {})
  }

  listeUnidadesChinas(){
    return this.gerenciadorDeMapeamentos.selecioneVariosAsync(this.metodo('selecioneUnidadesChinas'), {})
  }

  listeUnidadesGendais(){
    return this.gerenciadorDeMapeamentos.selecioneVariosAsync(this.metodo('selecioneUnidadesGendais'), {})
  }


  listeEmpresasRede(query: any): Promise<any> {
    return this.gerenciadorDeMapeamentos.selecioneVariosAsync(this.metodo('selecioneEmpresasRede'), query);
  }

  selecioneTotalEmpresasRede(query: any): Promise<any> {
    return this.gerenciadorDeMapeamentos.selecioneUmAsync(this.metodo('selecioneTotalEmpresasRede'), query);
  }

  ping( ) {
    return this.gerenciadorDeMapeamentos.selecioneUmAsync(this.metodo('selecionePing'),
      { });
  }

  private selecioneNoBancoPorLink(param: any): Promise<InformacoesDominioEmpresa> {
    return this.gerenciadorDeMapeamentos.selecioneUmAsync(this.metodo('selecioneNoBancoPorLink'), {link: param.link})
  }


  listeTodas(query: any = {}){
    return this.gerenciadorDeMapeamentos.selecioneVariosAsync(this.metodo('selecioneTodas'), query);
  }

  async atualizarNumeroWhatsappCampanhas(empresa: any, numeroId: number | null) {
    await this.removaDasCaches(empresa);
    return await this.gerenciadorDeMapeamentos.atualizeAsync(
      this.metodo('atualizarNumeroWhatsappCampanhas'),
      {id: empresa.id, numeroWhatsappCampanhasId: numeroId}
    );
  }

}
