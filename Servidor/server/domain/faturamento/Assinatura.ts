import {MapeadorDeAssinatura} from "../../mapeadores/MapeadorDeAssinatura";
import {EnumFormaPagamentoIugu} from "../../lib/emun/EnumFormaPagamentoIugu";
import {Empresa} from "../Empresa";
import {EnumFormaPagamento} from "../../lib/emun/EnumFormaPagamento";
import {CartaoCredito} from "./CartaoCredito";
import * as moment from 'moment';
import {Fatura} from "./Fatura";
import {MapeadorDeFatura} from "../../mapeadores/MapeadorDeFatura";
export class Assinatura {
  id: number;
  codigo: string;
  codigoPai: string;
  suspensa: boolean;
  ativa: boolean;
  dataCriacao: Date;
  dataAtualizacao: Date;
  dataVencimento: Date;
  formasDePagamento: string;
  identificadorPlano: string;
  dados: any;
  empresa: Empresa ;
  contrato: any ;
  cartao: CartaoCredito;
  dataPrimeiroPagamento: Date;
  dataUltimoPagamento: Date;
  constructor(empresa: Empresa, subscription: any) {
    this.empresa = empresa;
    if(subscription){
      this.codigo = subscription.id;
      this.suspensa = subscription.suspended;
      this.ativa = subscription.active;
      this.dataCriacao = moment(subscription.created_at).toDate();
      this.dataAtualizacao = moment(subscription.updated_at).toDate();
      this.identificadorPlano = subscription.plan_identifier;
      this.dataVencimento = subscription.expires_at ? moment(subscription.expires_at).toDate() : null;
      this.dados = {
        faturas_recentes: subscription.recent_invoices,
        subitems: subscription.subitems,
        price_cents: subscription.price_cents,
        modulos: subscription.custom_variables
      }

      if(subscription.custom_variables){
        let modulosAtivar =
          subscription.custom_variables.filter((item: any) => item.name === 'moduloId')

        if(modulosAtivar.length)
          this.dados.modulos = modulosAtivar.map((item: any) => item.value)

      }


      this.setFormasDePagamento(subscription.payable_with);
    }

  }

  private setFormasDePagamento(payable_with: any) {
    if(typeof payable_with === 'string'){
      this.formasDePagamento = EnumFormaPagamentoIugu[payable_with as keyof  typeof EnumFormaPagamentoIugu];
    } else {
      this.formasDePagamento = payable_with.map((item: any) =>  EnumFormaPagamentoIugu[item as keyof  typeof EnumFormaPagamentoIugu]).join(',')
    }
  }

  getDiaVencimento(){
    if(this.dataVencimento)
        return moment(this.dataVencimento).date();
  }

  insira(){
    this.dados = JSON.stringify(this.dados);
    return new MapeadorDeAssinatura().insiraGraph(this)
  }

  atualize(){
    if(typeof this.dados !== 'string' )
      this.dados = JSON.stringify(this.dados)

    return new MapeadorDeAssinatura().atualizeSync(this)
  }

  pagaNoCartao() {
    return this.formasDePagamento === EnumFormaPagamento.CartaoCredito;
  }

  aceitaCartao() {
      return this.formasDePagamento && this.formasDePagamento.indexOf( EnumFormaPagamento.CartaoCredito) >= 0
  }


  pagaNoBoleto() {
    return this.formasDePagamento === EnumFormaPagamento.Boleto;
  }

  pagaNoPix() {
    return this.formasDePagamento === EnumFormaPagamento.Pix;
  }

  ehNova(){
    if(!this.dataPrimeiroPagamento   )
      return moment().diff(moment(this.dataCriacao), 'd') <= 29;

    return moment().diff(moment(this.dataPrimeiroPagamento), 'd') <= 29;
  }


  async atualizeCartaoCredito(cartaoDeCredito: any) {
    if(cartaoDeCredito){
      this.formasDePagamento = EnumFormaPagamento.CartaoCredito;
      this.cartao = cartaoDeCredito;
    } else {
      this.cartao = null;
    }

    await new MapeadorDeAssinatura().atualizeCartaoCredito(this);
  }

  obtenhaFaturasRecentes() {
     if(typeof this.dados === 'string' )
       this.dados = JSON.parse(this.dados)

     return this.dados.faturas_recentes || [];
  }

  obtenhaValorAssinatura(){
    if(typeof this.dados === 'string' )
      this.dados = JSON.parse(this.dados)

    return this.dados.price_cents ? (this.dados.price_cents / 100 ) : null ;
  }
  obtenhaItensAssinatura(){
    if(typeof this.dados === 'string' )
      this.dados = JSON.parse(this.dados)

    return this.dados.subitems || [];
  }

  getModulosAtivar(){
    let dados: any =  this.getDados();

    return  dados && dados.modulos ? dados.modulos  :  [];
  }

  obtenhaFaturasRecentesNaoCanceladas() {

    return this.obtenhaFaturasRecentes().filter( (invoice: any) => invoice.status !== 'canceled');
  }


  obtenhaTotalNaoPagas() {
    let faturasRecentes = this.obtenhaFaturasRecentesNaoCanceladas();

    if(!faturasRecentes.length || faturasRecentes[0].status === 'paid') return 0;

    let faturaNaoPaga =
      faturasRecentes.filter((invoice: any) => invoice.status === 'pending' &&
        moment(invoice.due_date).diff(moment(), 'd') < 0);

    let faturasExpiradas =
      faturasRecentes.filter((invoice: any) => invoice.status === 'expired');


    return faturaNaoPaga.length  ?   faturaNaoPaga.length + faturasExpiradas.length : 0;
  }

  obtenhaDiasAtrasos() {
    if(this.obtenhaTotalNaoPagas() === 0 )  return 0;

    return  moment().startOf('d').diff(moment(this.obtenhaVencimentoUltimaFatura()).startOf('d'), 'd');

  }

  obtenhaVencimentoUltimaFatura(){
    let ultimaFatura: any = this.obtenhaUltimaFatura();

    if(ultimaFatura) return ultimaFatura.due_date

    return null;

  }

  obtenhaUltimaFatura(): Fatura {
    let faturasRecentes = this.obtenhaFaturasRecentesNaoCanceladas();

    let ultimaFatura: any;

    faturasRecentes.forEach( (invoice: any) => {
      if(!ultimaFatura || moment(invoice.due_date).isAfter( moment((ultimaFatura.due_date))))
        ultimaFatura = invoice;
    })

    return ultimaFatura;
  }

  getDados() {
    if(!this.dados) return  null;

    if(typeof this.dados === 'string' )
      this.dados = JSON.parse(this.dados);

    return this.dados;
  }

  // tslint:disable-next-line:member-ordering
  static async get(query: any){
    return new MapeadorDeAssinatura().selecioneSync(query)
  }

  async atualizeUltimoPagamento(fatura: any) {
    if(!this.dataPrimeiroPagamento)
      this.dataPrimeiroPagamento = fatura.dataPagamento;

    this.dataUltimoPagamento = fatura.dataPagamento;

    await new MapeadorDeAssinatura().atualizeDatasPagamento(this)
  }

  async atualizeDataVencimento(dataVencimento: Date){
    this.dataVencimento = dataVencimento;
    await new MapeadorDeAssinatura().atualizeDataVencimento(this)
  }

  async obtenhaProximoVencimento(intervalo: number) {
    let diaVencimento = this.getDiaVencimento();

    let dataProximoVencimento: Date = moment().date(diaVencimento).toDate();

    if(this.dataUltimoPagamento){
      let ultimaFaturaPaga: Fatura =  await new MapeadorDeFatura().obtenhaUltimaFaturaPaga(this);

      if(ultimaFaturaPaga)
        return ultimaFaturaPaga.obtenhaProximoVencimento(diaVencimento , intervalo);

    }

    return dataProximoVencimento;

  }


}
