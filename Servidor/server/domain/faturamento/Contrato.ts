import {Empresa} from "../Empresa";
import {PlanoEmpresarial} from "./PlanoEmpresarial";
import {MapeadorDeContrato} from "../../mapeadores/MapeadorDeContrato";
import {Fatura} from "./Fatura";
import {EnumTipoLancamento} from "../../lib/emun/EnumTipoLancamento";
import {ServicoCobrado} from "./ServicoCobrado";
import * as  moment from "moment";
import {Assinatura} from "./Assinatura";
import {DateUtils} from "../../lib/DateUtils";
import {MapeadorDeEmpresa} from "../../mapeadores/MapeadorDeEmpresa";
import {HistoricoContrato} from "../auditoria/HistoricoContrato";
export class Contrato {
  id: number;
  empresa: Empresa;
  plano: PlanoEmpresarial;
  diaVencimento: number;
  dataAtivacao: Date;
  dataFimTrial: Date;
  diasGratis: number;
  valorNegociado: number;
  taxaAdesao: number;
  limiteContatosNegociado: number;
  qtdeMensagensMes: number;
  faturas: Array<Fatura> = [];
  assinatura: Assinatura;
  numeroParcelas: number;
  qtdeOperadores: number;
  assinaturasDependentes: any = [];
  dataProximoVencimento: Date;
  constructor(empresa: Empresa, plano: PlanoEmpresarial, dataAtivacao: Date, diaVencimento: number,
              diasGratis: number = 0, valorNegociado: number = null,
              limiteContatosNegociado: number = null, taxaAdesao: number = null) {
    this.empresa = empresa;
    this.plano = plano;
    if(dataAtivacao)
      this.dataAtivacao = moment(dataAtivacao).toDate()
    this.diaVencimento = diaVencimento;
    this.diasGratis = diasGratis;
    this.valorNegociado = valorNegociado;
    this.limiteContatosNegociado = limiteContatosNegociado;
    this.taxaAdesao = taxaAdesao;
    if(diasGratis > 0)
      this.dataFimTrial = moment().add((diasGratis) , 'd').toDate();
  }

  static async obtenhaDadosVencimentoMesCorrente(empresa: Empresa){
    let resposta: any  = { adimplente : false, dataUltimaMensalidadeEmUso: null}

    let contrato: Contrato = await new MapeadorDeContrato().selecioneSync({idEmpresa: empresa.id});

    if(contrato){

      if( !contrato.estaNoTrial()) {

        let proximoVencimento = contrato.assinatura && contrato.assinatura.codigoPai ?
          contrato.assinatura.dataVencimento :  contrato.dataProximoVencimento;

        let jaVenceu = moment().startOf('d').isAfter(moment(proximoVencimento).startOf('d'));

        if(jaVenceu){
          resposta.dataUltimaMensalidadeEmUso =      moment(proximoVencimento).date();
          resposta.adimplente  = false;
        } else {
          let diaVencimento = moment(proximoVencimento).date();
          resposta.dataUltimaMensalidadeEmUso =  moment().date(diaVencimento).toDate();
          resposta.adimplente  = true;
        }

      } else {
        resposta.dataUltimaMensalidadeEmUso = new Date();
        resposta.adimplente  = true;
      }
    }


    return resposta;
  }

  obtenhaTotalMensal(){
    if(this.valorNegociado != null) return this.valorNegociado;

    let valor = this.plano.valor;

    if( this.assinatura && this.assinatura.pagaNoCartao() )
      valor = valor - this.plano.descontoCartao;

    return valor;
  }

  obtenhaValorMensalidade(){
    if(this.valorNegociado != null) return this.valorNegociado;

    return this.plano.valor;
  }

  obtenhaDescontoNegociado(){
    if(!this.plano.valor  ) return 0;

    if(this.valorNegociado != null)
      return Number( (this.plano.valor - this.valorNegociado).toFixed(2));
    else
      return 0;
  }



  async atualizePlano(plano: any){
    this.plano = plano;
    this.valorNegociado = null;
    if(plano.intervalo)
      this.numeroParcelas = plano.intervalo

    await new MapeadorDeContrato().atualizePlano(this)

  }



  async bloqueiEmpresaPorChargeback(fatura: any){
    this.empresa.bloqueada = true;
    this.empresa.dataBloqueioAuto = new Date();
    console.log(String(`bloqueando empresa ${this.empresa.nome} por chargeback fatura ${fatura.codigo} `));
    await new MapeadorDeEmpresa().atualizeDadosBloqueio(this.empresa);

    for(let i = 0; i < this.assinaturasDependentes.length; i++){
      let idAssinatura = this.assinaturasDependentes[i].id;
      let contratoDependente = await new MapeadorDeContrato().selecioneSync({ idAssinatura: idAssinatura});

      if(contratoDependente)
        await contratoDependente.bloqueiEmpresaPorChargeback(fatura);
    }
  }



  async reembolsouPagamento(fatura: any) {
    //TODO: tratar reembolso de fatura.
  }

  async gereFatura(referencia: any) {
    let dataVencimento = moment(referencia, "YYYYMM");

    dataVencimento.date(this.getDiaVencimento());

    let fatura = new Fatura(this, referencia, dataVencimento.toDate());
    let descricao = 'Mensalidade de ' + dataVencimento.format('MMMM [de] YYYY');

    fatura.insiraLancamento(descricao, EnumTipoLancamento.Cobranca,  1, ServicoCobrado.MESALIDADE,
      this.obtenhaTotalMensal(), this.obtenhaDescontoNegociado() );

    await fatura.insira();

    this.faturas.push(fatura);
  }

  async trouCartao(cartaoDeCredito: any){
    if(cartaoDeCredito)
      await cartaoDeCredito.salve();

    await this.assinatura.atualizeCartaoCredito(cartaoDeCredito)
  }

  async ativouViaCartao(cartaoDeCredito: any){
    await this.atualizeAtivo();
    await this.assinatura.atualizeCartaoCredito(cartaoDeCredito)

  }

  async atualizeAtivo() {
    this.dataAtivacao = new Date();
    return new MapeadorDeContrato().atualizeDataAtivacao(this);
  }

  async gereProximaFatura() {
    let ultimaFatura = this.obtenhaUltimaFatura();

    let dataVencimento =  moment(ultimaFatura ? ultimaFatura.referencia : null, "YYYYMM");

    dataVencimento.add(1, 'month');

    await this.gereFatura(dataVencimento.format('YYYYMM'));
  }

  obtenhaLimiteContatos() {
    if(this.limiteContatosNegociado)
      return this.limiteContatosNegociado;

    return this.plano.limiteContatos;
  }


  obtenhaUltimaFatura(): Fatura {
    let ultimaFatura: any;

    if( this.faturas && this.faturas.length ){
       this.faturas.forEach( (fatura => {
         if(!ultimaFatura || moment(fatura.dataVencimento).isAfter( moment((ultimaFatura.dataVencimento))))
            ultimaFatura = fatura;
       }))
    }

    return ultimaFatura;
  }

  obtenhaUltimaFaturaPaga(): Fatura {
    let ultimaFatura: any;

    if( this.faturas && this.faturas.length ){
      this.faturas.forEach( (fatura => {
        let faturaMaisAntiga = false;
        if( ultimaFatura )
          faturaMaisAntiga = moment(fatura.dataVencimento).isAfter( moment((ultimaFatura.dataVencimento)));

        if( (!ultimaFatura || faturaMaisAntiga) && fatura.estaPaga() )
          ultimaFatura = fatura;
      }))
    }

    return ultimaFatura;
  }

  obtenhaFaturaEmAberto(){
    return this.faturas.find( (fatura) =>  fatura.estaAguardandoPagamento() )
  }

  vinculeAssinatura(assinatura: Assinatura) {
    this.assinatura = assinatura;
    return new MapeadorDeContrato().atualizeAssinatura(this);
  }

  podeCriarFatura() {
    let vencimentoMes = this.getVencimentoMesCorrente();
    let diasAntesVencer  = vencimentoMes.diff(moment(), 'd');

     return diasAntesVencer  <= 10 // com 10 dias cria fatura iugu.
  }

  getVencimentoMesCorrente(): any{
    return moment().date(this.getDiaVencimento())
  }

  // tslint:disable-next-line:member-ordering
  static async get(query: any) {
    let contrato = await new MapeadorDeContrato().selecioneSync(query)

    return contrato;
  }

  estaNoTrial() {
    if(!this.dataFimTrial) return;

    return moment().startOf('d').diff(moment(this.dataFimTrial).startOf('d'), 'd') <= 0;
  }


  obtenhaVencimentoPrimeiraCobranca() {
    if(this.dataFimTrial) return  moment(this.dataFimTrial);

    if(!this.faturas.length) return  DateUtils.obtenhaVencimentoDiaUtil();

    return  DateUtils.obtenhaProximoVencimento(this.getDiaVencimento());
  }



  jaPagouAlguma() {
    return this.faturas.find( fatura => fatura.estaPaga()) != null
  }

  obtenhaPeridoCobranca(){
    if(!this.plano) return '';

    return this.plano.obtenhaDescricaoPeriodoCobranca()

  }

  getDiaVencimento() {
    return this.assinatura ? this.assinatura.getDiaVencimento() : this.diaVencimento;
  }

  async calculeProximoVencimento() {
    let qtdeCiclos = this.plano.intervalo;

    let  proximoVencimento = moment(this.dataProximoVencimento ? this.dataProximoVencimento : this.dataAtivacao).add(qtdeCiclos, 'month');

    await this.atualizeProximoVencimento( proximoVencimento.toDate(), 'Efetuou pagamento fatura')


  }


  async atualizeVencimentoBloqueio(operador: any = null){
    let valorAntigo = this.empresa.dataBloqueioAuto;

    this.empresa.calculeProximoBloqueio(this.dataProximoVencimento);

    let dados: any = { novoValor: this.empresa.dataBloqueioAuto}

    if(valorAntigo) dados.valorAntigo = valorAntigo;

    return new Promise(async (resolve: any) => {
      await new MapeadorDeEmpresa().atualizeDadosBloqueio(this.empresa);

      await new HistoricoContrato(this, 'Data bloqueio empresa recalculado', dados, operador).salve(true)
      resolve();
    })
  }


  async atualizeDiaVencimento(diaVencimento: number, operador: any) {
    let mapeador = new MapeadorDeContrato();
    let dados: any = { novoValor: diaVencimento}

    if(this.diaVencimento) dados.valorAntigo = this.diaVencimento;

    this.diaVencimento = diaVencimento;

    return new Promise((resolve: any) => {
      mapeador.transacao(async (conexao: any, commit: any) => {
        await new MapeadorDeContrato().atualizeDiaVencimento(this);
        await new HistoricoContrato(this, 'Dia vencimento atualizado', dados, operador).salve(true)

        commit( () => {
          resolve();
        })
      })
    })
  }

  async atualizeProximoVencimento(dataVencimento: Date, operacao: string , operador: any = null) {
    let mapeador = new MapeadorDeContrato();
    let dados: any = { novoValor: dataVencimento}

    if(this.dataProximoVencimento) dados.valorAntigo = this.dataProximoVencimento;

    this.dataProximoVencimento = dataVencimento;

    return new Promise((resolve: any) => {
      mapeador.transacao(async (conexao: any, commit: any) => {
        await new MapeadorDeContrato().atualizeDataVencimento(this);
        await new HistoricoContrato(this,   operacao, dados, operador).salve(true)
        console.log(String(`Novo vencimento calculado: ${this.dataProximoVencimento}`));

        await this.atualizeVencimentoBloqueio();
        commit( () => {
          resolve();
        })
      })
    })

  }
}
