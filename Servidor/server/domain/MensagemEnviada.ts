import {Empresa} from "./Empresa";
import {TipoDeNotificacaoEnum} from "./TipoDeNotificacaoEnum";
import {Contato} from "./Contato";
import {Cartao} from "./Cartao";
import {StatusDeMensagem} from "../service/StatusDeMensagem";
import {Campanha} from "./Campanha";
import {LinkEncurtado} from "./LinkEncurtado";
import {EnumMeioDeEnvio} from "./EnumMeioDeEnvio";
import {NumeroWhatsapp} from "./NumeroWhatsapp";
import axios from "axios";
const getMetaData = require('metadata-scraper');
let moment = require("moment");
const sharp = require('sharp');

export class MensagemEnviada {
  EXPRESSAO_URL = /(https?:\/\/)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&z\/\/=]*)/g;

  id: number;
  empresa: Empresa;
  mensagem: string;
  horario: Date;
  dia: Date;
  tipoDeNotificacao: TipoDeNotificacaoEnum;
  status: StatusDeMensagem;
  telefone: string;
  contato: Contato;
  horarioModificacao: Date;
  campanha: Campanha;
  links: Array<LinkEncurtado> = [];
  idSMSExterno: string;
  meioDeEnvio: EnumMeioDeEnvio;
  imagem: string;
  numeroWhatsapp: NumeroWhatsapp;
  idWhatsapp: string;

  abrirChat: boolean = false;
  posicao = 0;
  qtdeTentativas = 0;

  fazerPreview = true;
  //propriedade não persistente

  assinar = false;

  temMenu: boolean = false;

  menu: any = '';//menu é salvo como json

  linkPreview: any;
  enviarLinksBotao: any;

  constructor() {}

  static nova(contato: Contato, numeroWhatsapp: NumeroWhatsapp, mensagem: string, tipoDeNotificacao: TipoDeNotificacaoEnum,
              status: StatusDeMensagem, imagem: string = ''): MensagemEnviada {
    const mensagemEnviada = new MensagemEnviada();

    mensagemEnviada.empresa = contato.empresa;
    if( contato.id !== -1 ) {
      mensagemEnviada.contato = contato;
    }
    mensagemEnviada.numeroWhatsapp = numeroWhatsapp;
    mensagemEnviada.mensagem = mensagem;
    mensagemEnviada.horario = new Date();
    mensagemEnviada.tipoDeNotificacao = tipoDeNotificacao;
    mensagemEnviada.telefone = contato.codigoPais + contato.telefone;
    mensagemEnviada.status = status;
    mensagemEnviada.imagem = imagem;

    return mensagemEnviada;
  }

  expirou() {
    if( this.tipoDeNotificacao === TipoDeNotificacaoEnum.Aniversario ) {
      const agora = moment().startOf('day');
      const horarioMensagem = moment(this.horario).startOf('day');

      return agora.diff(horarioMensagem, 'days') > 0;
    }

    if( this.tipoDeNotificacao.toUpperCase().indexOf('PEDIDO') !== -1 ) {
      const tempoMensagemEmMinutos = (new Date().getTime() - this.horario.getTime()) / 1000.0 / 60;

      return tempoMensagemEmMinutos > 60;
    }

    return false;
  }

  processeMenu() {
    if( this.temMenu ) {
      if( typeof this.menu === 'string' && this.menu.length > 0 ) {
        this.menu = JSON.parse(this.menu);

        for(let i = 0; i < this.menu?.opcoes?.length; i++) {
          let opcao = this.menu.opcoes[i];

          opcao.id = this.id + '-' + opcao.texto;
        }
      }
    }
  }

  obtenhaResposta(textoOpcao: any) {
    if( !this.menu ) {
      return null;
    }

    for(let i = 0; i < this.menu.opcoes.length; i++) {
      let opcao = this.menu.opcoes[i];

       if( opcao.texto.toLowerCase() === textoOpcao.toLowerCase() ) {
        // Retorna a mensagem se existir, senão retorna o valor da opção
        return opcao.mensagem || opcao.valor || opcao.texto;
      }
    }

    return null;
  }

  extrairPrimeiroLink = (texto: string): string | null => {
    const EXPRESSAO_URL = /(https?:\/\/)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&=\/]*)/g;
    const resultado = texto.match(EXPRESSAO_URL);

    return resultado ? resultado[0] : null;
  };

  async convertImageToBase64(url: string) {
    try {
      const response = await axios({
        method: 'get',
        url: url,
        responseType: 'arraybuffer'
      });

      // Convertendo os dados da imagem para Base64
      let buffer = Buffer.from(response.data, 'binary');
      const dados = await sharp(buffer)
        .resize(1024)
        .toBuffer();

      buffer = Buffer.from(dados, 'binary');

      const base64 = buffer.toString('base64');

      const metadados = await sharp(buffer).metadata();

      return {
        thumbnailWidth: metadados.width,
        thumbnailHeight: metadados.height,
        base64: `${base64}`
    };
    } catch (error) {
      console.error('Erro ao converter imagem:', error);
      return null;
    }
  }

  async processeLinksParaPreview() {
    if( true ) {
      return;
    }

    /*
    const primeiroLink = this.extrairPrimeiroLink(this.mensagem);

    if( primeiroLink ) {
      console.log('extraindo tags do link:', primeiroLink);

        let response: any = null;
        try {
          response = await axios.get(primeiroLink, {
            headers: {
              'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
              'Accept-Language': 'pt-BR'
            },
            timeout: 2000  // Aumente se necessário
          });
        } catch (error) {
          console.error('Erro ao obter link:', error);
        }

        if( response && response.data ) {
          const metadata = await getMetaData({
            html: response.data
          });
          this.linkPreview = {
            title: metadata.title,
            description: metadata.description,
            imagem: metadata.image
          }

          if (this.linkPreview.thumbnailHQ) {
            const dadosImagem = await this.convertImageToBase64(this.linkPreview.imagem).catch( (erro) => {
              console.log('Erro ao converter imagem:', erro);
            });

            if( !dadosImagem ) return;

            this.linkPreview.thumbnailHQ = dadosImagem.base64;
            this.linkPreview.thumbnail = this.linkPreview.thumbnailHQ;
            this.linkPreview.thumbnailHeight = dadosImagem.thumbnailHeight;
            this.linkPreview.thumbnailWidth = dadosImagem.thumbnailWidth;
            //this.linkPreview.thumbnail = this.linkPreview.thumbnailHQ;
          }
        }
    }
    */
  }
}
