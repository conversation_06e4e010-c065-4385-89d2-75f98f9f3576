import {Produto} from "../Produto";
import {MapeadorDeItemPedido} from "../../mapeadores/MapeadorDeItemPedido";
import {UnidadeMedida} from "../UnidadeMedida";
import {ProdutoTamanho} from "../templates/ProdutoTamanho";
import {ObjetoComAdicionais} from "./ObjetoComAdicionais";
import {ProdutoPizza} from "../ProdutoPizza";
import {MovimentacaoEstoqueInsumo} from "../estoque/MovimentacaoEstoqueInsumo";
import {ProdutoBrindeFidelidade} from "../ProdutoBrindeFidelidade";


export class ItemPedido extends ObjetoComAdicionais {
  id: number;
  codigo: string;
  pedido: any;
  total = 0;
  unidadeMedida: UnidadeMedida;
  sabores: Array<ItemPedido> = [];
  itemDoSabor: any;
  descricao: string;
  valorResgatado = 0;
  //qtdeGanhou = 0; //usado em cálculo de promoções para decidir se o item já foi dado como brinde

  constructor(public produto: Produto, public valor: number, public  qtde: number,
              public observacao: string, public desconto: number = 0,
              public produtoTamanho: ProdutoTamanho  = null) {
    super();

    if(produto) {
      this.unidadeMedida = produto.unidadeMedida;
    }


  }

  async valide(empresa: any, catalogo: any): Promise<any> {
    return new Promise<any>( async (resolve, reject) => {
      if(!this.produto || !this.produto.id) return resolve('Produto não informado')
      if(! (this.valor >= 0))
        return resolve('Valor do produto inválido: ' + this.produto.nome)
      if(!this.qtde) return resolve('Quantidade do produto inválido: ' + this.produto.nome)

      if(!catalogo)
        console.log("Item " + this.descricao + " do produto " + this.produto.id + " não possui catálogo informado.")

      let disponivel  = await this.produto.estaDisponivel(empresa, catalogo, this.qtde);

      if(!disponivel)
        return resolve(String(`Produto indisponível - remova do carrinho "${this.produto.nome}"`) )

      let opcaoIndisponivel = await this.obtenhaOpcaoIndisponivel();

      if(opcaoIndisponivel) return resolve(opcaoIndisponivel)

      if(this.produto.temTamanho()){
        if(!this.produtoTamanho)
          return resolve(String(`Tamanho do produto "${this.produto.nome}" não informado`))
      }

      resolve('');
    })
  }

  mapeador(): any {
    return new MapeadorDeItemPedido();
  }

  setAdicionais(adicionais: any, sabores: Array<any> = []) {
    let camposAdicionais = this.produto.camposAdicionais;

    if(sabores){
      let totalSabores = sabores.length;
      let qtdeBase = 1 / totalSabores;
      let acumulado = 0;

      sabores.forEach((sabor: any, index: number) => {
        // Para todos os sabores, exceto o último
        let qtdeSabor: number;
        if (index < totalSabores - 1) {
          qtdeSabor = Number(qtdeBase.toFixed(2));
          acumulado += qtdeSabor;
        } else {
          // O último sabor recebe o restante para garantir soma = 1
          qtdeSabor = Number((1 - acumulado).toFixed(2));
        }

        let itemPedido = new ItemPedido(sabor.produto, sabor.preco, qtdeSabor, null, 0, sabor.produtoTamanho);

        itemPedido.total =  Number( (itemPedido.valor * itemPedido.qtde).toFixed(2))
        itemPedido.descricao = itemPedido.obtenhaDescricaoProduto();

        this.sabores.push(itemPedido);

        if(sabor.produto !== this.produto.id){
          let adicionaisSabor = sabor.produto.camposAdicionais.filter((adicionalSabor: any) =>
            adicionalSabor.obrigatorio &&
            !this.produto.camposAdicionais.find((adicionalProduto: any) =>  adicionalProduto.nome === adicionalSabor.nome));

          camposAdicionais.push(...adicionaisSabor)
        }
      })
    }

    super.setAdicionais(adicionais, sabores, camposAdicionais);
  }

  calculeTotal(){

    if(!this.produto.brindeFidelidade()) {
      let totalProdutos: number =  this.produto.obtenhaPrecoTotal(this.qtde, this.produtoTamanho, this.sabores);
      let valorAdicionais: number = this.obtenhaValorAdicionais();

      if(this.produto.vendaPorPeso()){
        this.total = totalProdutos + valorAdicionais;
      } else {
        this.valor += valorAdicionais;
        this.total = totalProdutos + this.qtde * valorAdicionais;
      }
    } else {
     this.valorResgatado =  Number((this.qtde * (this.produto as ProdutoBrindeFidelidade).valorResgate).toFixed(2));
    }
  }

  obtenhaValorUnitario() {
    let precoUnitarioProduto = this.produto.obtenhaPreco(this.produtoTamanho, this.sabores)

    let valorAdicionais = this.obtenhaValorAdicionais()

    return Number((precoUnitarioProduto + valorAdicionais).toFixed(2))

  }

  obtenhaValorUnitarioProduto(){
   return this.produto.obtenhaPreco(this.produtoTamanho, this.sabores);
  }

  obtenhaValorTamanho() {
   return  this.produto.obtenhaPreco(this.produtoTamanho, this.sabores) * this.qtde;
  }

  obtenhaUnidade(){
    if(this.produto.vendaPorPeso() && this.produto.unidadeMedida)
      return this.produto.unidadeMedida.sigla.toLowerCase()

    return ''
  }

  obtenhaUnidadeTexto(){
    if(this.produto.vendaPorPeso() && this.produto.unidadeMedida)
       return String(`${this.produto.unidadeMedida.sigla.toLowerCase()} de `)

    return '';
  }

  obtenhaDescricaoProduto(resumido: boolean = false) {
    let descricao = this.produto.nome;

    if(this.produtoTamanho)
      descricao = String(`${this.produto.nome} ${this.produtoTamanho.descricao}`);

    if(this.sabores && this.sabores.length){
      let listaSabores = this.sabores.map( (sabor: ItemPedido) => sabor.produto.nome);

      let prefixo = (this.produto as ProdutoPizza).template.identificador;

      if(!resumido && listaSabores.length  > 1){
        for(let i = 0 ; i < listaSabores.length; i++)
          listaSabores[i] = String(`${(i + 1)}°: ${listaSabores[i]}`)

        descricao = String(`${prefixo} ${this.produtoTamanho.descricao} ${this.sabores.length} Sabores ( ${listaSabores.join('; ') } )`);

      } else {
        if(listaSabores.length === 1){
          descricao = String(`${prefixo} ${this.produtoTamanho.descricao}  - ${listaSabores[0]}`);
        } else {
          descricao = String(`${prefixo} ${this.produtoTamanho.descricao} ${this.sabores.length} Sabores`);
        }
      }
    }

    return descricao.toUpperCase();
  }

  obtenhaAdicionaisEnviarComoProduto(){ //
    let adicionaisImprirmir = [];

    if (this.sabores && this.sabores.length ) {

      for (let i = 0; i < this.sabores.length; i++) {
        let descricao = this.sabores.length === 1 ? this.obtenhaDescricaoProduto(true) :
                          String(`${i + 1}° sabor:  ${this.sabores[i].produto.nome}`)

        adicionaisImprirmir.push({
          qtde: 1,
          nome: this.sabores[i].produto.nome,
          descricaoTamanho: this.sabores[i].produtoTamanho.descricao,
          codigoPdv:  this.sabores[i].produtoTamanho.codigoPdv,
          sabor: true,
          descricao: descricao
        })
      }
    }

    const extras = super.obtenhaAdicionaisImprimir();

    for( let i = 0; i < extras.length; i++ ) {
      adicionaisImprirmir.push(extras[i]);
    }

    return adicionaisImprirmir;
  }

  obtenhaAdicionaisImprimir(): any {
    let adicionaisImprirmir = [];

    if (this.sabores && this.sabores.length > 1) {

      for (let i = 0; i < this.sabores.length; i++) {
        adicionaisImprirmir.push({
          qtde: 1,
          preco: 0,
          nome: this.sabores[i].produto.nome,
          descricaoTamanho: this.sabores[i].produtoTamanho.descricao,
          codigoPdv:  this.sabores[i].produtoTamanho.codigoPdv,
          aindaDisponivel: true,
          sabor: true,
          descricao: String(`${i + 1}° sabor:  ${this.sabores[i].produto.nome}`)
        })
      }
    }
    this.definicoesDosAdicionais = this.produto.camposAdicionais
    const extras = super.obtenhaAdicionaisImprimir();

    for( let i = 0; i < extras.length; i++ ) {
      adicionaisImprirmir.push(extras[i]);
    }

    return adicionaisImprirmir;
  }


  obtenhaPontosFidelidade(atividade: any){
    if(atividade.cashback != null){
      let preco = this.total, cashback = atividade.cashback;

      if(this.produtoTamanho && this.produtoTamanho.template){
        if(this.produtoTamanho.template.cashback != null)
          cashback = (this.produtoTamanho.template.cashback / 100)
      } else  if(this.produto.cashback != null){
        cashback = (this.produto.cashback / 100)
      }

      let pontos = cashback  * preco;

      return Number(pontos.toFixed(2))
    }

    let pontosPorProduto =    ( atividade.pontosGanhos || 0);

    if(this.produtoTamanho &&  this.produtoTamanho.template){
      if( this.produtoTamanho.template.pontosGanhos != null)
        pontosPorProduto =  this.produtoTamanho.template.pontosGanhos
    } else if(this.produto.pontosGanhos != null)
      pontosPorProduto = this.produto.pontosGanhos

    return pontosPorProduto * this.qtde;
  }

  manteveItens(itemPedidoAntigo: ItemPedido){
    if(this.total !== itemPedidoAntigo.total || this.qtde !== itemPedidoAntigo.qtde)
      return false;

    //todo: tem validar adicionais
    return true;
  }

  getMovimentacoes(estoqueVinculadoProduto: boolean, pedido: any, movimentacoes: any) {
    if(!estoqueVinculadoProduto){
      if(this.produto.insumo){
        let movimentacao = new MovimentacaoEstoqueInsumo(this.produto.insumo, this.qtde);
        movimentacao.setTipoSaidaProdutoDoPedido(this, pedido);
        movimentacoes.push(movimentacao)
      }

      let opcoesComInsumo: any = this.obtenhaAdicionaisImprimir().filter((opcao: any) => opcao.insumo != null );

      opcoesComInsumo.forEach((opcaoComIsumo: any) => {
        let movimentacao = new MovimentacaoEstoqueInsumo(opcaoComIsumo.insumo, this.qtde * opcaoComIsumo.qtde);
        movimentacao.setTipoSaidaOpcaoDoPedido(this, pedido, opcaoComIsumo);
        movimentacoes.push(movimentacao)
      })
    } else {
      if(this.produto.estoque){
        let movimentacao = new MovimentacaoEstoqueInsumo(null, this.qtde);

        movimentacao.setTipoSaidaProdutoDoPedido(this, pedido);
        movimentacoes.push(movimentacao)
      }
    }


  }

  private async obtenhaOpcaoIndisponivel() {
    let opcoes: any =  this.obtenhaAdicionaisImprimir();
    let erro: string;

    for (const adicional of opcoes) {
      const disponivel = adicional.estaDisponivelFn
        ? await adicional.estaDisponivelFn()
        : adicional.aindaDisponivel !== false;

      if (!disponivel) {
        erro = `Adicional "${adicional.nome}" do produto "${this.produto.nome}" não está disponível para venda`;
        break; // Encerra o loop se encontrar um adicional indisponível
      }
    }

    return erro;
  }

  obtenhaValorPagoUnitario(){
    let descontoUnitario = this.desconto / this.qtde;

    return Number((this.valor - descontoUnitario).toFixed(2));
  }

  produtoEstaNaCategoria(categorias: Array<any>): boolean {
    if(!this.produto.categoria) return false;

    return categorias.some(cat => cat.id === this.produto.categoria.id);
  }

  produtoEstaNoTamanho(produtosTemplateTamanho: any) {
    if(!this.produtoTamanho  ) return false;

    return produtosTemplateTamanho.some((tamanho: any) => tamanho.id === this.produtoTamanho.template?.id);
  }

  temAlgumProduto(produtos: Array<any>){
    if(!this.produto) return false;

    return produtos.some(prod => prod.id === this.produto.id);
  }
}
