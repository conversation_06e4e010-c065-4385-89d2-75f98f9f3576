<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="empresa">
  <resultMap id="empresaRM" type="Empresa">
    <id property="id" column="empresa_id"/>
    <result property="nome" column="empresa_nome"/>
    <result property="razaoSocial" column="empresa_razao_social"/>
    <result property="cnpj" column="empresa_cnpj"/>
    <result property="email" column="empresa_email"/>
    <result property="dominio" column="empresa_dominio"/>
    <result property="endereco" column="empresa_endereco"/>
    <result property="whatsapp" column="empresa_whatsapp"/>
    <result property="instagram" column="empresa_instagram"/>
    <result property="facebook" column="empresa_facebook"/>
    <result property="linkMaps" column="empresa_link_maps"/>
    <result property="descricao" column="empresa_descricao"/>
    <result property="tituloFotos" column="empresa_titulo_fotos"/>
    <result property="tituloDestaques" column="empresa_titulo_destaques"/>
    <result property="statusPedidoAoAceitar" column="empresa_status_pedido_ao_aceitar"/>

    <result property="qtdeVisitasRecorrente" column="empresa_qtde_visitas_recorrente"/>
    <result property="qtdeDiasEmRisco" column="empresa_qtde_dias_em_risco"/>
    <result property="qtdeDiasPerdido" column="empresa_qtde_dias_perdido"/>
    <result property="qtdeComprasVIP" column="empresa_qtde_compras_vip"/>
    <result property="ticketMedioVIP" column="empresa_ticket_medio_vip"/>
    <result property="qtdeDiasPeriodo" column="empresa_qtde_dias_periodo"/>

    <result property="logo" column="empresa_logo"/>
    <result property="capa" column="empresa_capa"/>
    <result property="favicon" column="empresa_favicon"/>
    <result property="tema" column="empresa_tema"/>
    <result property="cep" column="empresa_cep"/>
    <result property="descricaoEndereco" column="empresa_descricao_endereco"/>

    <result property="codigoCliente" column="empresa_codigo_cliente"/>

    <result property="qtdeDeMensagens" column="empresa_qtde_mensagens"/>

    <result property="meioDeEnvio" column="empresa_meio_de_envio"/>
    <result property="ativarIndicacoes" column="empresa_ativar_indicacoes"/>
    <result property="bloqueada" column="empresa_bloqueada"/>
    <result property="dark" column="empresa_dark"/>
    <result property="agruparAdicionais" column="empresa_agrupar_adicionais"/>

    <result property="nomeCategoriaDestaques" column="empresa_nome_categoria_destaques"/>
    <result property="imagemCategoriaDestaque" column="empresa_imagem_categoria_destaque"/>

    <result property="latitudeLongitude" column="empresa_lat_long"/>
    <result property="sempreReceberPedidos" column="empresa_sempre_receber_pedidos"/>
    <result property="dataBloqueioAuto" column="empresa_data_bloqueio_auto"/>

    <result property="qtdPedidosMes" column="pedidos_qtd_mes"/>
    <result property="qtdPedidos7dias" column="pedidos_qtd_7_dias"/>
    <result property="qtdPedidos24horas" column="pedidos_qtd_24_horas"/>

    <result property="pixelFacebook" column="empresa_pixel_facebook"/>
    <result property="accessTokenAPIConversoes" column="empresa_access_token_api_conversoes"/>

    <result property="analytics" column="empresa_analytics"/>
    <result property="gtm" column="empresa_gtm"/>
    <result property="gtag" column="empresa_gtag"/>

    <result property="tipoDeLoja" column="empresa_tipo_de_loja"/>
    <result property="appIos" column="empresa_app_ios"/>
    <result property="googleMapsKey" column="empresa_google_maps_key"/>

    <result property="aceitarPedidoAutomatico" column="empresa_aceitar_pedido_automatico"/>
    <result property="estoqueVinculadoProduto" column="empresa_estoque_vinculado_produto"/>
    <result property="pedidoMesaNaoIdentificado" column="empresa_pedido_mesa_nao_identificado"/>
    <result property="avisosDeMesa" column="empresa_avisos_de_mesa"/>
    <result property="identificadorMesa" column="empresa_identificador_mesa"/>

    <result property="rede" column="empresa_rede"/>
    <result property="idRede" column="empresa_rede_id"/>
    <result property="saldoMensagens" column="empresa_saldo_mensagens"/>
    <result property="fusoHorario" column="empresa_fuso_horario"/>

    <result property="cobrarTaxaServico" column="empresa_cobrar_taxa_servico"/>
    <result property="valorTaxaServico" column="empresa_valor_taxa_servico" />
    <result property="agruparCategoriasPizza" column="empresa_agrupar_categorias_pizza" />
    <result property="exibirBandeiras" column="empresa_exibir_bandeiras" />
    <result property="permitirCupomMesas" column="empresa_permitir_cupom_mesas" />
    <result property="permitirMultiplasComandasMesa" column="empresa_permitir_multiplas_comandas_mesa" />
    <result property="garcomFecharComandas" column="empresa_garcom_fechar_comandas" />

    <result property="cadastroPagamentoOnline" column="empresa_cadastro_pagamento_online" />
    <result property="enviarLinksBotao" column="empresa_enviar_links_botao" />

    <result property="usarCartaoCliente" column="empresa_usar_cartao_cliente"/>
    <result property="associarCartaoFechamentoPedido" column="empresa_associar_cartao_fechamento_pedido"/>

    <association property="empresaPrincipal"   resultMap="empresaPrincipalRM"/>

    <association property="dadosRede" resultMap="rede.redeRM"/>
    <association property="grupoDaLoja" resultMap="grupoDeLojas.grupoDaEmpresaRM"/>
    <association property="ultimaAcao"   resultMap="acaoDoContato.acaoDoContatoDTO"/>
    <association property="responsavel"   resultMap="usuario.responsavelResultMap"/>
    <association property="cardapio"   resultMap="cardapio.cardapioRM"/>
    <association property="enderecoCompleto"   resultMap="endereco.enderecoRM"/>
    <association property="configImpressao"   resultMap="configImpressao.configImpressaoRM"/>
    <association property="segmento"   resultMap="segmento.segmentoRM"/>

    <association  property="numeroWhatsapp"   resultMap="numeroWhatsapp.numeroWhatsappRM"/>
    <association  property="numeroWhatsappCampanhas" columnPrefix="campanhas_" resultMap="numeroWhatsapp.numeroWhatsappRM"/>

    <association  property="configWhatsapp"   resultMap="configWhatsapp.configWhatsappRM"/>

    <association  property="integracaoOpendelivery"   resultMap="opendelivery.opendeliveryRM"/>
    <association  property="integracaoOpendeliveryLogistica"   resultMap="opendeliverylogistica.opendeliverylogisticaRM"/>
    <association  property="integracaoUberdirect"   resultMap="uberdirect.uberdirectRM"/>

    <association  property="integracaoDelivery"   resultMap="integracaodelivery.integracaodeliveryResultMap"/>
    <association  property="integracaoFoodyDelivery"   resultMap="integracaodelivery.integracaoFoodydeliveryResultMap"/>
    <association  property="integracaoPedidoFidelidade"   resultMap="integracaoPedidoFidelidade.integracaoPedidoFidelidadeRM"/>
    <association  property="integracaoFidelidade"   resultMap="integracaoFidelidade.integracaoFidelidadeRM"/>
    <association  property="integracaoGatewayPagamento"   resultMap="integracaoGatewayPagamento.integracaoGatewayPagamentoRM"/>
    <association  property="meucardapioPay"   resultMap="meucardapioPay.meucardapioPayRM"/>
    <association  property="urlDaEmpresa" resultMap="dominioDaEmpresa.dominioDaEmpresaRM" />
    <association  property="catalogo" resultMap="catalogo.catalogoRM" />
    <association  property="modeloCatalogoDaRede" columnPrefix="modelo_"  resultMap="catalogoDaRede.catalogoDaRedeRM" />

    <association  property="temaPersonalizado" resultMap="temaPersonalizado.temaPersonalizadoRM"/>

    <collection  property="horariosFuncionamento"   resultMap="horarioDeFuncionamento.horarioDeFuncionamentoRM"/>
    <collection  property="pausasProgramadas"   resultMap="pausaProgramada.pausaProgramadaRM"/>

    <collection  property="camposAdicionais" resultMap="adicionalDeProduto.adicionalDePedidoRM"/>

    <collection  property="modulos"   resultMap="modulo.moduloRM"/>
    <collection  property="camposExtras"   resultMap="campoExtra.campoExtraRM"/>
    <collection  property="formasDeEntrega"   resultMap="formaEntregaEmpresa.formaEntregaEmpresaRM"/>
    <collection  property="categorias"   resultMap="categoria.categoriaRM"/>
    <collection  property="formasDePagamento"   resultMap="formaDePagamento.formaDePagamentoSeguraRM"/>
    <collection  property="promocoes"   resultMap="promocao.promocaoRM"/>
    <collection  property="integracoesIfood"   resultMap="integracaoIfood.integracaoIfoodRM"/>
  </resultMap>

  <resultMap id="dtoEmpresaRM" type="Empresa">
    <id property="id" column="empresa_id"/>
    <result property="nome" column="empresa_nome"/>
    <result property="razaoSocial" column="empresa_razao_social"/>
    <result property="cnpj" column="empresa_cnpj"/>
    <result property="email" column="empresa_email"/>
    <result property="dominio" column="empresa_dominio"/>
    <result property="endereco" column="empresa_endereco"/>
    <result property="whatsapp" column="empresa_whatsapp"/>
    <result property="instagram" column="empresa_instagram"/>
    <result property="facebook" column="empresa_facebook"/>
    <result property="linkMaps" column="empresa_link_maps"/>
    <result property="descricao" column="empresa_descricao"/>
    <result property="tituloFotos" column="empresa_titulo_fotos"/>
    <result property="tituloDestaques" column="empresa_titulo_destaques"/>

    <result property="qtdeVisitasRecorrente" column="empresa_qtde_visitas_recorrente"/>
    <result property="qtdeDiasEmRisco" column="empresa_qtde_dias_em_risco"/>
    <result property="qtdeDiasPerdido" column="empresa_qtde_dias_perdido"/>
    <result property="qtdeComprasVIP" column="empresa_qtde_compras_vip"/>
    <result property="ticketMedioVIP" column="empresa_ticket_medio_vip"/>
    <result property="qtdeDiasPeriodo" column="empresa_qtde_dias_periodo"/>

    <result property="logo" column="empresa_logo"/>
    <result property="capa" column="empresa_capa"/>
    <result property="tema" column="empresa_tema"/>
    <result property="cep" column="empresa_cep"/>
    <result property="descricaoEndereco" column="empresa_descricao_endereco"/>

    <result property="codigoCliente" column="empresa_codigo_cliente"/>

    <result property="qtdeDeMensagens" column="empresa_qtde_mensagens"/>

    <result property="meioDeEnvio" column="empresa_meio_de_envio"/>
    <result property="ativarIndicacoes" column="empresa_ativar_indicacoes"/>
    <result property="bloqueada" column="empresa_bloqueada"/>
    <result property="dark" column="empresa_dark"/>
    <result property="agruparAdicionais" column="empresa_agrupar_adicionais"/>

    <result property="nomeCategoriaDestaques" column="empresa_nome_categoria_destaques"/>

    <result property="latitudeLongitude" column="empresa_lat_long"/>
    <result property="sempreReceberPedidos" column="empresa_sempre_receber_pedidos"/>
    <result property="dataBloqueioAuto" column="empresa_data_bloqueio_auto"/>

    <result property="qtdPedidosMes" column="pedidos_qtd_mes"/>
    <result property="qtdPedidos7dias" column="pedidos_qtd_7_dias"/>
    <result property="qtdPedidos24horas" column="pedidos_qtd_24_horas"/>

    <result property="pixelFacebook" column="empresa_pixel_facebook"/>
    <result property="accessTokenAPIConversoes" column="empresa_access_token_api_conversoes"/>

    <result property="analytics" column="empresa_analytics"/>
    <result property="gtm" column="empresa_gtm"/>

    <result property="tipoDeLoja" column="empresa_tipo_de_loja"/>
    <result property="appIos" column="empresa_app_ios"/>

    <result property="aceitarPedidoAutomatico" column="empresa_aceitar_pedido_automatico"/>
    <result property="estoqueVinculadoProduto" column="empresa_estoque_vinculado_produto"/>
    <result property="pedidoMesaNaoIdentificado" column="empresa_pedido_mesa_nao_identificado"/>
    <result property="avisosDeMesa" column="empresa_avisos_de_mesa"/>
    <result property="identificadorMesa" column="empresa_identificador_mesa"/>

    <result property="rede" column="empresa_rede"/>
    <result property="idRede" column="empresa_rede_id"/>
    <result property="saldoMensagens" column="empresa_saldo_mensagens"/>
    <result property="fusoHorario" column="empresa_fuso_horario"/>
    <result property="exibirBandeiras" column="empresa_exibir_bandeiras" />
    <result property="permitirCupomMesas" column="empresa_permitir_cupom_mesas" />
    <result property="cadastroPagamentoOnline" column="empresa_cadastro_pagamento_online" />

    <association property="dadosRede" resultMap="rede.redeRM"/>
    <association  property="catalogo" resultMap="catalogo.catalogoRM" />
    <association  property="modeloCatalogoDaRede" columnPrefix="modelo_"  resultMap="catalogoDaRede.catalogoDaRedeRM" />
    <association  property="integracaoOpendelivery"   resultMap="opendelivery.opendeliveryRM"/>
    <association  property="integracaoDelivery"   resultMap="integracaodelivery.integracaodeliveryResultMap"/>
    <association  property="integracaoFoodyDelivery"   resultMap="integracaodelivery.integracaoFoodydeliveryResultMap"/>
    <association  property="numeroWhatsapp"   resultMap="numeroWhatsapp.numeroWhatsappRM"/>
    <association  property="numeroWhatsappCampanhas" columnPrefix="campanhas_" resultMap="numeroWhatsapp.numeroWhatsappRM"/>

    <association  property="urlDaEmpresa" resultMap="dominioDaEmpresa.dominioDaEmpresaRM" />
    <association property="enderecoCompleto"   resultMap="endereco.enderecoRM"/>

    <association  property="temaPersonalizado" resultMap="temaPersonalizado.temaPersonalizadoRM"/>

    <collection  property="horariosFuncionamento"   resultMap="horarioDeFuncionamento.horarioDeFuncionamentoRM"/>
    <collection  property="formasDeEntrega"   resultMap="formaEntregaEmpresa.formaEntregaEmpresaRM"/>
  </resultMap>

  <resultMap id="empresaPrincipalRM" type="DTOObjetoComNome">
    <id property="id" column="empresa_empresa_principal_id"/>
  </resultMap>

  <resultMap id="empresaSimplesRM" type="Empresa">
    <id property="id" column="empresa_id"/>
    <result property="nome" column="empresa_nome"/>
    <result property="dominio" column="empresa_dominio"/>

   </resultMap>

  <resultMap id="empresaReferenciaRM" type="Empresa">
    <id property="id" column="empresa_id"/>

    <result property="nome" column="empresa_nome"/>
    <result property="cnpj" column="empresa_cnpj"/>
    <result property="email" column="empresa_email"/>
    <result property="dominio" column="empresa_dominio"/>
    <result property="endereco" column="empresa_endereco"/>
    <result property="whatsapp" column="empresa_whatsapp"/>
    <result property="instagram" column="empresa_instagram"/>
    <result property="facebook" column="empresa_facebook"/>
    <result property="linkMaps" column="empresa_link_maps"/>
    <result property="descricao" column="empresa_descricao"/>
    <result property="tituloFotos" column="empresa_titulo_fotos"/>
    <result property="tituloDestaques" column="empresa_titulo_destaques"/>

    <result property="qtdeVisitasRecorrente" column="empresa_qtde_visitas_recorrente"/>
    <result property="qtdeDiasEmRisco" column="empresa_qtde_dias_em_risco"/>
    <result property="qtdeDiasPerdido" column="empresa_qtde_dias_perdido"/>
    <result property="qtdeComprasVIP" column="empresa_qtde_compras_vip"/>
    <result property="ticketMedioVIP" column="empresa_ticket_medio_vip"/>
    <result property="qtdeDiasPeriodo" column="empresa_qtde_dias_periodo"/>

    <result property="logo" column="empresa_logo"/>
    <result property="capa" column="empresa_capa"/>
    <result property="tema" column="empresa_tema"/>
    <result property="cep" column="empresa_cep"/>
    <result property="descricaoEndereco" column="empresa_descricao_endereco"/>

    <association  property="integracaoOpendelivery"   resultMap="opendelivery.opendeliveryRM"/>
    <association  property="integracaoDelivery"   resultMap="integracaodelivery.integracaodeliveryResultMap"/>
    <association  property="integracaoFoodyDelivery"   resultMap="integracaodelivery.integracaoFoodydeliveryResultMap"/>
    <collection  property="formasDePagamento"   resultMap="formaDePagamento.formaDePagamentoSeguraRM"/>
    <association  property="catalogo" resultMap="catalogo.catalogoRM" />

    <association  property="urlDaEmpresa" resultMap="dominioDaEmpresa.dominioDaEmpresaRM" />
  </resultMap>

  <resultMap id="empresaCPRM" type="EmpresaContatosPontuado">
    <id property="id" column="id"/>

    <result property="nome" column="nome"/>
    <result property="qtdePontuado" column="qtde_pontuados"/>
    <result property="qtdeContratado" column="qtde_contratado"/>
  </resultMap>

  <resultMap id="subdominioEmpresaRM" type="InformacoesDominioEmpresa">
    <id property="id" column="id"/>

    <result property="subdominio" column="informacoes_dominio_empresa_subdominio"/>
    <result property="catalogo" column="informacoes_dominio_empresa_catalogo"/>
    <result property="cardapio" column="informacoes_dominio_empresa_cardapio"/>
  </resultMap>

  <resultMap id="empresaDTORM" type="DTOObjetoComNome">
    <id property="id" column="empresa_id"/>

    <result property="nome" column="empresa_nome"/>
    <result property="dominio" column="empresa_dominio"/>
  </resultMap>


  <resultMap id="lojaTrendRM" type="LojaTrendFoods">
    <id property="id" column="id"/>

    <result property="unidade" column="unidade"/>
    <result property="sigla" column="sigla"/>
    <result property="idLoja" column="id_loja"/>
    <result property="idEmpresa" column="empresa_id"/>

  </resultMap>



  <select id="selecioneNoBancoPorLink" parameterType="map" resultMap="subdominioEmpresaRM">
    select empresa.id id, empresa.dominio informacoes_dominio_empresa_subdominio, dominio_da_empresa.url_catalogo informacoes_dominio_empresa_catalogo, dominio_da_empresa.url_cardapio informacoes_dominio_empresa_cardapio
    from empresa join dominio_da_empresa on empresa.id = dominio_da_empresa.empresa_id
    where dominio_da_empresa.hostname = #{link};
  </select>

  <select id="selecioneTodasDTO" parameterType="map" resultMap="empresaDTORM">
    select empresa.id empresa_id, empresa.nome empresa_nome, empresa.dominio empresa_dominio
    from empresa
    where removida is not true
  </select>

  <select id="selecioneDominios" parameterType="map" resultMap="empresaRM">
    select empresa.id empresa_id, empresa.nome empresa_nome, empresa.dominio empresa_dominio from empresa
    where removida is not true
  </select>

  <select id="selecioneEmpresasQueUsamCatalogo" parameterType="map" resultMap="empresaDTORM">
    select empresa.id empresa_id, empresa.nome empresa_nome, empresa.dominio empresa_dominio
        from empresa where removida is not true and catalogo_id = #{idCatalogo}
  </select>

  <select id="selecione" parameterType="map" resultMap="empresaRM" prefix="true">
    select *
      from empresa   join catalogo on empresa.catalogo_id = catalogo.id
        left join numero_whatsapp on numero_whatsapp.empresa_id = empresa.id and numero_whatsapp.principal is true
        left join numero_whatsapp campanhas_numero_whatsapp on campanhas_numero_whatsapp.id = empresa.numero_whatsapp_campanhas_id and campanhas_numero_whatsapp.removido is false
        <choose>
          <when test="rede != null"></when>
          <otherwise>
            left join (select max(id) id, empresa_id from acao_contato group by empresa_id) acoes on(acoes.empresa_id = empresa.id)
            left join acao_contato on(acao_contato.id = acoes.id)
          </otherwise>
        </choose>
        left join horario_funcionamento horario   on horario.empresa_id = empresa.id
        left join adicional_produto adicional_pedido on  adicional_pedido.empresa_id = empresa.id  and  adicional_pedido.excluido is not true
                                                       and  adicional_pedido.entidade = 'pedido' and  adicional_pedido.produto_id is null
        left join opcao_adicional_produto on  adicional_pedido.id = opcao_adicional_produto.adicional_produto_id
                                                    and opcao_adicional_produto.excluido is not true
        left join usuario on usuario.id = empresa.responsavel_id
        left join rede on rede.empresa_id = empresa.id
        left join config_impressao on config_impressao.id = empresa.config_impressao_id
        left join impressora on impressora.config_impressao_id = config_impressao.id
        left join empresa_modulo em on em.empresa_id = empresa.id
        left join modulo on modulo.id = em.modulo_id
        left join cardapio on cardapio.empresa_id = empresa.id
        left join empresa_campo_extra on empresa_campo_extra.empresa_id = empresa.id
        left join campo_extra on campo_extra.id = campo_extra_id
        left join endereco on endereco.id = empresa.endereco_id
        left join cidade on cidade.id = endereco.cidade_id
        left join estado on estado.id = cidade.estado_id
        left join integracao_fidelidade on integracao_fidelidade.empresa_id = empresa.id
        left join integracao_pedido_fidelidade on integracao_pedido_fidelidade.empresa_id = empresa.id
        left join integracao_opendelivery opend on opend.empresa_id = empresa.id
        left join cliente_api on (opend.cliente_id = cliente_api.id)
        left join integracao_delivery on integracao_delivery.empresa_id  = empresa.id and integracao_delivery.sistema != 'foodydelivery'
        left join integracao_delivery integracao_foodydelivery on integracao_foodydelivery.empresa_id  = empresa.id and integracao_foodydelivery.sistema = 'foodydelivery'
        left join integracao_opendelivery_logistica opend_logistica on opend_logistica.empresa_id = empresa.id
        left join integracao_uberdirect uberdirect on uberdirect.empresa_id = empresa.id
        left join integracao_ifood on integracao_ifood.empresa_id = empresa.id
        left join plano on plano.id = integracao_pedido_fidelidade.plano_id
        left join tipo_de_pontuacao  on tipo_de_pontuacao.id = plano.id_tipo_de_pontuacao
        left join atividade on atividade.id = integracao_pedido_fidelidade.atividade_id
        left join segmento on empresa.segmento_id = segmento.id
        left join dominio_da_empresa on dominio_da_empresa.empresa_id = empresa.id
        left join pausa_programada on pausa_programada.empresa_id = empresa.id and pausa_programada.cancelada is not true and
                  (pausa_programada.data_fim is null || datediff(pausa_programada.data_fim, now())   >=  0 )
        left join config_whatsapp on config_whatsapp.empresa_id = empresa.id
        left join catalogo_da_rede  modelo_catalogo_da_rede on modelo_catalogo_da_rede_id = modelo_catalogo_da_rede.id
        left join catalogo modelo_catalogo on modelo_catalogo.id  = modelo_catalogo_da_rede.catalogo_id
        left join empresa_integracao_gateway_pagamento eigat on eigat.empresa_id = empresa.id
        left join integracao_gateway_pagamento on integracao_gateway_pagamento.id = integracao_gateway_pagamento_id
        left join tema_personalizado on tema_personalizado.empresa_id = empresa.id and empresa.tema_personalizado_id = tema_personalizado.id
        left join meucardapio_pay on meucardapio_pay.empresa_id = empresa.id
        left join contrato_meucardapio_pay on contrato_meucardapio_pay.id = meucardapio_pay.contrato_meucardapio_pay_id
        left join grupo_de_lojas_empresa on grupo_de_lojas_empresa.empresa_id = empresa.id
        left join grupo_de_lojas on grupo_de_lojas.id = grupo_de_lojas_empresa.grupo_de_lojas_id

    where empresa.removida is not true
    <choose>
      <when test="id != null">
        and empresa.id = #{id}
      </when>
      <when test="idRede != null">
        and empresa.rede_id = #{idRede}
      </when>
      <when test="nome != null">
        and empresa.nome = #{nome}
      </when>
      <when test="dominio != null">
        and empresa.dominio = #{dominio}
      </when>
      <when test="idCatalogo != null">
        and empresa.catalogo_id = #{idCatalogo}
        <if test="ecletica">
              and exists (select 1 from integracao_delivery where integracao_delivery.empresa_id = empresa.id and  sistema = 'ecletica' )
        </if>
      </when>

      <when test="idLojaIfood">
        and exists (select 1 from integracao_ifood where integracao_ifood.empresa_id = empresa.id and id_loja = #{idLojaIfood})
      </when>

      <when test="rede != null">
        and exists
          (select 1 from integracao_delivery
                where integracao_delivery.empresa_id = empresa.id and sistema = 'ecletica' and
                          rede = #{rede} and loja = #{loja}  )
      </when>
      <when test="instalacaoId != null">
        and integracao_gateway_pagamento.instalacao_id = #{instalacaoId}
      </when>

      <when test="idLojaTuna != null">
        and meucardapio_pay.id_loja  = #{idLojaTuna}
      </when>

      <when test="cnpj != null">
        and empresa.cnpj = #{cnpj}
      </when>

      <when test="tunaCriarAuto">
        and empresa.cnpj is not null and empresa.cnpj != '' and empresa.cnpj != '08150325000162'
          and meucardapio_pay.id is null
            order by empresa.id desc
      </when>


      <when test="diasBloqueio">
        and  datediff(now(),data_bloqueio_auto) >  #{diasBloqueio}
        order by  datediff(now(),data_bloqueio_auto) desc
      </when>
      <otherwise>
        order by acao_contato.horario desc;
      </otherwise>
    </choose>
  </select>

  <select id="selecioneEmpresasRede" parameterType="map" resultMap="dtoEmpresaRM" prefix="true">
    select *
    from (
      select empresa.* from empresa
      <if test="idGrupo != null">
      inner join grupo_de_lojas_empresa on(grupo_de_lojas_empresa.empresa_id = empresa.id)
      inner join grupo_de_lojas on(grupo_de_lojas.id = grupo_de_lojas_empresa.grupo_de_lojas_id)
      </if>
      left join endereco on endereco.id = empresa.endereco_id
      left join cidade on cidade.id = endereco.cidade_id
      left join estado on estado.id = cidade.estado_id
    where
      empresa.removida is not true
      <if test="idGrupo != null">
        and grupo_de_lojas.id = #{idGrupo}
        and grupo_de_lojas_empresa.listar_loja is true
      </if>
      <if test="redeDaEmpresa != null">
        and empresa.rede = #{redeDaEmpresa}
      </if>
      <if test="redes">
        and (empresa.rede_id in (
        <foreach item="item" collection="redes" open="" separator="," close="">
          #{item.id}
        </foreach>
        ))
      </if>

      <if test="kohala">
        and empresa.id in (884, 820)
      </if>

      <if test="sistemaIntegrado">
        and exists (select 1 from integracao_delivery where empresa_id = empresa.id and sistema = #{sistemaIntegrado}  )
      </if>

      <if test="lojasChinaEcletica">
        and exists (select 1 from integracao_delivery where empresa_id = empresa.id and unidade_china is not null and sistema = 'ecletica' )
      </if>

      <if test="lojasChinaGcom">
        and exists (select 1 from integracao_delivery where empresa_id = empresa.id and unidade_china is not null and sistema = 'gcom' )
      </if>

      <if test="lojasGendaiEcletica">
        and exists (select 1 from integracao_delivery where empresa_id = empresa.id and unidade_gendai is not null  and sistema = 'ecletica')
      </if>

      <if test="lojasGendaiGcom">
        and exists (select 1 from integracao_delivery where empresa_id = empresa.id and unidade_gendai is not null  and sistema = 'gcom')
      </if>


      <if test="idModeloCatalogo">
         and modelo_catalogo_da_rede_id = #{idModeloCatalogo}
      </if>

      <if test="q != null">
        and empresa.nome like #{q}
      </if>
      <if test="estado != null">
        and estado.nome = #{estado.nome}
      </if>
      <if test="grupoDeEmpresas != null">
        and grupo_de_lojas.nome = #{grupoDeEmpresas}
      </if>
      <if test="orderNome != null">
        order by empresa.nome
      </if>
      <if test="inicio != null">
        limit #{inicio}, #{total}
      </if>
    ) empresa
    join catalogo on empresa.catalogo_id = catalogo.id

    left join endereco on endereco.id = empresa.endereco_id
    left join cidade on cidade.id = endereco.cidade_id
    left join estado on estado.id = cidade.estado_id
    left join integracao_pedido_fidelidade on integracao_pedido_fidelidade.empresa_id = empresa.id
    left join integracao_delivery on integracao_delivery.empresa_id  = empresa.id
    left join dominio_da_empresa on dominio_da_empresa.empresa_id = empresa.id
    left join catalogo_da_rede  modelo_catalogo_da_rede on modelo_catalogo_da_rede_id = modelo_catalogo_da_rede.id
    left join catalogo modelo_catalogo on modelo_catalogo.id  = modelo_catalogo_da_rede.catalogo_id

    <if test="!buscaRasa">
      left join numero_whatsapp on numero_whatsapp.empresa_id = empresa.id and numero_whatsapp.principal is true
      left join numero_whatsapp campanhas_numero_whatsapp on campanhas_numero_whatsapp.id = empresa.numero_whatsapp_campanhas_id and campanhas_numero_whatsapp.removido is false
      left join horario_funcionamento horario   on horario.empresa_id = empresa.id
      left join empresa_formas_de_entrega  on empresa_formas_de_entrega.empresa_id = empresa.id
      left join forma_de_entrega on forma_de_entrega.id = forma_de_entrega_id
      left join raio_de_cobranca on raio_de_cobranca.empresa_forma_de_entrega_id = empresa_formas_de_entrega.id
      left join alcance on alcance.forma_de_entrega_empresa_id = empresa_formas_de_entrega.id
      left join zona_de_entrega on zona_de_entrega.empresa_forma_de_entrega_id = empresa_formas_de_entrega.id
    </if>

    <choose>
      <when test="redes">
        and (empresa.rede in (
        <foreach item="item" collection="redes" open="" separator="," close="">
          #{item.id}
        </foreach>
        ))
      </when>
      <when test="redeDaEmpresa != null">
        and empresa.rede = #{redeDaEmpresa}
      </when>
    </choose>
    <if test="orderNome != null">
      order by empresa.nome
    </if>
  </select>

  <select id="selecioneTotalEmpresasRede" parameterType="map" resultType="int">
    select count(*) from empresa where
    empresa.removida is not true
    and empresa.rede = #{redeDaEmpresa}
    <if test="q != null">
      and empresa.nome like #{q}
    </if>
  </select>

  <select id="selecioneBusca" parameterType="map" resultMap="empresaRM" prefix="true">
      select * from empresa
            left join endereco on empresa.endereco_id = endereco.id
            left join cidade on cidade.id = endereco.cidade_id
            left join estado on estado.id = cidade.estado_id
            left join ultima_acao on empresa.id = empresa_id
            left join dominio_da_empresa on dominio_da_empresa.empresa_id = empresa.id
        where   empresa.removida is not true
          <choose>

            <when test="naoUsaram">
              and ultima_acao.id is null
            </when>

            <when test="oportunidades">
               and exists  (select 1 from contrato join assinatura on assinatura.id = assinatura_id where contrato.empresa_id = empresa.id and data_primeiro_pagamento is null)
            </when>

            <when test="bloqueadas">
              and ( bloqueada is true or   DATEDIFF(now(), data_bloqueio_auto)    >=  0)
            </when>

            <when test="ativas">
              and (bloqueada is not true  and   DATEDIFF(data_bloqueio_auto, now())   > 0)
            </when>

            <when test="proximosBloqueios">
              and DATEDIFF(data_bloqueio_auto, now())   > 0 and  5 >= DATEDIFF(data_bloqueio_auto, now())
            </when>
          </choose>

          <if test="termo">
            and (empresa.nome like #{termo} or empresa.dominio like #{termo})
          </if>
          <if test="id">
            and  empresa.id = #{id}
          </if>

          <if test="estadoId">
            and estado.id = #{estadoId}
          </if>
          <if test="dataCadastro">
            and exists  (select 1 from contrato join assinatura on assinatura.id = assinatura_id where contrato.empresa_id = empresa.id and DATEDIFF(data_criacao,#{dataCadastro}) >= 0 )
          </if>

         <if test="vencimentoDiferente">
           and exists  (select 1 from contrato join assinatura on assinatura.id = assinatura_id where contrato.empresa_id = empresa.id and  data_proximo_vencimento != data_vencimento )
         </if>


         <choose>
           <when test="proximosBloqueios">
               order by data_bloqueio_auto
           </when>

           <when test="bloqueadas">
             order by data_bloqueio_auto desc
           </when>

           <when test="ordernaMaisNovas">
             order by empresa.id desc
           </when>

           <otherwise>
             order by ultima_acao.id desc limit 100
           </otherwise>
         </choose>


  </select>

  <select id="selecioneComResumo" parameterType="map" resultMap="empresaRM" prefix="true">
    select
      empresa.*,
      endereco.*, cidade.*, estado.*,
      acao_contato.*,    pedidos.*

    from  empresa left join empresa_pedidos_resumo pedidos on pedidos.empresa_id  = empresa.id
                  left join endereco on empresa.endereco_id = endereco.id
                  left join cidade on cidade.id = endereco.cidade_id
                  left join estado on estado.id = cidade.estado_id
                  left join ultima_acao   on empresa.id = ultima_acao.empresa_id
                  left join acao_contato on acao_contato.id = ultima_acao.id
                where
              <choose>

                <when test="naoUsaram">
                  ultima_acao.id is null
                </when>

                <when test="bloqueadas">
                   ( bloqueada is true or   DATEDIFF(now(), data_bloqueio_auto)    >=  0)
                </when>

                <when test="ativas">
                   (bloqueada is not true  and   DATEDIFF(data_bloqueio_auto, now())   > 0)
                </when>

              </choose>

              <if test="dataCadastro">
                and exists  (select 1 from contrato join assinatura on assinatura.id = assinatura_id where contrato.empresa_id = empresa.id and DATEDIFF(data_criacao,#{dataCadastro}) >= 0 )
              </if>

              <if test="termo">
                and  (empresa.nome like #{termo} or empresa.dominio like #{termo})
              </if>

              <if test="estadoId">
                and exists (select 1 from
                        endereco join cidade on cidade.id = cidade_id join estado on estado.id = estado_id
                            where endereco.id = endereco_id and estado.id = #{estadoId} )
              </if>



      order by ultima_acao.id desc limit 100


  </select>

  <select id="selecioneTodas" parameterType="map" resultMap="empresaRM" prefix="true">
     select * from empresa where capa is not null
  </select>

  <select id="existe" parameterType="map" resultType="int">
    select count(*) total from empresa e
          where
              <choose>
                <when test="dominio"> e.dominio = #{dominio}
                     <if test="id"> and e.id != #{id} </if>
                </when>
                <when test="cnpj">  e.cnpj = #{cnpj}
                  <if test="id"> and e.id != #{id} </if>
                </when>
                <when test="id"> e.id = #{id}</when>
              </choose>


  </select>

  <select id="selecioneTotal" parameterType="map" resultType="int">
    select count(*) total from empresa e where e.dominio = #{dominio};
  </select>

  <select id="selecionePing" parameterType="map" resultType="int">
    select 1 from empresa limit 1
  </select>

  <select id="selecioneLimiteContatos" parameterType="map" resultMap='empresaCPRM'>
    select * from empresa_contatos_pontuados  where id = #{idEmpresa}
  </select>

  <select id="listeLojasChinas" resultType="String" parameterType="map">
     select nome from filial_china  order by nome
  </select>

  <select id="listeLojasGendais" resultType="String" parameterType="map">
     select nome from filial_gendai order by nome
  </select>

  <select id="selecioneUnidadesChinas" resultMap="lojaTrendRM" parameterType="map">
    select uuid() id, chinainbox.*  from chinainbox
  </select>

  <select id="selecioneUnidadesGendais" resultMap="lojaTrendRM" parameterType="map">
    select uuid() id, gendai.*  from gendai
  </select>

  <insert id="insira" parameterType="map">
    insert into empresa (nome, dominio, endereco, endereco_id, whatsapp, instagram, facebook, descricao, titulo_fotos, titulo_destaques, link_maps,
      meio_de_envio, email, cnpj, cep, lat_long, sempre_receber_pedidos, responsavel_id, descricao_endereco, catalogo_id, avisos_de_mesa, rede_id,
      rede, agrupar_categorias_pizza,exibir_bandeiras, razao_social, usar_cartao_cliente,permitir_multiplas_comandas_mesa, associar_cartao_fechamento_pedido, cadastro_pagamento_online) values
    (#{nome}, #{dominio}, #{endereco}, #{enderecoCompleto.id},  #{whatsapp}, #{instagram}, #{facebook}, #{descricao}, #{tituloFotos}, #{tituloDestaques}, #{linkMaps},
      #{meioDeEnvio}, #{email} , #{cnpj}, #{cep}, #{latitudeLongitude}, #{sempreReceberPedidos}, #{responsavel.id}, #{descricaoEndereco},
    #{catalogo.id}, false, #{idRede}, #{rede}, #{agruparCategoriasPizza}, #{exibirBandeiras}, #{razaoSocial}, #{usarCartaoCliente},#{permitirMultiplasComandasMesa},#{associarCartaoFechamentoPedido},false)

  </insert>


  <update id="atualizeNomeCategoriaDestaques" parameterType="map">
    update empresa set nome_categoria_destaques = #{nomeCategoriaDestaques},
     imagem_categoria_destaque = #{imagemCategoriaDestaque}
      where id = #{id}
  </update>

  <update id="atualizeExibirBandeiras" parameterType="map">
    update empresa set exibir_bandeiras = #{exibirBandeiras} where id = #{id}
  </update>

  <update id="atualize" parameterType="map">
    update  empresa set nome = #{nome},
            endereco = #{endereco},
            endereco_id = #{enderecoCompleto.id},
            dominio = #{dominio},
            nome = #{nome},
            razao_social = #{razaoSocial},
            whatsapp = #{whatsapp},
            instagram = #{instagram},
            facebook = #{facebook},
            lat_long = #{latitudeLongitude},
            ativar_indicacoes = #{ativarIndicacoes},
            nome_categoria_destaques = #{nomeCategoriaDestaques},
            link_maps = #{linkMaps},
            titulo_fotos = #{tituloFotos},
            titulo_destaques = #{tituloDestaques},
            descricao = #{descricao},
            meio_de_envio = #{meioDeEnvio},
            email = #{email},
            cnpj = #{cnpj},
            cep = #{cep},
            descricao_endereco = #{descricaoEndereco},
            pixel_facebook = #{pixelFacebook},
            access_token_api_conversoes = #{accessTokenAPIConversoes},
            gtm = #{gtm},
            gtag = #{gtag},
            identificador_mesa = #{identificadorMesa},
            rede_id = #{idRede},
            rede = #{rede},
            responsavel_id = #{responsavel.id},
            permitir_multiplas_comandas_mesa = #{permitirMultiplasComandasMesa},
            usar_cartao_cliente = #{usarCartaoCliente}
    where id = #{id}
  </update>

  <update id="atualizeEnviarLinksBotao" parameterType="map">
    update empresa set
    enviar_links_botao = #{enviarLinksBotao}
    where id = #{id};
  </update>

  <update id="atualizeSaldoMensagens" parameterType="map">
    update  empresa set
    saldo_mensagens = #{saldoMensagens}
    where id = #{id};
  </update>

  <update id="atualizeSempreReceberPedidos" parameterType="map">
    update  empresa set
      sempre_receber_pedidos = #{sempreReceberPedidos}
      where id = #{id};
  </update>
  <update id="atualizeFusoHorario" parameterType="map">
    update  empresa set
      fuso_horario = #{fusoHorario}
      where id = #{id};
  </update>

  <update id="atualizeImprimirTXT" parameterType="map">
    update empresa set
      imprimir_txt = #{imprimirTXT}
      where id = #{id}
  </update>


  <update id="atualizeCapa" parameterType="map">
    update empresa set capa = #{capa} where id = #{id}
  </update>

  <update id="atualizeFavicon" parameterType="map">
    update empresa set favicon = #{favicon} where id = #{id}
  </update>

  <update id="atualizeTema" parameterType="map">
    update empresa set
    tema = #{tema},
    tema_personalizado_id = #{temaPersonalizado.id}
     where id = #{id}
  </update>

  <update id="atualizeResponsavel" parameterType="map">
    update empresa set   responsavel_id = #{responsavel.id} where id = #{id}
  </update>

  <update id="atualizeLogo" parameterType="map">
    update empresa set logo = #{logo} where id = #{id}
  </update>

  <update id="atualizeMeioEnvio" parameterType="map">
    update empresa set meio_de_envio = #{meioDeEnvio} where id = #{id}
  </update>

  <update id="atualizeBloqueada" parameterType="map">
    update empresa
      set bloqueada = #{bloqueada}, data_bloqueio_auto =  #{dataBloqueioAuto}
          where id = #{id}
  </update>

  <update id="atualizeCatalogo" parameterType="map">
    update empresa
    set catalogo_id = #{catalogo.id}
    where id = #{id}
  </update>
  <update id="atualizeModeloCatalogo" parameterType="map">
    update empresa
    set modelo_catalogo_da_rede_id = #{modeloCatalogoDaRede.id}
        where id = #{id}
  </update>

  <update id="atualizeSegmento" parameterType="map">
    update empresa
      set segmento_id = #{segmento.id},
        agrupar_categorias_pizza = #{agruparCategoriasPizza}
          where id = #{id}
  </update>

  <update id="atualizeCliente" parameterType="map">
    update empresa set codigo_cliente = #{codigoCliente}   where id = #{id};
  </update>

  <update id="remova" parameterType="map">
    update empresa set removida = true, data_remocao = now()  where id = #{id};
  </update>

  <update id="atualizeRemovido" parameterType="map">
    update empresa set removida = false  where id = #{id};
  </update>

  <select id="selecionarEmpresaAtivas">
    select t.*, datediff(now(),t.data_inicio) dias_ativo
        from ( select e.nome, count(*) total ,min(data_ativacao) data_inicio
                  from contato c join empresa e on e.id = c.empresa_id where removida is not true and c.status = 1
                      group by empresa_id )  t
          where  datediff(now(),t.data_inicio) > 30 order by t.data_inicio;

  </select>

  <update id="atualizeConfiguracoesClassificacoes" parameterType="map">
    update  empresa set
    qtde_visitas_recorrente = #{qtdeVisitasRecorrente},
    qtde_dias_em_risco = #{qtdeDiasEmRisco},
    qtde_dias_perdido = #{qtdeDiasPerdido},
    qtde_dias_periodo = #{qtdeDiasPeriodo},
    qtde_compras_vip = #{qtdeComprasVIP},
    ticket_medio_vip = #{ticketMedioVIP}
    where id = #{id}
  </update>

  <update id="atualizeConfiguracoesMesas" parameterType="map">
    update  empresa set
      pedido_mesa_nao_identificado = #{pedidoMesaNaoIdentificado},
      identificador_mesa = #{identificadorMesa},
      cobrar_taxa_servico = #{cobrarTaxaServico},
       permitir_cupom_mesas = #{permitirCupomMesas},
      valor_taxa_servico = #{valorTaxaServico},
       avisos_de_mesa = #{avisosDeMesa},
       usar_cartao_cliente = #{usarCartaoCliente},
       associar_cartao_fechamento_pedido = #{associarCartaoFechamentoPedido},
       permitir_multiplas_comandas_mesa = #{permitirMultiplasComandasMesa},
       garcom_fechar_comandas = #{garcomFecharComandas}
        where id = #{id}
  </update>





  <update id="atualizeScriptsMarketing" parameterType="map">
    update empresa set
      pixel_facebook = #{pixelFacebook},
      access_token_api_conversoes = #{accessTokenAPIConversoes},
      gtm = #{gtm},
      gtag = #{gtag},
      analytics = #{analytics}
    where id = #{id}
  </update>

  <update id="atualizeConfigImpressao" parameterType="map">
    update empresa set
      config_impressao_id = #{configImpressao.id}
    where id = #{id}
  </update>

  <update id="atualizeConfigModulosLoja" parameterType="map">
    update empresa set
      tipo_de_loja = #{tipoDeLoja},
        aceitar_pedido_automatico = #{aceitarPedidoAutomatico},
    status_pedido_ao_aceitar = #{statusPedidoAoAceitar},
    estoque_vinculado_produto = #{estoqueVinculadoProduto},
    app_ios = #{appIos}, google_maps_key = #{googleMapsKey}
    where id = #{id}

  </update>

  <update id="atualizeLocalizacao" parameterType="map">
    update empresa set
                     lat_long = #{latitudeLongitude}
    where id = #{id}

  </update>

  <update id="atualizeAceitarPedido" parameterType="map">
    update empresa set
    aceitar_pedido_automatico = #{aceitarPedidoAutomatico}
    where id = #{id}

  </update>

  <update id="removaConfigImpressao" parameterType="map">
    update empresa set
      config_impressao_id = null
    where id = #{id}
  </update>

  <update id="removaModulo"  parameterType="map">
    delete from empresa_modulo where empresa_id = #{empresa} and modulo_id = #{modulo}
  </update>

  <update id="removaCampos"  parameterType="map">
    delete from empresa_campo_extra where empresa_id = #{empresa} ;
  </update>

  <update id="removaFormasRetiradas"  parameterType="map">
    delete from empresa_forma_de_entrega where empresa_id = #{empresa} ;
  </update>

  <insert id="insiraModulo">
     insert into empresa_modulo (empresa_id, modulo_id) values (#{empresa} , #{modulo})
  </insert>

  <insert id="insiraCampoExtra">
     insert into empresa_campo_extra (empresa_id, campo_extra_id, opcional) values (#{empresa} , #{campo}, #{opcional})
  </insert>

  <update id="selecineCorrirBloqueiosAuto">
    select empresa.id,empresa.nome,data_bloqueio_auto,data_vencimento,data_ultimo_pagamento
            from empresa join contrato on contrato.empresa_id = empresa.id join assinatura on assinatura.id= assinatura_id
            where empresa.removida is not true and datediff(data_bloqueio_auto,data_vencimento) &lt;= 0 order by data_bloqueio_auto;


    select empresa.id,empresa.nome,data_bloqueio_auto,data_vencimento,datediff(data_bloqueio_auto,data_vencimento) dias_block,data_ultimo_pagamento
            from empresa join contrato on contrato.empresa_id = empresa.id join assinatura on assinatura.id= assinatura_id join plano_empresarial on plano_id = plano_empresarial.id
        where empresa.removida is not true and datediff(data_bloqueio_auto,data_vencimento) > 7 and data_ultimo_pagamento is not null order by data_bloqueio_auto;


  </update>

  <update id="corrirBloqueiosAuto">
    update empresa join contrato on contrato.empresa_id = empresa.id join
            assinatura on assinatura.id= assinatura_id set data_bloqueio_auto =  date_add(data_vencimento,INTERVAL 7 DAY)
            where empresa.removida is not true  and 6 >= datediff(data_bloqueio_auto,data_vencimento)

  </update>

  <update id="recalculeResumoPedidos24s" parameterType="map">
    update empresa_pedidos_resumo  join
      ( select count(*) qtde, empresa_id from pedido
      where id > 668323 and horario_atualizacao >= now() - interval 1 day
      group by empresa_id
      ) pedidos_24_horas on empresa_pedidos_resumo.empresa_id =pedidos_24_horas.empresa_id


      set empresa_pedidos_resumo.qtd_24_horas = pedidos_24_horas.qtde;

  </update>

  <update id="recalculeResumoPedidos7d" parameterType="map">
    update empresa_pedidos_resumo  join
      (select count(*) qtde, empresa_id from pedido
      where id > 668323 and  horario_atualizacao >= curdate() - interval 7 day
      group by empresa_id) pedidos_7_dias on empresa_pedidos_resumo.empresa_id =pedidos_7_dias.empresa_id

      set empresa_pedidos_resumo.qtd_7_dias = pedidos_7_dias.qtde;
  </update>

  <update id="recalculeResumoPedidosMes" parameterType="map">
    update empresa_pedidos_resumo  join
      ( select count(*) qtde, empresa_id from pedido
      where id > 668323 and month(horario_atualizacao) = month(curdate()) and year(horario_atualizacao) = year(curdate())
      group by empresa_id) pedidos_mes on empresa_pedidos_resumo.empresa_id =pedidos_mes.empresa_id

      set empresa_pedidos_resumo.qtd_mes = pedidos_mes.qtde;

  </update>

  <update id="insiraResumoPedidosNovos" parameterType="map">
    insert into empresa_pedidos_resumo(empresa_id)
         select id from empresa where not exists (select 1 from empresa_pedidos_resumo where empresa_id = empresa.id);

  </update>

  <select id="selecioneCuponsCbis">
    select empresa.id, empresa.nome, contato.nome, contato.telefone,cupom.codigo, count(*) qtde,   sum(pedido.valor+pedido.taxa_entrega) faturamento
       from cupom join empresa on empresa.id = cupom.empresa_id join pedido on pedido.empresa_id = empresa.id and cupom.id = cupom_id
               join contato on contato.id = contato_id
    where empresa.rede_id = 1 and  cupom.codigo in ('FRETE', 'FRETEWHATS', 'SEMFRETE','FRETEZERO','FRETEGRATIS', 'NOFRETE', 'GANHEIFRETE','FRETEFREE','ZEROFRETE','CHINA','SSAZERO')
          and 4 >=  pedido.status  and   pedido.horario_atualizacao > '2022-03-01 00:00:00' and  '2022-03-31 23:59:59' > horario_atualizacao
          group by empresa.id, cupom.codigo,contato.id order by empresa.id,cupom.codigo
            INTO OUTFILE '/var/lib/mysql-files/contatoscupons03.csv' FIELDS TERMINATED BY ',';


    select empresa.id, empresa.nome,cupom.codigo, count(*) qtde,   sum(pedido.valor+pedido.taxa_entrega) faturamento
    from cupom join empresa on empresa.id = cupom.empresa_id join pedido on pedido.empresa_id = empresa.id and cupom.id = cupom_id
    where empresa.rede_id = 1 and  cupom.codigo in ('FRETE', 'FRETEWHATS', 'SEMFRETE','FRETEZERO','FRETEGRATIS', 'NOFRETE', 'GANHEIFRETE','FRETEFREE','ZEROFRETE','CHINA','SSAZERO')
      and 4 >=  pedido.status  and   pedido.horario_atualizacao > '2022-03-01 00:00:00' and  '2022-03-31 23:59:59' > horario_atualizacao
    group by empresa.id, cupom.codigo order by empresa.id,cupom.codigo;


    select  pedido.horario,cupom.codigo,telefone
        from cupom join empresa on empresa.id = cupom.empresa_id join pedido on  cupom.id = cupom_id
                   join contato on contato.id = contato_id
           where empresa.rede_id = 1     and 4 >=  pedido.status  and
                 pedido.horario > '2022-01-01 00:00:00' and  '2022-01-31 23:59:59' > horario
                 order by horario INTO OUTFILE '/var/lib/mysql-files/cuponsjan.csv' FIELDS TERMINATED BY ',';

  </select>

  <select id="selecioneVendasCbis">
      select empresa.id, empresa.nome,  count(*) qtde,  sum(pedido.valor+pedido.taxa_entrega) faturamento
      from empresa  join pedido on pedido.empresa_id = empresa.id
      where empresa.rede_id = 1 and mesa_id is null  and 4 >=  pedido.status  and
         pedido.horario_atualizacao > '2022-03-01 00:00:00' and  '2022-03-31 23:59:59' > horario_atualizacao
      group by empresa.id  order by  count(*) desc;


    select empresa.id, empresa.nome,  count(*) qtde,  sum(pedido.valor+pedido.taxa_entrega) faturamento
    from empresa  join pedido on pedido.empresa_id = empresa.id
    where empresa.rede_id = 1 and mesa_id is not null  and 4 >=  pedido.status  and
    pedido.horario_atualizacao > '2022-03-01 00:00:00' and  '2022-03-31 23:59:59' > horario_atualizacao
    group by empresa.id  order by  count(*) desc;

  </select>

  <select id="selecioneNovosClientesCbis">
     select contato.id,contato.nome,empresa.id,empresa.nome
            from contato join empresa on empresa.id = empresa_id
                where empresa.rede_id = 1 and data_cadastro > '2022-03-01' and data_ultimo_pedido is not null
            order by  empresa.id, contato.nome INTO OUTFILE '/var/lib/mysql-files/novoclientes032022.csv' FIELDS TERMINATED BY ',';
  </select>

  <select id="selecioneBloqueadasRemoover">
    select id,data_bloqueio_auto, datediff(now(),data_bloqueio_auto) from empresa
      where    removida is not true
  </select>

  <update id="atualizarNumeroWhatsappCampanhas" parameterType="map">
    UPDATE empresa
    SET numero_whatsapp_campanhas_id = #{numeroWhatsappCampanhasId}
    WHERE id = #{id}
  </update>

</mapper>
