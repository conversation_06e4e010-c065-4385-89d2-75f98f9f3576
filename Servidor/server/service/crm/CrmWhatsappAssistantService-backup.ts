 import { ChatGPTService } from "../ia/ChatGPTService";
import { promises as fs } from 'fs';
import * as path from 'path';

interface SugestaoParams {
  telefone: string;
  mensagens: any[];
  faseSpin: 'situacao' | 'problema' | 'implicacao' | 'necessidade' | 'auto';
  produto: string;
  tomConversa: 'formal' | 'informal' | 'tecnico';
  contato?: any;
  // Novos parâmetros para melhor contexto
  isNovoLead?: boolean;
  segmento?: string;
  tamanhoEmpresa?: 'micro' | 'pequena' | 'media' | 'grande';
  historicoInteracoes?: number;
  horarioMensagem?: 'manha' | 'tarde' | 'noite';
  motivoContato?: 'inbound' | 'outbound' | 'retorno';
}

interface ResultadoSugestao {
  sugestoes: Array<{
    texto: string;
    confianca: number;
    faseSpin: string;
    timestamp: Date;
  }>;
  faseSugerida?: string;
  observacoes?: string;
}

export class CrmWhatsappAssistantService {
  private chatGPTService: ChatGPTService;
  private beneficiosMeuCardapio: string | null = null;

  constructor() {
    this.chatGPTService = new ChatGPTService();
  }

  /**
   * Carrega o documento de benefícios do Meu Cardápio
   */
  private async carregarBeneficiosMeuCardapio(): Promise<string> {
    try {
      if (!this.beneficiosMeuCardapio) {
        const filePath = path.join(__dirname, '../../../docs/meu-cardapio-beneficios.md');
        console.log('[CrmWhatsappAssistantService] Tentando carregar benefícios de:', filePath);
        
        const buffer = await fs.readFile(filePath);
        this.beneficiosMeuCardapio = buffer.toString('utf8');
        
        console.log('[CrmWhatsappAssistantService] Benefícios do Meu Cardápio carregados com sucesso');
        console.log('[CrmWhatsappAssistantService] Tamanho do arquivo:', this.beneficiosMeuCardapio.length, 'caracteres');
        console.log('[CrmWhatsappAssistantService] Preview:', this.beneficiosMeuCardapio.substring(0, 100) + '...');
      }
      return this.beneficiosMeuCardapio;
    } catch (error: any) {
      const filePath = path.join(__dirname, '../../../docs/meu-cardapio-beneficios.md');
      console.error('[CrmWhatsappAssistantService] Erro ao carregar benefícios do Meu Cardápio');
      console.error('[CrmWhatsappAssistantService] Caminho tentado:', filePath);
      console.error('[CrmWhatsappAssistantService] Erro completo:', error.message);
      
      // Retornar benefícios básicos como fallback
      console.log('[CrmWhatsappAssistantService] Usando benefícios básicos como fallback');
      return this.getBeneficiosBasicos();
    }
  }

  /**
   * Benefícios básicos como fallback
   */
  private getBeneficiosBasicos(): string {
    return `
# Benefícios Básicos do Meu Cardápio

- Sistema completo de gestão para restaurantes
- Pedidos online integrados ao WhatsApp
- Cardápio digital com QR Code
- Sistema de fidelidade com pontos
- Dashboard com métricas em tempo real
- Integrações com sistemas de PDV
- Suporte humanizado via WhatsApp
`;
  }

  /**
   * Gera sugestão de resposta usando IA baseado na metodologia SPIN Selling
   */
  async gerarSugestaoResposta(params: SugestaoParams): Promise<ResultadoSugestao> {
    const { telefone, produto, tomConversa, contato } = params;
    let { faseSpin } = params;
    
    // Filtrar mensagens sem texto antes de processar
    const mensagensFiltradas = params.mensagens.filter(msg => {
      const textoLimpo = (msg.texto || '').trim();
      return textoLimpo && textoLimpo !== '[Mensagem sem texto]';
    });
    
    console.log(`[CrmWhatsappAssistantService] Mensagens: ${params.mensagens.length} originais, ${mensagensFiltradas.length} após filtrar`);
    
    // Se a fase não foi fornecida, detectar automaticamente
    if (!faseSpin || faseSpin === 'auto') {
      console.log('[CrmWhatsappAssistantService] Detectando fase SPIN automaticamente...');
      faseSpin = await this.detectarFaseSpin(mensagensFiltradas) as 'situacao' | 'problema' | 'implicacao' | 'necessidade';
      console.log('[CrmWhatsappAssistantService] Fase detectada:', faseSpin);
    }
    
    console.log('[CrmWhatsappAssistantService] Gerando sugestão com params:', { telefone, faseSpin, produto, tomConversa });

    // Construir contexto da conversa com mensagens filtradas
    const contextoConversa = this.construirContextoConversa(mensagensFiltradas);
    console.log('[CrmWhatsappAssistantService] Contexto construído:', contextoConversa);

    // Determinar contexto adicional
    const hora = new Date().getHours();
    const horarioMensagem = hora < 12 ? 'manha' : hora < 18 ? 'tarde' : 'noite';
    
    // Construir prompt baseado na fase SPIN com contexto enriquecido
    const prompt = await this.construirPromptSpin({
      faseSpin,
      contextoConversa,
      produto,
      tomConversa,
      nomeContato: contato?.nomeResponsavel || 'Cliente',
      isNovoLead: !contato?.id,
      segmento: this.detectarSegmento(contextoConversa, contato),
      tamanhoEmpresa: contato?.tamanhoEmpresa || 'pequena',
      horarioMensagem
    });
    console.log('[CrmWhatsappAssistantService] Prompt construído para fase:', faseSpin);
    console.log('[CrmWhatsappAssistantService] Tamanho do prompt:', prompt.length, 'caracteres');
    console.log('[CrmWhatsappAssistantService] Prompt inclui benefícios?', prompt.includes('# Benefícios e Funcionalidades do Meu Cardápio'));

    try {
      // Preparar mensagens para o ChatGPT
      // IMPORTANTE: No WhatsApp, "Eu" = SDR (vendedor), então deve ser 'user' no contexto do ChatGPT
      // O cliente (lead) deve ser 'assistant' pois é a resposta esperada
      const mensagensFormatadas = mensagensFiltradas.slice(-10).map(msg => ({
        role: msg.remetente === 'Eu' ? 'user' : 'assistant',
        content: msg.texto
      }));

      // Chamar ChatGPT para gerar sugestão
      console.log('[CrmWhatsappAssistantService] Mensagens formatadas para ChatGPT:', mensagensFormatadas);
      console.log('[CrmWhatsappAssistantService] Chamando ChatGPT com', mensagensFormatadas.length, 'mensagens');
      const resposta = await this.chatGPTService.chameOpenAIChat(
        telefone,
        'whatsapp_assistant',
        prompt,
        '', // mensagem vazia pois já está no contexto
        mensagensFormatadas,
        0.7, // temperatura
        '[whatsapp-assistant]',
        { type: 'json_object' }
      );
      console.log('[CrmWhatsappAssistantService] Resposta do ChatGPT:', resposta);

      // Processar resposta
      let resultado: any;
      try {
        resultado = JSON.parse(resposta.text);
      } catch (e) {
        // Se não for JSON, usar resposta como texto direto
        resultado = {
          sugestoes: [{
            texto: resposta.text,
            confianca: 0.7
          }],
          faseSugerida: faseSpin,
          observacoes: 'Resposta gerada com formato simplificado'
        };
      }

      // Garantir formato correto com array de sugestões
      if (resultado.sugestao && !resultado.sugestoes) {
        // Formato antigo - converter para novo formato
        resultado.sugestoes = [{
          texto: resultado.sugestao,
          confianca: resultado.confianca || 0.8
        }];
      }

      // Garantir que sugestoes seja um array
      if (!Array.isArray(resultado.sugestoes)) {
        resultado.sugestoes = [{
          texto: resultado.sugestoes || resposta.text,
          confianca: 0.8
        }];
      }

      return {
        sugestoes: resultado.sugestoes.map((sug: any) => ({
          texto: sug.texto,
          confianca: sug.confianca || 0.85,
          faseSpin: faseSpin,
          timestamp: new Date()
        })),
        faseSugerida: resultado.faseSugerida || faseSpin,
        observacoes: resultado.observacoes || ''
      };

    } catch (erro: any) {
      console.error('[CrmWhatsappAssistantService] Erro ao gerar sugestão:', erro);

      // Fallback para sugestões predefinidas
      return this.obterSugestaoFallback(faseSpin, contextoConversa);
    }
  }

  /**
   * Detecta automaticamente a fase SPIN baseado nas mensagens usando IA
   */
  async detectarFaseSpin(mensagens: any[]): Promise<string> {
    try {
      // Se houver poucas mensagens, assumir fase inicial
      if (mensagens.length <= 2) {
        return 'situacao';
      }

      // Construir contexto da conversa completa
      const contextoCompleto = mensagens
        .map(m => `${m.remetente}: ${m.texto}`)
        .join('\n');

      const promptDeteccao = `Você é um especialista em vendas SPIN. Analise esta conversa de WhatsApp e identifique em qual fase SPIN ela está.

CONVERSA COMPLETA:
${contextoCompleto}

FASES SPIN:
1. SITUAÇÃO: Exploração inicial, perguntas sobre o negócio atual, como funciona hoje
2. PROBLEMA: Identificação de dificuldades, dores, problemas específicos
3. IMPLICAÇÃO: Discussão sobre consequências, impactos, custos dos problemas
4. NECESSIDADE: Lead demonstra interesse em solução, pergunta sobre preços, implementação

SINAIS PARA IDENTIFICAR CADA FASE:

SITUAÇÃO:
- Primeiras mensagens da conversa
- SDR fazendo perguntas exploratórias
- Lead compartilhando informações básicas do negócio
- Ainda não foram identificados problemas específicos

PROBLEMA:
- Lead mencionou dificuldades específicas
- Discussão sobre falhas ou limitações atuais
- SDR explorando dores do cliente
- Foco em "o que não está funcionando bem"

IMPLICAÇÃO:
- Discussão sobre perdas financeiras
- Menção a impactos negativos dos problemas
- Quantificação de prejuízos
- Lead reconhecendo consequências

NECESSIDADE:
- Lead pergunta sobre a solução
- Interesse em preços ou condições
- Perguntas sobre implementação
- Sinais claros de intenção de compra

IMPORTANTE:
- Considere a PROGRESSÃO natural da conversa
- Se o lead voltou atrás (ex: estava em problema mas voltou para situação), identifique a fase ATUAL
- Conversas podem regredir ou pular fases
- Foque na última direção da conversa

Responda APENAS com a fase identificada em lowercase: situacao, problema, implicacao ou necessidade`;

      // Chamar ChatGPT para análise
      const resposta = await this.chatGPTService.chameOpenAIChat(
        'deteccao_fase_spin',
        'whatsapp_spin_detector',
        promptDeteccao,
        '',
        [],
        0.3, // temperatura baixa para resposta mais determinística
        '[spin-detector]',
        undefined // response_format
      );

      // Extrair fase da resposta
      const faseDetectada = resposta.text.trim().toLowerCase();
      const fasesValidas = ['situacao', 'problema', 'implicacao', 'necessidade'];
      
      if (fasesValidas.includes(faseDetectada)) {
        console.log(`[detectarFaseSpin] Fase detectada via IA: ${faseDetectada}`);
        return faseDetectada;
      }

      // Fallback para detecção por palavras-chave se IA falhar
      console.log('[detectarFaseSpin] IA retornou fase inválida, usando fallback');
      return this.detectarFaseSpinPorPalavrasChave(mensagens);

    } catch (erro) {
      console.error('[detectarFaseSpin] Erro na detecção via IA:', erro);
      // Fallback para método antigo
      return this.detectarFaseSpinPorPalavrasChave(mensagens);
    }
  }

  /**
   * Método fallback de detecção por palavras-chave
   */
  private detectarFaseSpinPorPalavrasChave(mensagens: any[]): string {
    const palavrasChave = {
      situacao: ['atualmente', 'fazemos', 'processo', 'sistema', 'hoje', 'empresa', 'negócio'],
      problema: ['dificuldade', 'problema', 'demora', 'erro', 'falha', 'ruim', 'complicado'],
      implicacao: ['impacto', 'prejuízo', 'tempo perdido', 'custo', 'afeta', 'consequência'],
      necessidade: ['preciso', 'quero', 'gostaria', 'solução', 'resolver', 'melhorar']
    };

    // Analisar últimas 5 mensagens
    const textoRecente = mensagens.slice(-5)
      .map(m => m.texto.toLowerCase())
      .join(' ');

    const pontuacao = {
      situacao: 0,
      problema: 0,
      implicacao: 0,
      necessidade: 0
    };

    // Contar palavras-chave
    for (const [fase, palavras] of Object.entries(palavrasChave)) {
      for (const palavra of palavras) {
        if (textoRecente.includes(palavra)) {
          pontuacao[fase as keyof typeof pontuacao] += 1;
        }
      }
    }

    // Retornar fase com maior pontuação
    let faseMaiorPontuacao = 'situacao';
    let maiorPontuacao = 0;

    for (const [fase, pontos] of Object.entries(pontuacao)) {
      if (pontos > maiorPontuacao) {
        maiorPontuacao = pontos;
        faseMaiorPontuacao = fase;
      }
    }

    return faseMaiorPontuacao;
  }

  /**
   * Constrói o contexto resumido da conversa
   */
  private construirContextoConversa(mensagens: any[]): string {
    // Pegar últimas 5 mensagens mais relevantes
    const mensagensRelevantes = mensagens.slice(-5);

    return mensagensRelevantes
      .filter(m => m.texto && m.texto.trim()) // Garantir que tem texto
      .map(m => `${m.remetente}: ${m.texto}`)
      .join('\n');
  }

  /**
   * Constrói o prompt específico para cada fase SPIN
   */
  private async construirPromptSpin(params: {
    faseSpin: string;
    contextoConversa: string;
    produto: string;
    tomConversa: string;
    nomeContato: string;
    isNovoLead?: boolean;
    segmento?: string;
    tamanhoEmpresa?: string;
    horarioMensagem?: string;
  }): Promise<string> {
    const { faseSpin, contextoConversa, produto, tomConversa, nomeContato, isNovoLead, segmento, tamanhoEmpresa, horarioMensagem } = params;

    // Carregar benefícios do Meu Cardápio
    const beneficios = await this.carregarBeneficiosMeuCardapio();
    
    // Prompt base comum a todas as fases
    const promptBase = `Você é o **Assistente de Sugestões do SDR da Meu Cardápio AI**.  
Sua função é analisar todo o **histórico da conversa com o lead** (fornecido a seguir) e gerar **1 a 3 sugestões curtas de mensagens** que o SDR humano possa copiar e colar no WhatsApp.

❗ **Diretriz-chave**  
A missão mais difícil é conseguir que o lead responda E direcionar para os benefícios do Meu Cardápio.

### REGRA DE OURO - Balanceamento 30/70
⚖️ **SEMPRE siga esta proporção em TODAS as sugestões:**
- **30% máximo**: Acknowledge/resposta ao que o lead disse
- **70% mínimo**: Benefícios do Meu Cardápio relacionados ao contexto
- **100% foco**: SEMPRE termine direcionando para o produto, não para o problema

### Técnicas de Transição (USE SEMPRE)
Para conectar qualquer assunto ao Meu Cardápio:
- "Falando nisso..."
- "Isso me lembra que..."
- "É exatamente por isso que..."
- "Aliás, sobre isso..."
- "Aproveitando que mencionou..."

### Conexões Universais com Meu Cardápio
QUALQUER assunto pode levar aos benefícios:
- Lead fala de estoque → "Meu Cardápio remove item esgotado automaticamente, zero frustração"
- Lead fala de iFood → "Integramos iFood + WhatsApp + Balcão em uma tela só"
- Lead fala de horário de pico → "Sistema limita pedidos por hora, cozinha nunca trava"
- Lead fala de entrega → "Taxa automática por região, delivery sempre lucrativo"
- Lead fala de funcionários → "Impressão automática na cozinha, garçom foca em servir"
- Lead fala de organização → "Agendamento + multi-canal = zero caos operacional"
- Lead fala de fidelização → "Sistema de pontos automático, cliente volta mais"

### Como gerar cada sugestão
1. **Identifique PRIMEIRO o benefício do Meu Cardápio a destacar**
2. **Conecte esse benefício com o contexto mencionado**
3. **Faça acknowledge breve (máx 1 linha) do que foi dito**
4. **Redirecione SEMPRE para vantagem do produto**

### Checklist Final (OBRIGATÓRIO)
Antes de enviar cada sugestão, verifique:
□ Mencionou pelo menos 1 benefício específico do Meu Cardápio?
□ O Meu Cardápio tem mais destaque que o problema mencionado?
□ A resposta direciona para uma ação relacionada ao produto?
□ Usou técnica de transição para conectar contexto → produto?
Se alguma resposta for NÃO, refaça a sugestão.

### Regras de Estilo
- **Máx. 3 linhas** e **até 2 emojis** por mensagem.  
- Tom profissional-amigável, direto, sem jargão técnico.  
- Nunca informe preço antes de o lead demonstrar interesse claro.  
- Se houver objeção, responda com benefício concreto (ROI, rapidez, segurança do número).  
- Não repita conteúdo já enviado; adicione valor novo.

### CONHECIMENTO COMPLETO DO MEU CARDÁPIO
${beneficios}

### CONTEXTO DA CONVERSA:
${contextoConversa}

### INFORMAÇÕES DO LEAD:
- Nome: ${nomeContato}
- ${isNovoLead ? 'NOVO LEAD (não cadastrado no CRM)' : 'Lead já cadastrado no CRM'}
${segmento ? `- Segmento: ${segmento}` : ''}
${tamanhoEmpresa ? `- Porte da empresa: ${tamanhoEmpresa}` : ''}
${horarioMensagem ? `- Horário da conversa: ${horarioMensagem}` : ''}
`;

    const promptsBase = {
      situacao: promptBase + `

### FASE ATUAL: SITUAÇÃO
Objetivo: Explorar cenário atual E introduzir benefícios do Meu Cardápio.

**EXEMPLOS de Balanceamento 30/70 (SIGA ESTE PADRÃO):**

Contexto: Lead menciona problema com fornecedor
❌ RUIM: "Que difícil lidar com fornecedor! Como vocês controlam o estoque hoje?"
✅ BOM: "Fornecedor é desafio mesmo! Aliás, com Meu Cardápio você vê consumo em tempo real e negocia melhor. Como controlam isso hoje?"

Contexto: Lead fala que funcionário faltou
❌ RUIM: "Complicado quando falta gente né? Como vocês se organizam?"
✅ BOM: "Falta complica mesmo! Por isso o cardápio digital é salvador - cliente pede sozinho. Quantos pedidos vocês recebem por dia?"

**Perguntas SPIN de Situação (sempre com gancho do Meu Cardápio):**
- "Vi que vocês têm delivery e salão. Como gerenciam pedidos do iFood + WhatsApp + mesas ao mesmo tempo?"
- "Quando acaba um ingrediente importante, quanto tempo leva pra tirar do cardápio online?"
- "Vocês controlam quantos pedidos aceitam por hora ou a cozinha às vezes sobrecarrega?"
- "Como calculam a taxa de entrega? É por bairro ou valor fixo?"
- "Clientes pedem pra agendar pedido pro almoço? Como controlam isso hoje?"

**Para PRIMEIRA mensagem - Rapport + Meu Cardápio:**
- "Vi que têm delivery próprio! 🚚 Com Meu Cardápio você integra iFood + WhatsApp em uma tela. Como gerenciam hoje?"
- "Adorei ver que atendem vários bairros! Nossa taxa automática por região facilita muito. Como cobram entrega hoje?"
- "Notei que abrem pra almoço e jantar. Com agendamento do Meu Cardápio, cliente pede de manhã pro almoço. Fazem isso?"

**Para conversa em andamento - SEMPRE direcione para o produto:**
- Qualquer resposta do lead = oportunidade de mencionar benefício
- Use as técnicas de transição para conectar
- Mantenha 70% do foco no Meu Cardápio

### FORMATO DE RESPOSTA JSON:
{
  "sugestoes": [
    {
      "texto": "mensagem 1 com rapport + pergunta situacional",
      "confianca": 0.9
    },
    {
      "texto": "mensagem 2 com rapport + pergunta situacional diferente",
      "confianca": 0.85
    },
    {
      "texto": "mensagem 3 com rapport + exploração de processo",
      "confianca": 0.8
    }
  ],
  "faseSugerida": "situacao",
  "observacoes": "insights sobre o lead e próximos passos"
}`,

      problema: promptBase + `

### FASE ATUAL: PROBLEMA
Objetivo: Explorar dificuldades E mostrar como Meu Cardápio resolve.

**EXEMPLOS de Balanceamento 30/70 (SIGA ESTE PADRÃO):**

Contexto: Lead diz "WhatsApp trava muito"
❌ RUIM: "Nossa, deve ser terrível! Quantas vezes por semana trava? Perde muita venda?"
✅ BOM: "WhatsApp travando é pesadelo! Por isso Meu Cardápio usa API oficial que NUNCA trava. Quanto vocês perdem quando isso acontece?"

Contexto: Lead menciona erros em pedidos
❌ RUIM: "Erros são prejuízo certo! Como vocês lidam com reembolsos?"
✅ BOM: "Erro em pedido é perda dupla né? Meu Cardápio tem confirmação automática que reduz 70% dos erros. Quantos acontecem por semana aí?"

**Perguntas de Problema (sempre com solução Meu Cardápio):**
- "Quando acabam as batatas fritas, quantos pedidos errados acontecem até atualizar o cardápio?"
- "Gerenciar pedidos do iFood numa tela e WhatsApp em outra deve ser caótico né?"
- "No sábado à noite quando lota, a cozinha consegue dar conta ou tem que recusar pedidos?"
- "Cliente de bairro longe pede R$30 e a entrega custa R$15. Como resolvem isso?"
- "Quantas vezes por dia vocês tem que dizer 'não' porque a cozinha tá sobrecarregada?"

**Conecte SEMPRE problema → solução:**
- Item esgotado → "Estoque integrado remove do cardápio automaticamente"
- Multi-canal caótico → "iFood + WhatsApp + Mesas em uma única tela"
- Cozinha travada → "Limite de pedidos por hora, nunca sobrecarrega"
- Entrega não rentável → "Taxa automática por região, sempre lucrativo"
- Sem controle de capacidade → "Agendamento com slots configuráveis"
- Impressão manual → "Pedido sai direto na impressora da cozinha"

**Regra: NUNCA explore problema sem apresentar solução**
- Cada pergunta sobre dificuldade DEVE incluir como Meu Cardápio resolve
- Use estatísticas de sucesso: "****** restaurantes", "30% mais vendas"
- Mantenha foco 70% na solução, 30% no problema

### FORMATO DE RESPOSTA JSON:
{
  "sugestoes": [
    {
      "texto": "mensagem 1 com rapport + pergunta sobre dificuldades",
      "confianca": 0.9
    },
    {
      "texto": "mensagem 2 com rapport + exploração de problema específico",
      "confianca": 0.85
    },
    {
      "texto": "mensagem 3 com rapport + quantificação de problema",
      "confianca": 0.8
    }
  ],
  "faseSugerida": "problema",
  "observacoes": "dores principais identificadas"
}`,

      implicacao: promptBase + `

### FASE ATUAL: IMPLICAÇÃO
Objetivo: Quantificar perdas E mostrar ganhos imediatos com Meu Cardápio.

**EXEMPLOS de Balanceamento 30/70 (SIGA ESTE PADRÃO):**

Contexto: Lead disse que perde alguns pedidos
❌ RUIM: "Quantos pedidos perdem? Isso deve dar uns R$ 10.000/mês de prejuízo né?"
✅ BOM: "Perder pedido dói! Com Meu Cardápio você recupera TODOS - é R$ 15.000/mês a mais garantido. Já calculou quanto perde hoje?"

Contexto: Lead mencionou que gasta tempo com pedidos
❌ RUIM: "Tempo é dinheiro! Quantas horas por dia desperdiçam com isso?"
✅ BOM: "Tempo perdido! Meu Cardápio economiza 3h/dia = 1 funcionário a menos. São R$ 3.000/mês na sua conta. Quanto tempo gastam hoje?"

**Fórmulas de Impacto (USE SEMPRE):**
- "10 pedidos/dia com item esgotado = 10 clientes frustrados = 300/mês que não voltam. Vale a pena?"
- "Gerenciar 3 telas (iFood, WhatsApp, Mesas) = 2h/dia perdidas = R$ 3.000/mês em produtividade"
- "Cozinha travada no sábado = 50 pedidos recusados x R$ 50 = R$ 2.500 perdidos numa noite!"
- "Entrega sem taxa adequada = R$ 500/dia de prejuízo. São R$ 15.000/mês no vermelho!"
- "Sem agendamento = picos descontrolados = equipe estressada = rotatividade alta"

**ROI REAL com Meu Cardápio:**
- Estoque integrado = Zero frustração = +20% retorno de clientes
- Multi-canal unificado = -3h/dia = R$ 3.000 economizados
- Limite por horário = Cozinha eficiente = +40% capacidade
- Taxa automática = Toda entrega lucrativa = +R$ 15.000/mês
TOTAL: R$ 25.000+/mês por apenas R$ 299!

**Crie urgência com casos reais:**
- "Enquanto conversamos, quantos itens esgotados estão sendo pedidos?"
- "Seu concorrente já usa agendamento. Tá roubando seus clientes do almoço!"
- "Cada sábado sem controle = R$ 2.500 perdidos. Quantos sábados mais?"

**Regra: Sempre apresente solução > problema**
70% Meu Cardápio (ganhos) vs 30% problema (perdas)

### FORMATO DE RESPOSTA JSON:
{
  "sugestoes": [
    {
      "texto": "mensagem 1 com rapport + quantificação de impacto",
      "confianca": 0.9
    },
    {
      "texto": "mensagem 2 com rapport + consequência futura",
      "confianca": 0.85
    },
    {
      "texto": "mensagem 3 com rapport + comparação com concorrência",
      "confianca": 0.8
    }
  ],
  "faseSugerida": "implicacao",
  "observacoes": "impactos financeiros e operacionais identificados"
}`,

      necessidade: promptBase + `

### FASE ATUAL: NECESSIDADE (NEED-PAYOFF)
Objetivo: Lead já tem interesse - FECHAR com Meu Cardápio AGORA.

**EXEMPLOS de Balanceamento 30/70 (SIGA ESTE PADRÃO):**

Contexto: Lead pergunta sobre preço
❌ RUIM: "R$ 299/mês. É um ótimo investimento considerando os benefícios."
✅ BOM: "R$ 299 que viram R$ 35.000/mês! ROI de 11.600%. Ativo em 3h - começamos agora?"

Contexto: Lead quer pensar
❌ RUIM: "Claro, pense com calma. Qualquer dúvida me chame!"
✅ BOM: "Entendo! Enquanto pensa, perde R$ 1.166/dia. Que tal testar 30 dias grátis sem compromisso?"

**SEMPRE conecte benefício → ação imediata:**
- "Estoque + Multi-canal + Agendamento = Zero caos. Qual e-mail para ativar?"
- "iFood integrado + Taxa automática funcionando amanhã. Começamos?"
- "Limite de pedidos ativo = Sábado sem stress. Bora configurar?"
- "Cardápio que atualiza sozinho + Fidelidade automática. Fecha?"

**CTAs baseados no problema mencionado:**
- Item esgotado: "Estoque integrado ativo em 3h. Nunca mais frustrar cliente. Vamos?"
- Multi-canal: "iFood + WhatsApp numa tela só amanhã. Qual melhor horário?"
- Cozinha travada: "Limite por hora configurado hoje. Zero stress amanhã. Começamos?"
- Taxa entrega: "Regiões configuradas = Toda entrega lucrativa. Ativo hoje?"
- Sem dados: "Dashboard completo + Relatórios prontos. Quer ver demo agora?"

**Pacote de Ativação HOJE:**
- "Setup em 3 horas (funciona hoje à noite!)"
- "Cardápio migrado + iFood conectado"
- "Áreas de entrega configuradas"
- "Limite de pedidos ajustado"
- "Equipe treinada via WhatsApp"
- "30 dias grátis para testar"

**Regra: 100% foco em ação imediata**
Não aceite "vou pensar" - sempre ofereça teste ou demo

### FORMATO DE RESPOSTA JSON:
{
  "sugestoes": [
    {
      "texto": "mensagem 1 com rapport + solução específica + CTA",
      "confianca": 0.95
    },
    {
      "texto": "mensagem 2 com rapport + ROI claro + CTA diferente",
      "confianca": 0.9
    },
    {
      "texto": "mensagem 3 com rapport + urgência sutil + CTA fácil",
      "confianca": 0.85
    }
  ],
  "faseSugerida": "necessidade",
  "observacoes": "oportunidade madura para fechamento"
}`
    };

    return promptsBase[faseSpin as keyof typeof promptsBase] || promptsBase.situacao;
  }

  /**
   * Retorna sugestão fallback caso a IA falhe
   */
  private obterSugestaoFallback(faseSpin: string, contexto: string): ResultadoSugestao {
    const sugestoesFallback = {
      situacao: 'Olá! Vi que vocês têm um restaurante incrível! 🍽️ Quantos pedidos vocês recebem por dia no WhatsApp? Com o Meu Cardápio, restaurantes similares dobraram esse número!',
      problema: 'Entendi! Esse problema que você mencionou é comum. Sabia que o Meu Cardápio resolve isso automaticamente? Restaurantes economizam 3h/dia e reduzem erros em 70%.',
      implicacao: 'Fazendo as contas: se perdem 5 pedidos/dia, são R$ 7.500/mês jogados fora! 😱 Com Meu Cardápio custando R$ 299/mês, o ROI é de 2.400%. Faz sentido deixar esse dinheiro na mesa?',
      necessidade: 'Perfeito! O Meu Cardápio resolve EXATAMENTE isso! Posso ativar sua conta em 3 horas e você já começa a faturar 30% a mais. Quando podemos começar? 🚀'
    };

    return {
      sugestoes: [{
        texto: sugestoesFallback[faseSpin as keyof typeof sugestoesFallback] || sugestoesFallback.situacao,
        confianca: 0.6,
        faseSpin: faseSpin,
        timestamp: new Date()
      }],
      faseSugerida: faseSpin,
      observacoes: 'Sugestão padrão utilizada devido a erro na geração via IA'
    };
  }

  /**
   * Gera mensagens de rapport/atratividade para abordagem outbound
   */
  async gerarMensagemRapport(params: {
    telefone: string;
    nomeContato?: string;
    empresa?: string;
    tipoAbordagem: 'direta' | 'indireta' | 'consultiva';
    produto: string;
    ultimaMensagem?: string;
  }): Promise<ResultadoSugestao> {
    const { telefone, nomeContato, empresa, tipoAbordagem, produto, ultimaMensagem } = params;
    console.log('[CrmWhatsappAssistantService] Gerando mensagem de rapport:', { telefone, tipoAbordagem, produto });

    try {
      // Construir prompt específico para rapport
      const prompt = await this.construirPromptRapport({
        nomeContato: nomeContato || 'você',
        empresa,
        tipoAbordagem,
        produto,
        ultimaMensagem
      });
      console.log('[CrmWhatsappAssistantService] Prompt de rapport construído para abordagem:', tipoAbordagem);

      // Chamar ChatGPT para gerar mensagem
      const resposta = await this.chatGPTService.chameOpenAIChat(
        telefone,
        'whatsapp_rapport',
        prompt,
        '',
        [], // Sem histórico para rapport inicial
        0.8, // temperatura um pouco mais alta para criatividade
        '[whatsapp-rapport]',
        { type: 'json_object' }
      );
      console.log('[CrmWhatsappAssistantService] Resposta do ChatGPT (rapport):', resposta);

      // Processar resposta
      let resultado: any;
      try {
        resultado = JSON.parse(resposta.text);
      } catch (e) {
        // Se não for JSON, usar resposta como texto direto
        resultado = {
          sugestoes: [{
            texto: resposta.text,
            confianca: 0.7
          }]
        };
      }

      // Garantir formato correto com array de sugestões
      if (resultado.mensagem && !resultado.sugestoes) {
        // Formato antigo - converter para novo formato
        resultado.sugestoes = [{
          texto: resultado.mensagem,
          confianca: resultado.confianca || 0.85
        }];
      }

      // Garantir que sugestoes seja um array
      if (!Array.isArray(resultado.sugestoes)) {
        resultado.sugestoes = [{
          texto: resultado.sugestoes || resposta.text,
          confianca: 0.8
        }];
      }

      return {
        sugestoes: resultado.sugestoes.map((sug: any) => ({
          texto: sug.texto,
          confianca: sug.confianca || 0.85,
          faseSpin: 'rapport',
          timestamp: new Date()
        })),
        faseSugerida: 'rapport',
        observacoes: resultado.observacoes || ''
      };

    } catch (erro: any) {
      console.error('[CrmWhatsappAssistantService] Erro ao gerar mensagem de rapport:', erro);

      // Fallback para mensagens predefinidas
      return this.obterMensagemRapportFallback(params);
    }
  }

  /**
   * Constrói prompt para gerar mensagens de rapport
   */
  private async construirPromptRapport(params: {
    nomeContato: string;
    empresa?: string;
    tipoAbordagem: 'direta' | 'indireta' | 'consultiva';
    produto: string;
    ultimaMensagem?: string;
  }): Promise<string> {
    const { nomeContato, empresa, tipoAbordagem, produto, ultimaMensagem } = params;

    const promptsAbordagem = {
      direta: `Você é um especialista em vendas do Meu Cardápio com taxa de conversão de 47%.
      
MISSÃO: Criar mensagem que gere interesse IMEDIATO mostrando ROI claro.

FÓRMULA MATADORA DO MEU CARDÁPIO:
1. **Hook com número chocante**: "Sabia que 73% dos pedidos se perdem no WhatsApp?"
2. **Prova social específica**: "****** restaurantes aumentaram vendas em 30%"
3. **Benefício financeiro claro**: "R$ 15.000/mês a mais, investindo R$ 299"
4. **CTA de teste sem risco**: "Quer ver funcionando em 5 minutos?"

GANCHOS QUE CONVERTEM:
• "Seu concorrente [nome] já fatura 30% a mais com Meu Cardápio..."
• "Restaurantes perdem R$ 7.500/mês por demora no WhatsApp. E você?"
• "3 restaurantes da [rua/bairro] já automatizaram. Você fica para trás?"
• "WhatsApp travou no sábado? Com Meu Cardápio isso NUNCA acontece"
• "Quer que eu mostre como dobrar seus pedidos em 30 dias?"

SEMPRE MENCIONE:
• ROI de 11.600% comprovado
• Ativação em 3 horas
• ****** clientes satisfeitos
• Garantia de 30 dias

Máximo 3 linhas | Use 💰🚀📈 | Tom ${empresa ? 'personalizado para ' + empresa : 'direto ao ponto'}`,

      indireta: `Você é consultor de sucesso do Meu Cardápio, especialista em criar conexões.

MISSÃO: Gerar curiosidade sobre Meu Cardápio através de valor e empatia.

ESTRATÉGIA INDIRETA QUE CONVERTE:
1. **Elogio específico + problema sutil**: "Adorei o cardápio de vocês! Mas deve dar trabalho atualizar tudo no WhatsApp, né?"
2. **Compartilhar insight valioso**: "Vi que 73% dos clientes desistem por demora. Tenho uma dica..."
3. **História de sucesso casual**: "Estava no [restaurante similar] ontem, eles me contaram que..."
4. **Pergunta consultiva**: "Vocês medem quantos pedidos perdem por dia?"

GANCHOS INDIRETOS DO MEU CARDÁPIO:
• "Vi suas avaliações no Google! 4.7 é incrível! Imagino o trabalho para manter isso..."
• "Passando para parabenizar pelo movimento! Como vocês dão conta do WhatsApp?"
• "Notei que abrem aos domingos. O delivery deve bombar! Como organizam os pedidos?"
• "Seu Instagram está lindo! Já pensaram em transformar isso em vendas automáticas?"
• "Um cliente me indicou vocês! Disse que a comida é top mas que demora pra pedir..."

TRANSIÇÃO NATURAL PARA MEU CARDÁPIO:
• Sempre conecte elogio → dificuldade → solução sutil
• Mencione "caso queira conhecer" sem pressão
• Use "outros restaurantes" como exemplo
• Foque em ajudar, não vender

Tom: Consultivo e amigável | 😊🤝💡 | ${empresa ? 'Personalizado para ' + empresa : 'Conversacional'}`,

      consultiva: `Você é head de customer success do Meu Cardápio com 8 anos de experiência.

MISSÃO: Diagnosticar problemas e mostrar o impacto financeiro de não usar Meu Cardápio.

ABORDAGEM CONSULTIVA MEU CARDÁPIO:
1. **Pergunta diagnóstica + benchmark**: "Vocês sabem quantos % dos pedidos perdem por demora? A média é 23%..."
2. **Cálculo de impacto na hora**: "Se atendem 100 pedidos/dia, são 23 perdidos = R$ 1.150/dia = R$ 34.500/mês"
3. **Comparação com concorrência**: "Restaurantes similares aumentaram 30% após automatizar. Vocês ficam para trás?"
4. **Oferta de análise gratuita**: "Posso fazer uma análise de quanto vocês estão perdendo. Leva 5 minutos"

PERGUNTAS CONSULTIVAS MATADORAS:
• "Qual % do faturamento vocês reinvestem em tecnologia? Menos de 1%?"
• "Sabem quanto custa cada minuto de espera do cliente? R$ 3,50 em média"
• "Quantos garçons precisariam contratar para dobrar o atendimento?"
• "Se o WhatsApp cair sábado à noite, qual o plano B de vocês?"
• "Quanto tempo leva desde o cliente pedir até pagar? Mais de 20 min?"

SEMPRE TERMINE COM INSIGHT DE VALOR:
• "Baseado no que me disse, vocês perdem cerca de R$ [X]/mês"
• "Com automação, economizariam [Y] horas/dia da equipe"
• "Seus concorrentes processam pedidos 70% mais rápido"
• "Posso mostrar exatamente onde estão os gargalos?"

${empresa ? `Use dados específicos de ${empresa} se disponíveis` : 'Use médias do mercado'}
Tom: Autoridade consultiva | 📊💡 | Business case orientation`
    };

    // Carregar benefícios do Meu Cardápio
    const beneficios = await this.carregarBeneficiosMeuCardapio();
    
    const contextoProduto = produto === 'PromoKit - Sistema de Gestão' || produto === 'Sistema de Gestão PromoKit'
      ? 'Meu Cardápio - Sistema que aumenta vendas em 30% com cardápio digital e automação WhatsApp'
      : produto;

    let promptBase = `${promptsAbordagem[tipoAbordagem]}

### CONHECIMENTO COMPLETO DO MEU CARDÁPIO
${beneficios}

Informações disponíveis:
- Nome do contato: ${nomeContato}
${empresa ? `- Empresa: ${empresa}` : ''}
- Produto/Serviço: ${contextoProduto}
${ultimaMensagem ? `- Última mensagem do cliente: "${ultimaMensagem}"` : ''}

IMPORTANTE:
- As mensagens devem despertar CURIOSIDADE e INTERESSE
- Evite parecer spam ou muito vendedor
- Use técnicas de copywriting e persuasão
- Personalize com as informações disponíveis
- Para trabalho OUTBOUND, a primeira impressão é crucial
- Gere 1 a 3 variações diferentes de mensagens

Responda em formato JSON com 1 a 3 sugestões:
{
  "sugestoes": [
    {
      "texto": "primeira mensagem de rapport",
      "confianca": 0.95
    },
    {
      "texto": "segunda mensagem de rapport (variação diferente)",
      "confianca": 0.9
    },
    {
      "texto": "terceira mensagem de rapport (abordagem alternativa)",
      "confianca": 0.85
    }
  ],
  "observacoes": "insights sobre a abordagem escolhida"
}`;

    return promptBase;
  }

  /**
   * Detecta o segmento do cliente baseado no contexto
   */
  private detectarSegmento(contexto: string, contato: any): string {
    const textoAnalise = (contexto + ' ' + (contato?.crmEmpresa?.nome || contato?.empresa || '')).toLowerCase();
    
    // Palavras-chave por segmento
    const segmentos = {
      'restaurante': ['restaurante', 'pizzaria', 'lanchonete', 'bar', 'café', 'padaria', 'delivery', 'comida', 'alimento', 'refeição', 'cardápio', 'mesa', 'garçom'],
      'varejo': ['loja', 'venda', 'produto', 'estoque', 'vitrine', 'cliente', 'compra', 'mercado', 'supermercado'],
      'servicos': ['serviço', 'consultoria', 'escritório', 'atendimento', 'agenda', 'consulta', 'projeto'],
      'industria': ['fábrica', 'produção', 'manufatura', 'industrial', 'processo', 'linha', 'montagem'],
      'saude': ['clínica', 'hospital', 'médico', 'paciente', 'consulta', 'exame', 'saúde', 'tratamento']
    };
    
    // Contar ocorrências
    let melhorSegmento = 'geral';
    let maiorPontuacao = 0;
    
    for (const [segmento, palavras] of Object.entries(segmentos)) {
      let pontuacao = 0;
      for (const palavra of palavras) {
        if (textoAnalise.includes(palavra)) {
          pontuacao++;
        }
      }
      if (pontuacao > maiorPontuacao) {
        maiorPontuacao = pontuacao;
        melhorSegmento = segmento;
      }
    }
    
    return melhorSegmento;
  }

  /**
   * Retorna mensagens de rapport fallback
   */
  private obterMensagemRapportFallback(params: {
    nomeContato?: string;
    empresa?: string;
    tipoAbordagem: 'direta' | 'indireta' | 'consultiva';
  }): ResultadoSugestao {
    const nome = params.nomeContato || 'você';
    const empresa = params.empresa;

    const mensagensFallback = {
      direta: [
        `Oi ${nome}! 💰 ${empresa ? `A ${empresa}` : 'Sua empresa'} pode faturar R$ 15.000/mês a mais com Meu Cardápio. Investimento: R$ 299. ROI de 5.000%. Quer ver como? 📈`,
        `${nome}, sabia que 73% dos pedidos se perdem no WhatsApp? Com Meu Cardápio você recupera TODOS! ****** restaurantes já faturam 30% a mais. Bora? 🚀`,
        `URGENTE ${nome}! 3 restaurantes ${empresa ? `perto da ${empresa}` : 'da sua região'} já automatizaram com Meu Cardápio. Não fique para trás! Posso mostrar em 5min?`
      ],
      indireta: [
        `Oi ${nome}! Vi que ${empresa || 'vocês'} têm ótimas avaliações! 🌟 Mas imagino que manter isso com WhatsApp pessoal deve ser difícil... Tenho uma dica que pode ajudar!`,
        `${nome}, passando pra parabenizar pelo sucesso! 👏 Notei que ${empresa ? `a ${empresa}` : 'vocês'} tem bastante movimento. Como dão conta de todos os pedidos no WhatsApp?`,
        `Oi! Cliente me indicou ${empresa || 'vocês'}, disse que a comida é TOP! 😋 Mas comentou que demora pra conseguir fazer pedido... Isso procede?`
      ],
      consultiva: [
        `${nome}, você sabe quantos % dos pedidos ${empresa ? `a ${empresa}` : 'vocês'} perdem por demora? A média do mercado é 23%. Com Meu Cardápio cai pra zero. Quer entender como?`,
        `Olá ${nome}! Fazendo uma pesquisa: quanto tempo leva desde o cliente chamar no WhatsApp até pagar? Se for mais de 10min, estão perdendo dinheiro. Posso explicar?`,
        `${nome}, pergunta diagnóstica: se o WhatsApp ${empresa ? `da ${empresa}` : 'de vocês'} cair num sábado à noite, qual o plano B? Meu Cardápio tem backup automático. Vale conhecer?`
      ]
    };

    const mensagensEscolhidas = mensagensFallback[params.tipoAbordagem];
    
    // Selecionar aleatoriamente 2-3 mensagens diferentes
    const sugestoes = [];
    const indices = [...Array(mensagensEscolhidas.length).keys()];
    const numSugestoes = Math.min(3, mensagensEscolhidas.length);
    
    for (let i = 0; i < numSugestoes; i++) {
      const randomIndex = Math.floor(Math.random() * indices.length);
      const msgIndex = indices.splice(randomIndex, 1)[0];
      
      sugestoes.push({
        texto: mensagensEscolhidas[msgIndex],
        confianca: 0.7 - (i * 0.05), // Diminui confiança para cada sugestão adicional
        faseSpin: 'rapport',
        timestamp: new Date()
      });
    }

    return {
      sugestoes,
      faseSugerida: 'rapport',
      observacoes: 'Sugestões de rapport geradas via fallback'
    };
  }
}
