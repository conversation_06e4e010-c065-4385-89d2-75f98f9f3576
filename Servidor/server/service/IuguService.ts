import {Ambiente} from "./Ambiente";
import {PlanoEmpresarial} from "../domain/faturamento/PlanoEmpresarial";
import {Empresa} from "../domain/Empresa";
import {Contrato} from "../domain/faturamento/Contrato";
import {Assinatura} from "../domain/faturamento/Assinatura";
import * as async from "async";
import * as moment from "moment";
import * as _ from "underscore";
import {DTODadosCobranca} from "../lib/iugu/DTODadosCobranca";
import {MapeadorDeEmpresa} from "../mapeadores/MapeadorDeEmpresa";
import {DateUtils} from "../lib/DateUtils";
import {ErroCartao, ErrosCartaoUtils} from "../lib/ErrosCartaoUtils";
import {MapeadorDeFatura} from "../mapeadores/MapeadorDeFatura";
import {EnumStatusFatura} from "../lib/emun/EnumStatusFatura";
import {EnumFormaPagamentoIugu} from "../lib/emun/EnumFormaPagamentoIugu";
import axios from "axios";
import {Fatura} from "../domain/faturamento/Fatura";


const iugu  = require( "iugu");

export class IuguService {
  private token: string;
  private host = 'https://api.iugu.com/v1'
  api: any;
  private credencial: any = {
    prod: {
      token: '022953d1687612eb6fba92ae7bca2b7c'
    },
    dev: {
      token: '2f83eeebd763f828a25e093c9abf0e50'
    }
  }

  constructor() {
    const credential = this.obtenhaCredencial();
    this.token = credential.token;
    this.api = iugu(this.token);
    this.api.setTimeout(45000);
  }

  obtenhaCredencial(){
    if(Ambiente._instance.producao) return  this.credencial.prod;
    return  this.credencial.dev;
  }

  obtenhaToken(){
    return this.token;
  }

  async obtenhaCliente(codigo: string){
    return new Promise<void>( async (resolve, reject) => {
      this.api.customers.retrieve(codigo, (err: any, resp: any) => {
        if (resp && resp.id) {
          resolve(resp)
        } else {
          resolve()
        }
      })
    })
  }

  async obtenhaPlano(codigo: string){
    return new Promise<void>( async (resolve, reject) => {
      //identifier/identifier
      this.api.plans.retrieveByIdentifier(codigo, (err: any, resp: any) => {
        if (resp && resp.id) {
          resolve(resp)
        } else {
          resolve()
        }
      })
    })
  }

  async removaPagamentoPadrao(empresa: any){
    return new Promise<void>( async (resolve, reject) => {
      if(!empresa.codigoCliente) return resolve();

      this.api.customers.update(empresa.codigoCliente, {default_payment_method_id: 'null'},  (err: any, resp: any) => {
        if(err) console.error(err)

        resolve()
      })
    })
  }

  async listeClientes(inicio: number = 0, total: number = 10, cpf_cnpj: string = null){
    let params: any  = {start: inicio, limit: total};

    if(cpf_cnpj)
      params.query = cpf_cnpj;

    return new Promise<void>( async (resolve, reject) => {
      this.api.customers.list(params, (err: any, resp: any) => {
        if(resp){
          resolve(resp.items)
        } else {
          reject(err)
        }
      })
    })
  }

  async obtenhaOuCrieCliente(empresa: Empresa, assinaturaMensal: boolean = false){
    return new Promise( async (resolve, reject) => {
      if(empresa.codigoCliente)
        return resolve({ id: empresa.codigoCliente})

      let dadosCobranca =  new DTODadosCobranca(empresa);

      if(!dadosCobranca.cep) return reject('CEP da empresa não foi cadastrado.');
      if(!dadosCobranca.email) return reject('Email de cobrança não foi cadastrado.');

      let dadosCliente: any = { email: dadosCobranca.email,
        cpf_cnpj: (dadosCobranca.cpf || dadosCobranca.cnpj),
        name: empresa.nome, zip_code: dadosCobranca.cep,  number: '0'
      };

      if(dadosCobranca.bairro){
        dadosCliente.district = dadosCobranca.bairro;
        dadosCliente.street = dadosCobranca.logradouro;
      }

      if(!dadosCliente.cpf_cnpj) return reject('Cnpj ou cpf não cadastrado.');

      let customers: any = await this.listeClientes(0, 10, dadosCliente.cpf_cnpj).catch((err) => {
        reject('Não foi possível criar cliente (listar clientes):' + err);
      })

      if(customers){
        //se for pra criar uma assinatura nao mensal, nao pode usar existente que tenha cartao padrao salvo
        // se nao ja cobra quando cria assinatura sem dividir
        let customerExistente =  assinaturaMensal  ?  customers[0] :
          customers.find((item: any) => item.default_payment_method_id == null);

        if(customerExistente){
          empresa.codigoCliente = customerExistente.id;
          await new MapeadorDeEmpresa().atualizeCliente(empresa);
          resolve(customerExistente);
        } else {
          this.api.customers.create(dadosCliente,   async(erro2: any, repostaNovoCliente: any) => {
            if(repostaNovoCliente.id){
              customers = repostaNovoCliente;
              empresa.codigoCliente = customers.id;
              await new MapeadorDeEmpresa().atualizeCliente(empresa);
              resolve(customers);
            } else {
              reject('Não foi possível criar cliente: ' + this.obtenhaErro(repostaNovoCliente, erro2));
            }
          })
        }
      }

    })
  }

  async obtenhaAssinatura(codigo: string) {
    return new Promise<any>( async (resolve, reject) => {
      this.api.subscriptions.retrieve(codigo, (err: any, resp: any) => {
        if (resp && resp.id) {
          resolve(resp)
        } else {
          resolve(null);
        }
      })
    })
  }


  async obtenhaFaturas(idCliente: any, dataCricao: Date){
    return new Promise<any>( async (resolve, reject) => {
      let dados: any = {customer_id: idCliente, created_at_from: moment(dataCricao).format('YYYY-MM-DDTHH:mm:ssZ')};
      console.log(dados)
      this.api.invoices.list(dados,   (err: any, resp: any) => {
        if (resp && resp.items) {
          resolve(resp.items)
        } else {
          resolve([]);
        }
      })
    })
  }

  async obtenhaFatura(codigo: string) {
    return new Promise<any>( async (resolve, reject) => {
      if(!codigo) return resolve(null);
      this.api.invoices.retrieve(codigo, (err: any, resp: any) => {
        if (resp && resp.id) {
          resolve(resp)
        } else {
          resolve(null);
        }
      })
    })
  }

  async listeAssinaturas(inicio: number, total: number  = 100, ativas: boolean = false) {
    return new Promise( async (resolve, reject) => {
      console.log(String(`Buscar assinaturas ${inicio} / ${total}`))

      let params: any = { start: inicio, limit: total };

      if(ativas)
        params.status_filter = 'active' //due, active, suspended

      console.log(params)
      this.api.subscriptions.list( params, (err: any, resp: any) => {
        if(err){
          console.error(err)
          return reject(err)
        }

        resolve(resp.items)
      })
    })
  }

  async listePlanos(inicio: number, total: number) {
    return new Promise( async (resolve, reject) => {
      this.api.plans.list( { start: inicio, limit: total }, (err: any, resp: any) => {
        resolve(resp.items)
      })
    })
  }

  async criePlano(plano: PlanoEmpresarial) {
    let identificador: string = plano.obtenhaIdentificador()

    let dados = {
      name: plano.nome,
      identifier:  identificador  ,
      interval_type: 'months',
      value_cents: plano.valor * 100,
      payable_with: 'all' ,
      interval: 1,
      features: [ { name: 'Mensalidade Promokit', identifier: 'promokit_mensal' , value: plano.valor * 100 }]
    };

    return new Promise<void|Error>( (resolve, reject) => {
      console.log(dados)
      this.api.plans.retrieveByIdentifier(identificador, async (erro: any, resposta: any) => {
         if( resposta.id ){
           await plano.insiraPlanoIugu(this.token, resposta.id, resposta.identifier, resposta.created_at);
           resolve();
         } else {
           this.api.plans.create(dados,  async (  err: any, novoPlano: any) => {
             if(!err){
               await plano.insiraPlanoIugu(this.token, novoPlano.id, novoPlano.identifier, novoPlano.created_at);
               resolve()
             } else {
               resolve(err)
             }
           });
         }
      })
    });
  }

  async atualizeParcelaMaximaPlano(plano: any){
    return new Promise( async (resolve, reject) => {
      let dados: any  = {  invoice_max_installments: plano.invoice_max_installments};
      console.log(dados);
      this.api.plans.update(plano.id, dados,  async (  err: any, resposta: any) => {
        if(resposta && resposta.id){
          resolve(resposta)
        } else {
          resolve(null)
        }
      });
    })
  }

  async removaFormaDePagamento(empresa: any, cartao: any){

    return new Promise( async (resolve, reject) => {
      if(!cartao.codigo) return reject ('codigo do cartão nao informado')
      if(!empresa.codigoCliente) return reject ('Empresa não possui um código de ativação válido')

      this.api.customers.deletePaymentMethod(empresa.codigoCliente,  cartao.codigo , (erro: any, resposta: any) => {
        if(resposta && resposta.id){
          this.removaPagamentoPadrao(empresa).then(() => {
            resolve(resposta)
          })
        } else {
          reject('Não foi possível remover cartao:  ' + this.obtenhaErro(resposta, erro))
        }
      });

    });
  }

  async crieFormaPagamento(empresa: Empresa, cartao: any, plano: any) {
    return new Promise( async (resolve, reject) => {
      if(!cartao.token) return reject ('Token do cartão nao informado')
      if(!empresa.codigoCliente) return reject ('Empresa não possui um código de ativação válido')

      let dados: any = { description: 'Cartão Credito',
        token: cartao.token
      };

      if(plano && plano.ehMensal())
        dados.set_as_default =  true ;
      else
        dados.set_as_default =  false ;

      console.log(dados)

      this.api.customers.listPaymentMethod(empresa.codigoCliente, {}, (e: any, payments: any) => {
        let payment: any;

        if(payments && payments.errors) return reject(payments.errors)

        if(payments){
          payments.forEach( (card: any) => {
            if(cartao.numero.startsWith(card.data.first_digits) && cartao.numero.endsWith( card.data.last_digits))
              payment = card;

          })
        }

        if(!payment){
          this.api.customers.createPaymentMethod(empresa.codigoCliente,  dados , (erro: any, resposta: any) => {
            if(resposta && resposta.id){
              resolve(resposta)
            } else {
              reject('Não foi possível criar a meio de pagamento:  ' + this.obtenhaErro(resposta, erro))
            }
          });
        } else {
          resolve(payment)
        }
      })
    })
  }

  private obtenhaFormasPagamentosValidas( formaDePagamento: string){
    let formasdePagamentoIugu: any = [];
    let lista =  formaDePagamento.split(',');

    lista.forEach((forma: string) => {
      let formapagamentoIugu: string;
      // tslint:disable-next-line:forin
      for (let key in EnumFormaPagamentoIugu){
        if(EnumFormaPagamentoIugu[key as keyof  typeof EnumFormaPagamentoIugu] === forma)
          formapagamentoIugu = key;
      }

      if(formapagamentoIugu)
        formasdePagamentoIugu.push(formapagamentoIugu)
    })

    if(lista.length !== formasdePagamentoIugu.length) return null

    return formasdePagamentoIugu;
  }

  async crieAssinatura(empresa: Empresa, contrato: Contrato, formaDePagamento: string, itens: any = null) {
    return new Promise( async (resolve, reject) => {
      if(!empresa) return reject('Empresa não informada')
      if(!contrato) return reject('Empresa não tem contrato cadastrado.');
      if(!formaDePagamento) return reject('Nenhuma forma de pagamento informada');

      let formasdePagamentoIugu: any = this.obtenhaFormasPagamentosValidas(formaDePagamento)

      if(!formasdePagamentoIugu)
          return reject('Forma de pagamento invalida: ' + formaDePagamento);

      if(contrato.assinatura) return reject('Contrato já tem uma assinatura vinculada.');

      let customer: any = await this.obtenhaOuCrieCliente(empresa, contrato.plano.ehMensal()).catch( (erro => {
        return reject(erro)
      }));

      let vencimento = contrato.obtenhaVencimentoPrimeiraCobranca();

      if(customer) {
        let dadosAssinatura: any = {
          plan_identifier: contrato.plano.obtenhaIdentificador(),
          customer_id: customer.id,
          expires_at: vencimento.format('DD-MM-YYYY'), //DD-MM-AAAA
          payable_with: formasdePagamentoIugu,
          subitems: [],
          custom_variables: [ { name: "contrato", value: contrato.id }]
        }


        if(itens){
          this.setItensAssinatura(dadosAssinatura, itens)
        } else { // tela antiga
          this.setRecorrenciasAssinatura(contrato, formaDePagamento,  dadosAssinatura);
        }

        let query = {customer_id: customer.id, plan_identifier: dadosAssinatura.plan_identifier};

        this.api.subscriptions.list( query , (er: any, resposta: any) => {
          if(er){
            console.log(er)
            return reject('Não foi possível verificar assinaturas do  cliente: ' + er.message)
          }


          //if(!resposta.errors){
            //return reject('Não foi possível verificar assinaturas do  cliente: ' + resposta.errors)
         // }


          let subscription  =  resposta.items ?  resposta.items.find( (item: any) =>
            item.suspended === false && item.custom_variables.find((dados: any) =>
                                            dados.name === 'contrato' && dados.value === contrato.id.toString()) != null  ) : null;

          if(!subscription){
            console.log(dadosAssinatura)
            this.api.subscriptions.create(dadosAssinatura, (erro2: any, novaAssinatura: any) => {
              if(novaAssinatura.id){
                resolve(novaAssinatura)
              } else {
                reject('Não foi possível criar assinatura: ' + this.obtenhaErro(novaAssinatura, erro2))
              }
            });
          } else {
            console.log('Já existe assinatura ativa do cliente')
            resolve(subscription);
          }
        })

      }

    });
  }

  async crieFaturaAvulsa(empresa: any, itens: [], formaDePagamento: string, dataVencimento: Date, parcelas: number ,
                         referenciaExterna: string = null, custom_variables: any = []){
    return new Promise( async (resolve, reject) => {
        if(!empresa.codigoCliente) return reject('Empresa não criada no Iugu ainda')

        let dados: any = {
          customer_id: empresa.codigoCliente,
          payable_with: formaDePagamento,
          email: empresa.email,
          max_installments_value: parcelas,
          ignore_due_email: true,
          external_reference: referenciaExterna,
          due_date: moment(dataVencimento).format('YYYY-MM-DD'),
          items:  itens.map((item: any) =>  (
            { description: item.descricao, quantity: item.qtde, price_cents: item.total * 100 }))
        }

       if(!Ambiente.Instance.producao)
        dados.customer_id  = '0E20DEA8E1214ED2AF03D6B3B50FE628'

        if(custom_variables && custom_variables.length)
           dados.custom_variables  = custom_variables;

        console.log(dados)

        this.api.invoices.create(dados ,  (erro: any, novaFatura: any) => {
          if( novaFatura && novaFatura.id){
            resolve(novaFatura)
          } else {
            reject(this.obtenhaErro(novaFatura, erro))
          }
      })
    })
  }

  async crieFaturaAdesao(empresa: any,   valorTaxa: number, formaDePagamento: string , dataVencimento: Date){
    return new Promise( async (resolve, reject) => {
      let dadosCobranca =  new DTODadosCobranca(empresa);

      let customer: any = await this.obtenhaOuCrieCliente(empresa, true).catch( (erro => {
        return reject(erro)
      }));

      if(customer){
        let dados: any = {
          customer_id: customer.id,
          payable_with: formaDePagamento,
          email: dadosCobranca.email,
          due_date: moment(dataVencimento).format('YYYY-MM-DD'),
          items: [
            { description: 'Taxa de adesão', quantity: 1, price_cents: valorTaxa * 100 }
          ]
        }

        this.api.invoices.create(dados ,  (erro: any, novaFatura: any) => {
          if( novaFatura && novaFatura.id){
            resolve(novaFatura)
          } else {
            reject(this.obtenhaErro(novaFatura, erro))
          }
        })
      }


    })
  }

  async recrieFatura(assinatura: Assinatura, fatura: Fatura){

    return new Promise( async (resolve, reject) => {
      if(!assinatura.codigo) return reject('Assinatura inválida')

      let dataVencimento = moment(fatura.dataVencimento).toDate();

      if(moment(dataVencimento).isBefore(moment()))
        dataVencimento = DateUtils.obtenhaVencimentoDiaUtil();

      let dadosCobranca =  new DTODadosCobranca(assinatura.empresa);

      let dados: any = {
        email: dadosCobranca.email,
        subscription_id: assinatura.codigo,
        due_date: moment(dataVencimento).format('YYYY-MM-DD'),
        items: []
      }

      fatura.getDados().lancamentos.forEach((lancamento: any) => {
        dados.items.push({
          description: lancamento.description,
          price_cents: lancamento.description.price_cents,
          quantity: lancamento.quantity,
          recurrent: lancamento.recurrent
        })
      })

      this.api.invoices.create(dados ,  (erro: any, novaFatura: any) => {
        if( novaFatura && novaFatura.id){
          resolve(novaFatura)
        } else {
          reject(this.obtenhaErro(novaFatura, erro))
        }
      })

    });

  }

  async crieFatura(assinatura: Assinatura, contrato: Contrato,  dataVencimento: Date ){
    return new Promise( async (resolve, reject) => {
      if(!assinatura.codigo) return reject('Assinatura inválida')
      let dadosCobranca =  new DTODadosCobranca(contrato.empresa);

      if(moment(dataVencimento).isBefore(moment()))
        dataVencimento = DateUtils.obtenhaVencimentoDiaUtil();

      let dados: any = {
        email: dadosCobranca.email,
        subscription_id: assinatura.codigo,
        due_date: moment(dataVencimento).format('YYYY-MM-DD'),
      }

      dados.items =  this.obtenhaItensFatura(contrato, assinatura.formasDePagamento)

      this.api.invoices.create(dados ,  (erro: any, novaFatura: any) => {
        if( novaFatura && novaFatura.id){
          resolve(novaFatura)
        } else {
          reject(this.obtenhaErro(novaFatura, erro))
        }
      })
    })

  }

  async executeCobrancaCartao(fatura: any, idCartao: string, numeroParcelas: number = null){
    return new Promise( async (resolve, reject) => {

      if(!fatura) return reject('Fatura é obrigatoria');
      if(!fatura.codigo) return reject('Codigo da fatura inválida');

      let dados: any = {
        invoice_id: fatura.codigo,
        customer_payment_method_id: idCartao
      };

      if(numeroParcelas) dados.months = numeroParcelas

      console.log('**executar cobrança cartão: ')
      console.log(dados)
      this.api.charge.create(dados, async(erro: any, resposta: any) => {
        if(resposta && resposta.success   ){
          console.log(resposta)
          resolve(resposta)
        } else {
          reject('Pagamento não aprovado: '  + this.obtenhaErro(resposta, erro))
        }
      })

    })
  }

  async ativeAssinatura(codigo: string){
    return new Promise( async (resolve, reject) => {
      this.api.subscriptions.activate(codigo, (err: any, resp: any) => {
        if(resp && resp.id){
          resolve(resp)
        } else {
          reject(this.obtenhaErro(resp, err))
        }

      })
    })
  }

  async suspendaAssinatura(codigo: string){
    return new Promise( async (resolve, reject) => {
      this.api.subscriptions.suspend(codigo, (err: any, resp: any) => {
        if(resp && resp.suspended){
          resolve(resp)
        } else {
          reject(this.obtenhaErro(resp, err))
        }
      })
    })
  }

  async removaItemAssinatura(codigo: any, item: any){
    return new Promise( async (resolve, reject) => {
      let itensRemovidos: any = [ {
        id: item.id,
        _destroy: true
      }];

      let dadosAssinatura: any = {  subitems: itensRemovidos }, subscription: any;

      console.log(dadosAssinatura)

      this.api.subscriptions.update(codigo, dadosAssinatura, async (erro: any, resposta: any) => {
        if(resposta && resposta.id){
          subscription = resposta;

          if(item.faturaPendente){
            let novaFatura: any =
              await this.gere2ViaFatura(item.faturaPendente.codigo, item.faturaPendente.dataVencimento, itensRemovidos);

            if(novaFatura) this.adicioneFaturaRecente(subscription, novaFatura);

          } else {
            resolve(subscription)
          }
        } else {
          reject('Não foi possível remover item na assinatura: ' + this.obtenhaErro(erro, resposta));
        }
      });

    });
  }
  async adicioneItemAssinatura(codigo: any, descricao: string, qtde: number, valor: number, recorrente: boolean,
                               faturaPendente: any){
    return new Promise( async (resolve, reject) => {
      let novosItem: any = [ {
        description: descricao,
        price_cents: Number( (valor * 100).toFixed(2) ),
        quantity: qtde,
        recurrent: recorrente
      }];


      //assinatura de teste
      if(!Ambiente.Instance.producao)
          codigo = 'C07811BCC062410BAA3DE6AF540A2BAA'

      let dadosAssinatura: any = {  subitems: novosItem }, subscription: any;

      //tem fatura e nao recorrente, adicionar direto na 2° via da fatura paranao passar proximo ciclo
      let adicionarSoNaFatura = !recorrente && faturaPendente != null;

      async.series([
          (cb) => {
           if(adicionarSoNaFatura){
             this.obtenhaAssinatura(codigo).then((resposta: any) => {
               subscription = resposta;
               cb();
             })
           } else {
             this.api.subscriptions.update(codigo, dadosAssinatura, async (erro: any, resposta: any) => {
               if(resposta && resposta.id){
                 subscription = resposta;
                 cb();
               } else {
                 cb(erro || resposta);
               }
             });
           }
          },

          async () => {
            if(faturaPendente){
              let novaFatura = await this.gere2ViaFatura(faturaPendente.codigo, faturaPendente.dataVencimento, novosItem);
              if(novaFatura)
                this.adicioneFaturaRecente(subscription, novaFatura);
            }
          }
        ], (erro: any) => {
            if(erro) {
              console.log(erro)
              reject('Não foi possível inserir item na assinatura: ' + this.obtenhaErro(erro, erro));
            } else {
              resolve(subscription)
            }
      });
    })

  }


  async altereValorAssinatura(contrato: Contrato, assinatura: Assinatura){
    return new Promise( async (resolve, reject) => {
      if(!assinatura.codigo) return reject('Assinatura nao possui código.')

      //hoje , o itens sao sao calculados corretamente, se tiver um item add por fora nao é recalcualdo.
      if(!assinatura.ehNova() && contrato.valorNegociado)
        return reject('Valor da assinatura não pode ser alterado, ela já foi iniciada.')

      let dadosAssinatura: any = {
        subitems: []
      }

      let subscription: any =  await this.obtenhaAssinatura(assinatura.codigo);
      let faturasRecentes = subscription.recent_invoices;
      let faturaPendente: any = _.findWhere(faturasRecentes, { status: 'pending'})

      this.calculeNovosItensAssinatura(contrato, subscription.payable_with, subscription, dadosAssinatura);

      //inclui o dia do vencimento
      let dataVencimentoFuturo =
        moment(subscription.expires_at, 'YYYY-MM-DD').startOf('day').diff(moment().startOf('day'), 'day') >  0;

      if(!dataVencimentoFuturo )
        dadosAssinatura.expires_at =   moment().add(1, 'd').format('YYYY-MM-DD');


      async.series([
        (alterarAssinatura: Function) => {
          this.api.subscriptions.update(assinatura.codigo, dadosAssinatura, (erro2: any, resposta: any) => {
            if(resposta.id){
              subscription = resposta;
              alterarAssinatura();
            } else {
              alterarAssinatura('Não foi possível atualizar assinatura: ' + this.obtenhaErro(resposta, erro2));
            }
          });
        },
        async () => {
          let novaFatura: any ;

          if(faturaPendente){
            let invoice: any = await this.obtenhaFatura(faturaPendente.id);

            let novosItens: any =  this.obtenhaItensFatura(contrato, subscription.payable_with);

            if(invoice.items && invoice.items.length){
              invoice.items.forEach( (item: any) => {
                novosItens.push({
                  id: item.id,
                  _destroy: true
                })
              } )
            }

            novaFatura =  await this.gere2ViaFatura(faturaPendente.id, faturaPendente.due_date , novosItens)
            faturaPendente.status = 'canceled'

          }

          if(novaFatura)
            this.adicioneFaturaRecente(subscription, novaFatura)

        }
      ], (erro) => {

        if(!erro){
          resolve(subscription)
        } else {
          reject(erro)
        }
      })

    })
  }

  private calculeNovosItensAssinatura(contrato: any, formaDePagamento: string, subscription: any, dadosAssinatura: any){
    this.setRecorrenciasAssinatura(contrato, formaDePagamento,  dadosAssinatura);

    if(subscription.subitems && subscription.subitems.length){
      subscription.subitems.forEach( (item: any) => {
        dadosAssinatura.subitems.push({
          id: item.id,
          _destroy: true
        })
      } )
    }
  }

  async altereDataVencimentoAssinatura(assinatura: Assinatura, dataVencimento: Date): Promise<string>{
    let dadosAssinatura: any = {
      expires_at: moment(dataVencimento).format('YYYY-MM-DD')
    }

    return new Promise( async (resolve, reject) => {
      if(moment(dataVencimento).diff(moment(), 'd') < 0)
        return resolve('Data vencimento tem ser hoje ou um data futura')

      this.api.subscriptions.update(assinatura.codigo, dadosAssinatura, async (erro2: any, resposta: any) => {
        if(resposta.id){
          await assinatura.atualizeDataVencimento(dataVencimento);
          resolve(null)
        } else {
          resolve('Não foi possível atualizar assinatura: ' + this.obtenhaErro(resposta, erro2));
        }
      });
    });
  }

  async altereFormaDePagamentoAssinatura(contrato: Contrato, assinatura: Assinatura, formasDePagamento: string){
    return new Promise( async (resolve, reject) => {
      if(!assinatura.codigo) return reject('Assinatura nao possui código.');
      if(!formasDePagamento || !formasDePagamento.length) return reject('Forma de pagamento não informada');

      let subscription: any =  await this.obtenhaAssinatura(assinatura.codigo);

      let formasdePagamentoIugu: any = this.obtenhaFormasPagamentosValidas(formasDePagamento)

      if(!formasdePagamentoIugu)
        return reject('Forma de pagamento invalida: ' + formasDePagamento);


      let dadosAssinatura: any = {
        payable_with: formasdePagamentoIugu
      }

      let removerCartao = assinatura.aceitaCartao() && assinatura.cartao  != null;

      assinatura.formasDePagamento = dadosAssinatura.payable_with;

      async.series([
        (alterarAssinatura: Function) => {
          this.api.subscriptions.update(assinatura.codigo, dadosAssinatura, (erro2: any, resposta: any) => {
            if(resposta.id){
              subscription = resposta;
              alterarAssinatura();
            } else {
              alterarAssinatura('Não foi possível atualizar assinatura: ' + this.obtenhaErro(resposta, erro2));
            }
          });
        },

        async () => {
          let faturasRecentes = subscription.recent_invoices;
          let faturaPendente: any = _.findWhere(faturasRecentes, { status: 'pending'})
          let faturaPagaDoMes: any =
            _.find(faturasRecentes, invoice => invoice.status === 'paid' &&  moment(invoice.due_date).isSame(moment(), 'month') )

          if(faturaPendente){

            let faturaAberta = contrato.faturas.find((item: any) => item.codigo === faturaPendente.id);

            await this.canceleFatura(faturaAberta.codigo);

            let novaFatura: any =  await this.recrieFatura(assinatura, faturaAberta);

            subscription.recent_invoices.push( {id: novaFatura.id, status: novaFatura.status,
              due_date: novaFatura.due_date, total: novaFatura.total,
              secure_url: novaFatura.secure_url
            });

          } else if (!faturaPagaDoMes && contrato.podeCriarFatura()){
            let novaFatura: any =  await this.crieFatura(assinatura, contrato,  contrato.getVencimentoMesCorrente().toDate());

            subscription.recent_invoices.push( {id: novaFatura.id, status: novaFatura.status,
              due_date: novaFatura.due_date, total: novaFatura.total,
              secure_url: novaFatura.secure_url
            });
          }

          if(removerCartao){
             await   this.removaPagamentoPadrao(assinatura.empresa);
             await assinatura.atualizeCartaoCredito(null);
          }
        }

      ], (erro) => {

         if(!erro){
           resolve(subscription)
         } else {
            reject(erro)
         }
      })
    })
  }

  async canceleECrieOutraAssinatura(contrato: Contrato, plano: any){
    return new Promise( async (resolve, reject) => {
      try{
        await this.suspendaAssinatura(contrato.assinatura.codigo);

        for(let i = 0; i < contrato.faturas.length; i++){
          let fatura =  contrato.faturas[i];
          fatura.status = EnumStatusFatura.Cancelada;
          await new MapeadorDeFatura().atualizeSync(fatura)
        }

        let formasDePagamento = contrato.assinatura.formasDePagamento;

        contrato.plano = plano;
        delete contrato.assinatura

        let subscription: any = await this.crieAssinatura(contrato.empresa, contrato, formasDePagamento).catch( (erroCriar) => {
           return  reject(erroCriar)
        })

        if(subscription && subscription.id){
          await contrato.atualizePlano(plano)
          return resolve(subscription)
        }

      } catch (e) {
        return reject(e)
      }

    })
  }
  async alterePlanoDaAssinatura(contrato: Contrato, plano: any){

    //evitar cobrar automatico
    await this.removaPagamentoPadrao(contrato.empresa);

    if(!contrato.jaPagouAlguma())
      return this.canceleECrieOutraAssinatura(contrato, plano);

    return new Promise( async (resolve, reject) => {
      let assinatura =  contrato.assinatura;
      let faturaAberto = contrato.obtenhaFaturaEmAberto();

      let dadosAssinatura: any = {
        plan_identifier: plano.identificador,
        ignore_due_email: true,
        skip_charge: !faturaAberto,
        subitems: []
      }

      let subscription: any =  await this.obtenhaAssinatura(assinatura.codigo);

      if(subscription.subitems && subscription.subitems.length){
        subscription.subitems.forEach( (item: any) => {
          dadosAssinatura.subitems.push({
            id: item.id,
            _destroy: true
          })
        } )
      }

      this.setRecorrenciasAssinatura(contrato, contrato.assinatura.formasDePagamento,  dadosAssinatura);

      console.log('alteração plano da assinatura')
      console.log(dadosAssinatura)
      this.api.subscriptions.update(assinatura.codigo, dadosAssinatura, (erro2: any, resposta: any) => {
        if(resposta && resposta.id){
          resolve(resposta);
        } else {
          reject('Não foi possível atualizar assinatura: ' + this.obtenhaErro(resposta, erro2));
        }
      });
    })
  }

 /* async alterePlanoAssinatura(assinatura: Assinatura, plano: any){
    return new Promise( async (resolve, reject) => {
      this.api.subscriptions.change_plan(assinatura.codigo, plano.identificador ,  (erro: any, subscription: any) => {
        if(subscription  && subscription.id ){
          resolve(subscription)
        } else {
          reject('Falha ao gerar nova via da fatura: ' + this.obtenhaErro(subscription, erro))
        }
      })
    })
  } */

  async gere2ViaFatura(codigo: string, dataVencimento: string,   items: []){
    return new Promise( async (resolve, reject) => {
      let due_date: any = dataVencimento.length === 8 ?  moment(dataVencimento, 'DD/MM/YY') :  moment(dataVencimento);

      if(due_date.startOf('d').isBefore(moment().startOf('d'))){
        console.log('Próximo vencimento será hoje')
        due_date = moment();
      }

      let dados = {  due_date: due_date.format('YYYY-MM-DD'),
                     items: items , ignore_canceled_email: true, ignore_due_email: true};

      console.log(dados)

      this.api.invoices.duplicate(codigo, dados ,  (erro: any, invoce: any) => {
        if(invoce  && invoce.id ){
          resolve(invoce)
        } else {
          reject('Falha ao gerar nova via da fatura: ' + this.obtenhaErro(invoce, erro))
        }
      })
    })
  }

  async canceleFatura(codigo: string){
    return new Promise( async (resolve, reject) => {
      if(!codigo) return reject('Codigo não informado');

      this.api.invoices.cancel(codigo ,  (erro: any, invoice: any) => {
          if(invoice  && invoice.status === 'canceled'){
            resolve(invoice)
          } else {
            reject('Falha ao cancelar fatura: ' + this.obtenhaErro(invoice, erro))
          }
      })
    })
  }

 async listeTodasTransacoes(ano: number, mes: number){
    let allTransactions: any[] = [];
    let start = 0;
    let pageSize = 100;
    let limit = pageSize;

    try {
      while (true) {
        const transactions: any = await this.listeTransacoes(ano, mes, start, limit);
        if (!transactions || transactions.length === 0) {
          // Nenhuma transação encontrada, encerrando o loop.
          break;
        }

        allTransactions = allTransactions.concat(transactions);

        // Atualizando os parâmetros para a próxima página
        start += pageSize;
        limit = pageSize;
      }

      return Promise.resolve(allTransactions);
    } catch (error) {
      throw error; // Você pode querer tratar ou relançar o erro conforme necessário.
    }
  }

  listeTransacoes(ano: number, mes: number, start = 0, limit = 100){
    //
    return new Promise(async (resolve, reject) => {
      let headers = { Accept: 'application/json'};

      axios.get(String(`${this.host}/accounts/financial?year=${ano}&month=${mes}&api_token=${this.token}&start=${start}&limit=${limit}`) ,
        { headers: headers}).then(   (response: any) => {
        resolve(response.data.transactions );
      }).catch((erro: any) => {
        reject(this.obtenhaErro('listar recebimentos', erro))
      })
    })
  }

  private setItensAssinatura( dadosAssinatura: any, itens: any){
    itens.forEach((item: any) => {
      dadosAssinatura.subitems.push({
        description: item.descricao,
        price_cents: Number( (item.valor * 100).toFixed(2) ),
        quantity: item.quantidade,
        recurrent: item.recorrente
      })

      if(item.moduloId && !item.taxaAtivacao)
         dadosAssinatura.custom_variables.push( { name: "moduloId", value: item.moduloId })
    })
  }

  private setRecorrenciasAssinatura(contrato: Contrato, formaDePagamento: string, dadosAssinatura: any) {
    let valorDesconto = contrato.obtenhaDescontoNegociado();

    if(contrato.plano.valor === 0 && contrato.valorNegociado ){
      dadosAssinatura.subitems.push({
        description: "Assinatura Promokit",
        price_cents: Number( (contrato.valorNegociado * 100).toFixed(2) ),
        quantity: 1,
        recurrent: true
      })
    }

    if(!contrato.jaPagouAlguma() && contrato.taxaAdesao > 0)
      dadosAssinatura.subitems.push({
        description: "Taxa de adesão",
        price_cents: Number( (contrato.taxaAdesao * 100).toFixed(2) ),
        quantity: 1,
        recurrent: false
      })

    if( valorDesconto > 0 ){

      dadosAssinatura.subitems.push({ description: "Desconto assinatura",
        price_cents: Number( -(valorDesconto * 100).toFixed()),
        quantity: 1,
        recurrent: true
      })
    } else  {
      if(formaDePagamento === 'credit_card' && contrato.plano.descontoCartao){
        dadosAssinatura.subitems.push({ description: "Desconto cartão",
          price_cents: Number( -(contrato.plano.descontoCartao  * 100).toFixed())  ,
          quantity: 1,
          recurrent: true
        })
      }
    }

  }

  private obtenhaItensFatura(contrato: Contrato, formasDePagamento: string){
    let items: any = [];

    if(!contrato.jaPagouAlguma() && contrato.taxaAdesao > 0)
      items.push({
        description: "Taxa de adesão",
        price_cents: Number( (contrato.taxaAdesao * 100).toFixed(2) ),
        quantity: 1,
        recurrent: false
      })

    let mensalidade = 0,   valorDesconto = 0;

    if(contrato.plano.valor > 0){
      mensalidade = Number( (contrato.plano.valor * 100).toFixed(2) );
      valorDesconto = contrato.obtenhaDescontoNegociado();
    } else if(contrato.valorNegociado){
      mensalidade = contrato.valorNegociado;
    }

    items.push( { description: 'Assinatura promokit', quantity: 1, price_cents: mensalidade});

    if( valorDesconto > 0 ){

      items.push({ description: "Desconto assinatura",
        price_cents: - Number((valorDesconto  * 100).toFixed(2)),
        quantity: 1,
        recurrent: true
      })
    } else  {
      if(formasDePagamento === 'credit_card'){
        items.push({ description: "Desconto cartão",
          price_cents: - Number((contrato.plano.descontoCartao * 100).toFixed(2)),
          quantity: 1,
          recurrent: true
        })
      }
    }

    return items;

  }

  private obtenhaErro(response: any, error: any) {
    let erro = '';
    console.log('###processar erro iugu####')
    if(response){
      console.log(response)
      let erroCartao: ErroCartao = ErrosCartaoUtils.obtenhaErro(response.LR);

      if(erroCartao)
        return erroCartao.obtehaMensagemErro();

      if(response.message)
        erro = response.message;
      if(response.errors){
        if(typeof response.errors === 'string'){
          erro =  response.errors
        } else {
          // tslint:disable-next-line:forin
          for(let key in response.errors){
            let listaErros: any = response.errors[key];

            erro += (key + " - " + listaErros.join(', ') + " ").trim()
          }
        }
      }
    } else {
      console.log(error)
      erro = 'Problema no processador pagamentos (IUGU): ' + error.message

    }

    return erro;
  }

  private adicioneFaturaRecente(subscription: any, novaFatura: any) {
    subscription.recent_invoices.push( {id: novaFatura.id, status: novaFatura.status,
      due_date: novaFatura.due_date, total: novaFatura.total,
      secure_url: novaFatura.secure_url
    });
  }


  private listeProximasAssinaturas(listaAssinaturas: any[], inicio: number, total: number){
    return new Promise(async(resolve, reject) => {
         let assinaturasPagina: any = await this.listeAssinaturas(inicio, total).catch((err) => {
               resolve(false);
         });

         if(assinaturasPagina){
           if(assinaturasPagina.length){
             listaAssinaturas.push(...assinaturasPagina);
             await this.listeProximasAssinaturas(listaAssinaturas, listaAssinaturas.length + 1, total);
             resolve(true)
           } else {
             resolve(true)
           }
         }
    })
  }

  async listeTodasAssinaturas() {
    return new Promise(async(resolve, reject) => {
      let  assinaturas: any = [];

      await this.listeProximasAssinaturas(assinaturas, 0, 100);

      resolve(assinaturas)
    });

  }
}
