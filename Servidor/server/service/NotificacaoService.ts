import {IEnviadorDeMensagem} from './IEnviadorDeMensagem';
import {SituacaoDeMensagem} from './SituacaoDeMensagem';
import {Notificacao} from '../domain/Notificacao';
import {MapeadorDeNotificacao} from '../mapeadores/MapeadorDeNotificacao';
import * as async from 'async';
import {VariaveisDeRequest} from './VariaveisDeRequest';
import {TipoDeNotificacaoEnum} from '../domain/TipoDeNotificacaoEnum';
import {Cartao} from '../domain/Cartao';
import {Contato} from '../domain/Contato';
import {MensagemEnviada} from '../domain/MensagemEnviada';
import {MapeadorDeMensagemEnviada} from '../mapeadores/MapeadorDeMensagemEnviada';
import {StatusDeMensagem} from "./StatusDeMensagem";
import {Empresa} from "../domain/Empresa";
import {Resposta} from "../utils/Resposta";
import {Ambiente} from "./Ambiente";
import {Campanha} from "../domain/Campanha";
import {RespostaEncurtarLinks} from "../utils/RespostaEncurtarLinks";
import {LinkEncurtado} from "../domain/LinkEncurtado";
import {EnumTipoDeEnvioCampanha} from "../domain/EnumTipoDeEnvioCampanha";
import {EnviadorDeMensagemSMSMulti} from "./EnviadorDeMensagemSMSMulti";
import * as moment from 'moment';
import {PontuacaoRegistrada} from "../domain/PontuacaoRegistrada";
import {Pedido} from "../domain/delivery/Pedido";
import {
  MensagemComandaNotificacao,
  MensagemPedidoNotificacao,
  TipoDeNotificacaoPorStatusPedido
} from "../lib/emun/EnumStatusPedido";
import {EnumMeioDeEnvio} from "../domain/EnumMeioDeEnvio";
import {PedidoUtils} from "../utils/PedidoUtils";
import {Usuario} from "../domain/Usuario";
import {NumeroWhatsapp} from "../domain/NumeroWhatsapp";
import {MapeadorDeContato} from "../mapeadores/MapeadorDeContato";
import {EnumStatusContato} from "../lib/emun/EnumStatusContato";
import {SessaoLinkSaudacao} from "../domain/SessaoLinkSaudacao";
import {MapeadorDeSessaoLinkSaudacao} from "../mapeadores/MapeadorDeSessaoLinkSaudacao";
import {Contrato} from "../domain/faturamento/Contrato";
import {MapeadorDeContrato} from "../mapeadores/MapeadorDeContrato";
import {PedidoGenerico} from "../domain/delivery/PedidoGenerico";
import {Comanda} from "../domain/comandas/Comanda";
import {FormaDeEntrega} from "../domain/delivery/FormaDeEntrega";
import {TipoDeContatoEnum} from "../domain/TipoDeContatoEnum";
import {Entregador} from "../domain/delivery/Entregador";

const randtoken = require('rand-token');

const request = require('request');

export class NotificacaoService {
  private enviadorDeMensagem: IEnviadorDeMensagem;

  constructor(enviadorDeMensagem: IEnviadorDeMensagem) {
    this.enviadorDeMensagem = enviadorDeMensagem;
  }

  async executeEnvioMensagens(contato: Contato, mensagemEnviada: MensagemEnviada, contexto: any): Promise<MensagemEnviada> {
    return new Promise<MensagemEnviada>(async (resolve, reject) => {
      try {
        const mapeadorDeMensagemEnviada = new MapeadorDeMensagemEnviada();

        console.log('Preparando para enviar mensagem "' + mensagemEnviada.mensagem + '" para o telefone ' + mensagemEnviada.telefone);

        // Salvar o contato do entregador se necessário
        if (mensagemEnviada.contato && !mensagemEnviada.contato.id && mensagemEnviada.contato.entregador) {
          let id = await new MapeadorDeContato().insiraSync(mensagemEnviada.contato);
          contato.id = id;
        }

        // Primeiro, salvar a mensagem no banco de dados para obter um ID
        if (!mensagemEnviada.id) {
          // Definir status inicial como Nova
          mensagemEnviada.status = StatusDeMensagem.Nova;
          
          // Salvar a mensagem para obter um ID
          await mapeadorDeMensagemEnviada.insiraSync(mensagemEnviada);
          
          // Atualizar o contexto se necessário
          if (contexto && contexto.sessaoLinkSaudacao) {
            contexto.sessaoLinkSaudacao.mensagemEnviada = mensagemEnviada;
            await new MapeadorDeSessaoLinkSaudacao().atualizeSync(contexto.sessaoLinkSaudacao);
          }
        }

        // Agora que temos um ID, enviar a mensagem
        const situacao = await this.enviadorDeMensagem.envieMensagem(
          mensagemEnviada,
          mensagemEnviada.telefone, 
          mensagemEnviada.mensagem
        );

        // Atualizar o status da mensagem após o envio
        mensagemEnviada.status = situacao.status;
        mensagemEnviada.idSMSExterno = situacao.idSMSExterno;
        mensagemEnviada.meioDeEnvio = this.enviadorDeMensagem.obtenhaMeioDeEnvio();

        // Atualizar a mensagem no banco de dados com o novo status
        await mapeadorDeMensagemEnviada.atualizeSync(mensagemEnviada);

        console.log('Mensagem enviada e atualizada com ID: ' + mensagemEnviada.id);

        // Notificar assinantes se não for campanha
        if (!mensagemEnviada.campanha) {
          this.enviadorDeMensagem.notifiqueAssinantes(mensagemEnviada);
        }

        resolve(mensagemEnviada);
      } catch (erro) {
        console.log('Erro ao enviar mensagem:');
        console.log(erro);
        reject(erro);
      }
    });
  }

  async envieMensagem(contato: Contato, numeroWhatsapp: NumeroWhatsapp, campanha: Campanha, telefone: string,
                      mensagem: string, links: Array<LinkEncurtado>, notificacao: Notificacao, tipoDeNotificao: TipoDeNotificacaoEnum,
                      contexto: any): Promise<MensagemEnviada> {
    const mensagemEnviada: MensagemEnviada = MensagemEnviada.nova(contato, numeroWhatsapp, mensagem, tipoDeNotificao,
      StatusDeMensagem.Nova, contexto.imagem);

    mensagemEnviada.campanha = campanha;
    if( mensagemEnviada.campanha )
      mensagemEnviada.imagem = mensagemEnviada.campanha.obtenhaUrlImagem();

    mensagemEnviada.links = links;

    if(notificacao){
      mensagemEnviada.temMenu = notificacao.temMenu;
      mensagemEnviada.menu = notificacao.menu
      mensagemEnviada.fazerPreview = notificacao.fazerPreview;
    }

    mensagemEnviada.processeMenu();

    const horarioMoment = moment();
    const horario = horarioMoment.toDate();

    return await this.executeEnvioMensagens(contato, mensagemEnviada, contexto);
  }

  verifiqueStatus(id: string): Promise<SituacaoDeMensagem> {
    return this.enviadorDeMensagem.acompanheMensagem(id);
  }

  private valide(notificacao: Notificacao): Promise<string> {
    return new Promise<string>( (resolve) => {
      if ( !notificacao ) {
        return resolve('Nenhuma nofificacao informada.');
      }

      if (!notificacao.mensagem) { return resolve('Notificação deve ter uma mensagem'); }

      if ( !notificacao.podeDesativar && !notificacao.ativada ) {
        return resolve('Notificação não pode ser desativada.');
      }

      let qtdeMaximaCaracteres = 160;

      if( notificacao.empresa.meioDeEnvio === EnumMeioDeEnvio.Whatsapp ||
        notificacao.empresa.meioDeEnvio === EnumMeioDeEnvio.Mock) {
        qtdeMaximaCaracteres = 1000;
      }
      if ( notificacao.mensagem.length > qtdeMaximaCaracteres ) {
        return resolve('Mensagem da notificação muito longa.');
      }

      resolve(null);
    });
  }

  atualize(notificacao: Notificacao): Promise<boolean> {
    return new Promise<boolean>( (resolve, reject) => {
      async.series([
        (cb: Function) => {
        console.log('validando');
          // @ts-ignore
          this.valide(notificacao).then( cb );
        },
        (cb: Function) => {
          console.log('inserindo');
          new MapeadorDeNotificacao().atualizeSync(notificacao).then((inseriu: any) => { cb(); });
        }
      ], (erro: any)   => {
        if ( erro ) { return reject(erro); }

        resolve(null);
      });
    });
  }

  public async crieLinkCardapio(contato: Contato, contexto: any, modoTeste: boolean = false ) {
    let contextoDoAmbiente = Ambiente.Instance.contexto()
    if(!contexto.linkCardapio && contextoDoAmbiente.empresa.temCardapio())
    {
      let sessaoLinkSaudacao

      if(!modoTeste) {
        sessaoLinkSaudacao = await SessaoLinkSaudacao.CrieSessao(contato, contato.telefone, contato.codigoPais);
        await new MapeadorDeSessaoLinkSaudacao().insiraGraph(sessaoLinkSaudacao);
      }


      const variaveisDeRequest = new VariaveisDeRequest();


      const link = variaveisDeRequest.obtenhaUrlCardapio(Ambiente.Instance.contexto().empresa)

      contexto.linkCardapio = link  + (modoTeste ? '' : '/link/' + contato.telefone + '/' + sessaoLinkSaudacao.hash)
      contexto.sessaoLinkSaudacao = sessaoLinkSaudacao;
      contexto.linkDesinscrever = link  + (modoTeste ? '' : '/link/' + contato.telefone + '/' + sessaoLinkSaudacao.hash) +
        '?url=configuracoes';

      if(contexto.linkPagamento)
        contexto.linkCardapio = `${contexto.linkCardapio}?url=${contexto.linkPagamento}`


      if( contexto.pedido && contexto.pedido.guid ) {
        contexto.linkAvaliarPedido = link + (modoTeste ? '' : '/link/' + contato.telefone + '/' + sessaoLinkSaudacao.hash) + '?url=' +
          encodeURIComponent('avaliar-pedido/' + contexto.pedido.guid);
      }
    }
  }

  private envieNotificacao(contato: Contato, tipoDeNotificacao: any, contexto: any = {}): Promise<Resposta<any>> {
    let campanha: Campanha = contexto.campanha;


    return new Promise<Resposta<any>>( (resolvePrincipal, rejectPrincipal) => {
      const variaveisDeRequest = new VariaveisDeRequest();
      let empresa: Empresa = contato.empresa;
      let notificacao: any = null;
      let mensagemEnviar = contexto.mensagem;
      let statusMensagem: SituacaoDeMensagem = null;
      let links: Array<LinkEncurtado>;
      let numeroWhatsapp: NumeroWhatsapp = null;
      let enviarSms = true;

      let mensagemEnviadaCriada: MensagemEnviada = null;

      this.crieLinkCardapio(contato, contexto, tipoDeNotificacao === TipoDeNotificacaoEnum.TesteCampanha
        || tipoDeNotificacao.indexOf('Etapa') !== -1).then(() => {

        if(contato.ehConsumidorNaoIndentificado() || !contato.telefone){
          console.log('nao enviar mensagem contatos nao identificados')
          console.log(contato.obtenhaDTOContato())
          return resolvePrincipal(null);
        }

        async.series([
          async function obtenhaEmpresaLogada(cb: Function) {
            return variaveisDeRequest.obtenhaEmpresaLogada().then(async (empresaDaRequest) => {
              if(!empresa)
                empresa = empresaDaRequest;

              // Para campanhas, usar número específico se existir, senão usar o principal
              if (campanha) {
                numeroWhatsapp = empresaDaRequest.obtenhaNumeroWhatsappParaCampanhas();
              } else if( empresaDaRequest.numeroWhatsapp ) {
                numeroWhatsapp = empresaDaRequest.numeroWhatsapp;
              }
            });
          },
          async function obtenhaNumeroWhatsapp(cb: Function) {//se usuário logado está associado a um whatsapp
            return variaveisDeRequest.obtenhaUsuarioLogado().then(async (usuarioDaRequest: Usuario) => {
              if( usuarioDaRequest && usuarioDaRequest.numeroWhatsapp ) {
                numeroWhatsapp = usuarioDaRequest.numeroWhatsapp;
              }
            });
          },
          async function obtenhaNumeroWhatsappPeloOperador(cb: Function) {//se usuário logado está associado a um whatsapp
            return variaveisDeRequest.obtenhaOperadorRequest().then(async (operadorDaRequest: Usuario) => {
              if( operadorDaRequest && operadorDaRequest.numeroWhatsapp ) {
                numeroWhatsapp = operadorDaRequest.numeroWhatsapp;
              }
            });
          },
          async function verifiqueSeNotificacaoEstahAtiva(cb: Function) {
            return new Promise<void>( async (resolve: any, reject: any) => {
              if(tipoDeNotificacao === TipoDeNotificacaoEnum.CodigoConfirmacao) return resolve();
              if (campanha) {
                notificacao = new Notificacao();
                notificacao.mensagem = campanha.mensagem;
                notificacao.temMenu = campanha.temMenu;
                notificacao.menu = campanha.menu;

                /*
                if( campanha.inserirLinkDesinscrever ) {
                  notificacao.mensagem += `\n
_Caso não queira mais receber nossas promoções e ofertas, acesse_ \n_[Link_Desinscrever]_`;
                }
                */
                notificacao.tipoDeNotificacao = tipoDeNotificacao;

                if( campanha.naoEnviarMsgParaQuemRecebeuRecente ) {
                  notificacao.qtdeDeDiasNovaNotificacao = campanha.qtdeDiasUltimaNotificacao;
                } else {
                  notificacao.qtdeDeDiasNovaNotificacao = 0;
                }

                return resolve();
              }

              const mapeadorDeNotificacao = new MapeadorDeNotificacao();
              const dados = {empresa: empresa, tipoDeNotificacao: tipoDeNotificacao};

              return mapeadorDeNotificacao.selecioneSync(
                dados).then((objNotificacao) => {
                notificacao = objNotificacao;

                if (!notificacao) {
                  return reject('Não foi possível encontrar uma notificação do tipo ' + tipoDeNotificacao +
                    ' para a empresa ' + empresa.id);
                }

                if (!notificacao.ativada) {
                  return reject(String(`Notificação "${tipoDeNotificacao}"  não está ativada`));
                }

                resolve();
              });
            });
          },

          async function gereMensagemFinal() {
            return new Promise<void>( (resolve: any, reject: any) => {
              if(!mensagemEnviar){

                notificacao.obtenhaMensagemProcessada(empresa, contato, contexto).then((resposta: RespostaEncurtarLinks) => {

                  if(resposta && resposta.mensagemFinal){
                    mensagemEnviar = resposta.mensagemFinal;
                    links = resposta.links;

                    resolve();
                  } else {
                    let erro = 'Não foi possivel determinar mensagem final da mensagem '
                    console.error(erro)
                    reject(erro);
                  }

                }).catch(  (erro: any) => {
                  console.error(erro)
                  reject(erro);
                });
              } else {
                resolve();
              }
            });
          },

          async function verifiqueSeNaoEhDuplicada() {
            return new Promise<void>( async (resolve: any, reject: any) => {
              const mapeadorDeMensagemEnviada = new MapeadorDeMensagemEnviada();

              if( tipoDeNotificacao === TipoDeNotificacaoEnum.TesteCampanha  ||
                tipoDeNotificacao === TipoDeNotificacaoEnum.ConfirmacaoPedido ||
                tipoDeNotificacao === TipoDeNotificacaoEnum.PedidoConfirmado ||
                tipoDeNotificacao === TipoDeNotificacaoEnum.PedidoAlterado ||
                tipoDeNotificacao === TipoDeNotificacaoEnum.PedidoCancelado ||
                tipoDeNotificacao === TipoDeNotificacaoEnum.PedidoEntregue ||
                tipoDeNotificacao === TipoDeNotificacaoEnum.PedidoSaiuParaEntrega ||
                tipoDeNotificacao === TipoDeNotificacaoEnum.PedidoEmPreparacao ||
                tipoDeNotificacao === TipoDeNotificacaoEnum.PedidoPronto ||
                tipoDeNotificacao === TipoDeNotificacaoEnum.CodigoConfirmacao ||
                tipoDeNotificacao === TipoDeNotificacaoEnum.PedidoRealizadoCardapioOnline
              ) {
                return resolve();
              }

              const mensagensEnviadas = contato.id ? await mapeadorDeMensagemEnviada.listeAsync({
                contato: contato,
                tipo: tipoDeNotificacao,
                enviadasNosultimosDias: notificacao.qtdeDeDiasNovaNotificacao,
                statusDiferente: StatusDeMensagem.Cancelada
              }) : []; //se é um contato inexistente, não precisa consultar o banco

              //campanha não é Agendada então deve Marcar Duplicidade
              let marcarEnvioDuplicidade = !campanha || (campanha.tipoDeEnvio !== EnumTipoDeEnvioCampanha.Agendado);

              if ( mensagensEnviadas.length > 0 ) {
                const situacao = new SituacaoDeMensagem();

                situacao.mensagem = 'Mensagem não pode ser enviada. Já foi enviada outra recentemente';
                situacao.status = StatusDeMensagem.Duplicada;

                enviarSms = false;

                if( marcarEnvioDuplicidade ) {
                  const mensagemEnviada = MensagemEnviada.nova(contato, numeroWhatsapp, mensagemEnviar,
                    tipoDeNotificacao, StatusDeMensagem.Duplicada);
                  mensagemEnviada.campanha = campanha;
                  if( campanha ) {
                    mensagemEnviada.imagem = campanha.obtenhaUrlImagem();
                  }

                  mapeadorDeMensagemEnviada.insiraSync(mensagemEnviada).then((inseriu: any) => {
                    resolve();
                  });
                } else if( campanha ) {
                } else {
                  reject("Já enviou a mensagem");
                }
              } else {
                resolve();
              }
            });
          },

          async () => {
            return new Promise<void>( (resolve: any, reject: any) => {
              if(!enviarSms) return resolve();
              contato.empresa = empresa;

              this.envieMensagem(contato, numeroWhatsapp, campanha, contato.telefone, mensagemEnviar, links,
                notificacao, tipoDeNotificacao, contexto)
                .then( (objMensagemEnviada: any)  => {
                  mensagemEnviadaCriada = objMensagemEnviada
                  resolve();
                }).catch( (erro) => {
                reject(erro);
              });
            });
          }
        ], (erro: any) => {
          if ( !erro ) {
            resolvePrincipal(Resposta.sucesso(mensagemEnviadaCriada));
          } else {
            resolvePrincipal(Resposta.erro(erro.message));
          }
        });
      })
    });
  }

  envieNotificacaoAvaliarPedido(pedido: PedidoGenerico, contato: Contato) {
    return this.envieNotificacao(contato, TipoDeNotificacaoEnum.AvaliarPedido, {pedido: pedido});
  }



  envieNotificacaoCartaoCompleto(contato: Contato): Promise<Resposta<any>> {
    return this.envieNotificacao(contato, TipoDeNotificacaoEnum.CartaoCompleto);
  }

  envieNotificacaoGanhouPontos(contato: Contato): Promise<Resposta<any>> {
    return this.envieNotificacao(contato, TipoDeNotificacaoEnum.GanhouPontos);
  }

  envieNotificacaoNovoContato(contato: Contato ): Promise<Resposta<any>> {
    const variaveisDeRequest = new VariaveisDeRequest();

    const link = variaveisDeRequest.obtenhaUrlRaiz(Ambiente.Instance.contexto().empresa) + contato.getLinkAtivacao();

    return this.envieNotificacao(contato, TipoDeNotificacaoEnum.NovoCartao, {linkCartao: link});
  }

  envieNotificacaoPedidoAssociadoAoEntregador(pedido: PedidoGenerico): Promise<Resposta<any>> {
    return new Promise((resolve) => {
      if(!pedido.entregador)
        return resolve(null);

      let dadosPedido = PedidoUtils.obtenhaDadosPedido(pedido.contato, pedido);
      let entregador = new Entregador()
      Object.assign(entregador, pedido.entregador)
      entregador.obtenhaContato().then((contato: Contato) => {
        if(!contato.token)
          contato.setToken(pedido.empresa, randtoken);
        this.envieNotificacao(contato, TipoDeNotificacaoEnum.PedidoAssociadoEntregador,
          {codigoPedido: pedido.codigo,
                   cliente: pedido.contato,
                   dadosPedido: dadosPedido} ).then((retorno: any) => {
          resolve(retorno)
        })

      })

    })
  }

  envieNotificacaoPedidoConfirmado(pedido: PedidoGenerico){
    const variaveisDeRequest = new VariaveisDeRequest();
    const link = variaveisDeRequest.obtenhaUrlRaiz(Ambiente.Instance.contexto().empresa)
    pedido.contato.empresa = pedido.empresa;
    return this.envieNotificacao(pedido.contato, TipoDeNotificacaoEnum.PedidoConfirmado,  pedido.obtenhaContexto(link));
  }

  envieCardapio(contato: Contato) {
    return this.envieNotificacao(contato, TipoDeNotificacaoEnum.Cardapio, {
      imagem: contato.empresa.cardapio.obtenhaUrlImagem(contato.empresa)
    });
  }

  envieLinkExtrato(contato: Contato){
    let linkExtratoCartao =  new VariaveisDeRequest().obtenhaUrlRaiz(contato.empresa) + contato.obtenhaLinkExtratoCartao();
    return this.envieNotificacao(contato, TipoDeNotificacaoEnum.LinkExtratoCartao, { linkExtratoCartao: linkExtratoCartao });
  }

  envieNotifificacaoPagamentoConfirmadoOnline(contato: Contato, pedido: PedidoGenerico){
    pedido.contato.empresa = pedido.empresa;
    let contexto: any = {codigoPedido: pedido.codigo};

    return this.envieNotificacao(contato, TipoDeNotificacaoEnum.PagamentoConfirmadoOnline, contexto );
  }

  envieNotificacaoPagamentoPendenteOnline(pedidoGuid: string, pagamento: any, contato: Contato){
    let contexto: any = {pagamento: pagamento, pedidoGuid: pedidoGuid, codigoPedido: pagamento.pedido.codigo};

    contexto.linkPagamento = `pedido/acompanhar/${pedidoGuid}`

    return this.envieNotificacao(contato, TipoDeNotificacaoEnum.PagamentoPendenteOnline, contexto );
  }

  envieConfirmacaoPedido(contato: Contato, pedido: PedidoGenerico, usuario: Usuario = null) {
    let msgConfirmacao = PedidoUtils.obtenhaMensagemConfirmacao(contato, pedido);
    let dadosPedido = PedidoUtils.obtenhaDadosPedido(contato, pedido);

    pedido.contato.empresa = pedido.empresa;

    return this.envieNotificacao(contato, TipoDeNotificacaoEnum.ConfirmacaoPedido, {
      confirmacaoPedido: msgConfirmacao,
      dadosPedido: dadosPedido,
      tipoDeEntrega: this.obtenhaTipoDeEntrega(pedido)
    });
  }

  envieCodigoConfirmacaoConta( contato: Contato, codigo: string){
    let mensagem =  this.obtenhaTextoMensagemValidacao(contato, codigo);

    return this.envieNotificacao(contato, TipoDeNotificacaoEnum.CodigoConfirmacao,
      {codigo: codigo, mensagem: mensagem});
  }

  private obtenhaTextoMensagemValidacao(contato: any, codigo: string){
    return contato.obtenhaPrimeiroNome()  + ", seu código de confirmação é " + codigo;
  }

  obtenhaMesnsagemValidarTelefone(empresa: Empresa, contato: Contato, codigo: string){
    return RespostaEncurtarLinks.naoEncurtar(this.obtenhaTextoMensagemValidacao(contato, codigo));
  }

  envieNotificacaoComandaFechada(comanda: Comanda) {
    if(!comanda.contato || comanda.contato.ehConsumidorNaoIndentificado()) return Promise.resolve();

    let contexto = { statusPedido: MensagemComandaNotificacao.get(comanda.status)}

    let tipoDeNotificacao = TipoDeNotificacaoEnum.ComandaFechada;

    return this.envieNotificacao(comanda.contato, tipoDeNotificacao, contexto);

  }

  obtenhaTipoDeEntrega(pedido: PedidoGenerico) {
    return pedido.formaDeEntrega && pedido.formaDeEntrega.id === FormaDeEntrega.ENTREGA ? 'ENTREGA' : 'RETIRADA'
  }


  envieNotificacaoPedidoAlterado(pedido: Pedido){
    let contexto: any = {
      statusPedido: MensagemPedidoNotificacao.get(pedido.status),
      tipoDeEntrega: this.obtenhaTipoDeEntrega(pedido)
    }

    if(pedido.deliveryPedido){
      contexto.urlRastreamento =  pedido.deliveryPedido.getUrlRastreamento()
      let pincode: string = pedido.deliveryPedido.getPincodeEntrega();

      if(pincode && pedido.aindaNaoEntregou())
        contexto.msgConfirmacaoEntrega =  "\n\nO Código para confirmar sua entrega é *" + pincode + "*";
    }

    let tipoDeNotificacao = TipoDeNotificacaoEnum.PedidoAlterado;

    if( TipoDeNotificacaoPorStatusPedido.get(pedido.status ) ) {
      tipoDeNotificacao = TipoDeNotificacaoPorStatusPedido.get(pedido.status);
    }

    return this.envieNotificacao(pedido.contato, tipoDeNotificacao, contexto);
  }

  envieNotificacaoResgatouCartao(contato: Contato, mensagem: string): Promise<Resposta<any>> {
    return this.envieNotificacao(contato, TipoDeNotificacaoEnum.ResgatouCartao, {mensagem: mensagem});
  }

  envieNotificacaoSaudades(contato: Contato) {
    return this.envieNotificacao(contato, TipoDeNotificacaoEnum.SentimosSuaFalta);
  }

  envieNotificacaoBemVindo(contato: Contato) {
    return this.envieNotificacao(contato, TipoDeNotificacaoEnum.SejaBemVindo)
  }

  envieNotificacaoClientePerdido(contato: Contato) {
    return this.envieNotificacao(contato, TipoDeNotificacaoEnum.ClientePerdido, { notificandoPerdidos: true});
  }

  envieNotificacaoAniversario(contato: Contato) {
    return this.envieNotificacao(contato, TipoDeNotificacaoEnum.Aniversario);
  }

  envieNotificacaoCarrinhoAbandonado(contato: any) {
    return this.envieNotificacao(contato, TipoDeNotificacaoEnum.CarrinhoAbandonado);
  }

  envieNotificacaoCampanha(campanha: Campanha, contato: Contato, envioTeste: boolean = false) {
    const tipoDeNotificacao = envioTeste ? TipoDeNotificacaoEnum.TesteCampanha : TipoDeNotificacaoEnum.Marketing;

    return this.envieNotificacao(contato, tipoDeNotificacao, {campanha: campanha});
  }

  envieNotificacaoPontosExpirar(cartao: Cartao, pontuacoes: Array<PontuacaoRegistrada>, tempoRestante: number) {
    let qtdePontosVenceu: any =  0;

    pontuacoes.forEach( (pontuacao: PontuacaoRegistrada) => {
      qtdePontosVenceu += pontuacao.obtenhaQtdePontosVenceu();
    })

    qtdePontosVenceu =  cartao.obtenhaDescricaoPontos(qtdePontosVenceu);

    let contexto = { pontosExpirar: qtdePontosVenceu, tempoRestante: tempoRestante + ' dias',
                    cartao: cartao
    };

    return this.envieNotificacao(cartao.contato, TipoDeNotificacaoEnum.PontosExpirar, contexto);
  }


  envieSMSCodigo(mensagem: string, telefone: string) {
    const variaveisDeRequest = new VariaveisDeRequest();

    let mapeadorDeMensagemEnviada = new MapeadorDeMensagemEnviada();

    return new Promise<Resposta<any>>(resolve => {
      mapeadorDeMensagemEnviada.selecioneSync({envioRecente: true, tipo: TipoDeNotificacaoEnum.ConfirmarCartao}).then((ultimoEnvio) => {
        if(ultimoEnvio) {
          let t1 = new Date();
          let t2 = ultimoEnvio.horario;
          let dif = t1.getTime() - t2.getTime();

          let Seconds_from_T1_to_T2 = dif / 1000;
          let segundosRestantes = 60 - Math.floor(Seconds_from_T1_to_T2);


          return resolve(Resposta.sucesso( {
            jaEnviado: true,
            segundosRestantes: segundosRestantes,
            mensagem: "Você enviou uma mensagem de confirmação há menos de um minuto. Aguarde antes de tentar novamente."}))
        }

        new EnviadorDeMensagemSMSMulti().envieSMS(telefone, mensagem).then( (resposta: any) => {
          resolve(resposta);
        });
      }).catch(excecao => {
        if(excecao)
          Resposta.erro("Houve um erro ao criar sua mensagem: " + excecao.message);
      });
    });
  }

  envieNotificacaoConfirmarContato(contato: Contato) {
    const variaveisDeRequest = new VariaveisDeRequest();

    const link = variaveisDeRequest.obtenhaUrlRaiz(Ambiente.Instance.contexto().empresa) + contato.getLinkAtivacao();

    let mapeadorDeMensagemEnviada = new MapeadorDeMensagemEnviada()

    return new Promise<Resposta<any>>(resolve => {
      mapeadorDeMensagemEnviada.selecioneSync({envioRecente: true, tipo: TipoDeNotificacaoEnum.ConfirmarCartao}).then((ultimoEnvio) => {
        if(ultimoEnvio) {
          let t1 = new Date();
          let t2 = ultimoEnvio.horario;
          let dif = t1.getTime() - t2.getTime();

          let Seconds_from_T1_to_T2 = dif / 1000;
          let segundosRestantes = 60 - Math.floor(Seconds_from_T1_to_T2);


          return resolve(Resposta.sucesso( {
            jaEnviado: true,
            segundosRestantes: segundosRestantes,
            mensagem: "Você enviou uma mensagem de confirmação há menos de um minuto. Aguarde antes de tentar novamente."}))
        }
        this.envieNotificacao(contato, TipoDeNotificacaoEnum.ConfirmarCartao, { linkCartao: link }).then( (resposta: any) => {
          resolve(resposta);
        });
      }).catch(excecao => {
        if(excecao)
          Resposta.erro("Houve um erro ao criar sua mensagem: " + excecao.message);
      });
    });
  }

  envieNotificacaoMarketing(empresa: Empresa, contato: Contato, etapa: string) {
    const variaveisDeRequest = new VariaveisDeRequest();

    return new Promise<Resposta<any>>(async (resolve) => {
      this.envieNotificacao(contato, 'Etapa ' + etapa,
        {}).then( (resposta: any) => {
        resolve(resposta);
      });
    });
  }

  envieNotificacaoSaudade(empresa: Empresa, nome: string, codigoPais: string, telefone: string, usuario: Usuario) {
    const variaveisDeRequest = new VariaveisDeRequest();

    return new Promise<Resposta<any>>(async (resolve) => {
      const contexto = await this.obtenhaLinkCardapio(empresa, nome, codigoPais, telefone, usuario);

      let contato = contexto.contato;
      this.envieNotificacao(contato, TipoDeNotificacaoEnum.MensagemSaudacaoWhatsappPedido,
        {}).then( (resposta: any) => {
        resolve(resposta);
      });
    });
  }


  envieNotificacaoClientesEmPerigo(notificacao: any, qtdeMaxima = 50){
    console.log('empresa ' + notificacao.empresa.nome  + ' contatos em perigo, dias:  ' + notificacao.qtdeDiasAtiva)

    return new Promise<void>(resolve => {
      let mapeadorDeContato = new MapeadorDeContato()

      if(notificacao.qtdeDiasAtiva <= 0) return resolve();

      mapeadorDeContato.listeAsync({ naoVolta: notificacao.qtdeDiasAtiva, emPerigo: true, orderByVisita: true}).then( contatos => {
        if(!contatos.length) return resolve();

        console.log('Total de contatos em perigo: ' + contatos.length)

        if(qtdeMaxima){
          contatos = contatos.splice(0, qtdeMaxima);
          console.log('Maximo de contatos notificar: ' + contatos.length)
        }

        async.forEachSeries(contatos, (contato: any, cb2) => {
          this.envieNotificacaoSaudades( contato  ).then( () => {
            contato.status = EnumStatusContato.EmPerigo;
            mapeadorDeContato.atualizeStatus(contato).then( (resp) => {
              cb2();
            })
          })
        }, () => {
          resolve();
        })
      })
    });
  }

  envieNotificacaoClientesPerdidos(notificacao: any, qtdeMaxima = 50){
    console.log('empresa ' + notificacao.empresa.nome  + ' contatos perdidos, dias:  ' + notificacao.qtdeDiasAtiva)
    return new Promise<void>(resolve => {

      let mapeadorDeContato = new MapeadorDeContato()

      if(notificacao.qtdeDiasAtiva <= 0) return resolve();

      mapeadorDeContato.listeAsync({ naoVolta: notificacao.qtdeDiasAtiva, vaiPerder: true, orderByVisita: true }).then( contatos => {

        console.log('[task perdidos] contatos: total: ' + contatos.length);

        if(!contatos.length) return resolve();

        console.log('Total de contatos perdidos: ' + contatos.length + ' na empresa ' + notificacao.empresa.nome);

        if(qtdeMaxima){
          contatos = contatos.splice(0, qtdeMaxima);
          console.log('Maximo de contatos notificar: ' + contatos.length)
        }

        async.forEachSeries(contatos, (contato: any, cb2) => {

          this.envieNotificacaoClientePerdido( contato  ).then( () => {
            contato.status = EnumStatusContato.Perdido;
            mapeadorDeContato.atualizeStatus(contato).then( (resp) => {
              cb2();
            })
          })
        }, () => {
          resolve();
        })
      }, () => {
        resolve();
      })

    });

  }

  async obtenhaLinkCardapio(empresa: Empresa, nome: string, codigoPais: string, telefone: string, usuario: Usuario,
                            pedidoGuid: string = null) {
    let contato = await new MapeadorDeContato().selecionePorTelefone({telefone: telefone, codigoPais: codigoPais});

    if( !contato ) {
      contato = new Contato(null, nome, telefone, null, null, null, null, codigoPais);
      contato.setToken(empresa, randtoken);
    }

    const variaveisDeRequest = new VariaveisDeRequest();

    const sessaoLinkSaudacao = await SessaoLinkSaudacao.CrieSessao(contato, telefone, codigoPais);

    if(pedidoGuid) sessaoLinkSaudacao.pedidoGuid  = pedidoGuid;

    await new MapeadorDeSessaoLinkSaudacao().insiraGraph(sessaoLinkSaudacao);

    const link = variaveisDeRequest.obtenhaUrlCardapio(empresa)
    let contexto: any = {
      linkCardapio: link  + '/link/' + telefone + '/' + sessaoLinkSaudacao.hash
    };
    contexto.contato = contato;

    contexto.sessaoLinkSaudacao = sessaoLinkSaudacao;

    let contrato: Contrato = await new MapeadorDeContrato().selecioneSync({ idEmpresa: empresa.id})

    if( contrato && contrato.qtdeOperadores > 1)
      contexto.linkCardapio += '?op=' + usuario.id;


    return contexto;
  }

  obtenhaMensagemLinkPagamentoPix(empresa: Empresa, pedido: Pedido, usuario: any): Promise<any> {
    return new Promise( async(resolve, reject) => {
      const mapeadorDeNotificacao = new MapeadorDeNotificacao();

      const notificacao: Notificacao = await mapeadorDeNotificacao.selecioneSync({
        tipoDeNotificacao: TipoDeNotificacaoEnum.LinkPagamentoPedido
      });

      let msgPedido: any = null, contato: Contato = pedido.contato;

      if( notificacao ) {

        const contexto = await this.obtenhaLinkCardapio(empresa, contato.nome, contato.codigoPais, contato.telefone, usuario, pedido.guid);

        msgPedido =  await notificacao.obtenhaMensagemProcessada(empresa, contato, contexto);

        msgPedido.fazerPreview = notificacao.fazerPreview;
        msgPedido.temMenu = notificacao.temMenu;
        msgPedido.menu = notificacao.menu;
        msgPedido.enviarLinksBotao = empresa.enviarLinksBotao;


        resolve({
          sucesso: true,
          data: {
            contato: contato,
            sessaoLinkSaudacao: contexto.sessaoLinkSaudacao,
            msg: msgPedido,
            marcarComoLida: notificacao.marcarComoLida
          }
        });
      } else {
        resolve({
          sucesso: true
        });
      }

    })
  }


  obtenhaMensagemSaudacao(empresa: Empresa, nome: string, codigoPais: string, telefone: string, usuario: Usuario): Promise<any> {
    return new Promise( (resolve, reject) => {
      const mapeadorDeNotificacao = new MapeadorDeNotificacao();

      mapeadorDeNotificacao.transacao( async(conexao: any, commit: any) => {
        const notificacao: Notificacao = await mapeadorDeNotificacao.selecioneSync({
          tipoDeNotificacao: TipoDeNotificacaoEnum.MensagemSaudacaoWhatsappPedido
        });

        let msgPedido: any = null;

        if( notificacao ) {
          const contexto = await this.obtenhaLinkCardapio(empresa, nome, codigoPais, telefone, usuario);

          let contato = contexto.contato;

          msgPedido =  await notificacao.obtenhaMensagemProcessada(empresa, contato, contexto);

          msgPedido.fazerPreview = notificacao.fazerPreview;
          msgPedido.temMenu = notificacao.temMenu;
          msgPedido.menu = notificacao.menu;
          msgPedido.enviarLinksBotao = empresa.enviarLinksBotao;

          commit( () => {
            resolve({
              sucesso: true,
              data: {
                contato: contato,
                sessaoLinkSaudacao: contexto.sessaoLinkSaudacao,
                msg: msgPedido,
                marcarComoLida: notificacao.marcarComoLida
              }
            });
          });
        } else {
          commit( () => {
            resolve({
              sucesso: true
            });
          });
        }
      });
    });
  }
}
