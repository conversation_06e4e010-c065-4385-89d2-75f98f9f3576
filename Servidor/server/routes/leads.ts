import {Router} from 'express';
import MapeadorDeLead from '../mapeadores/MapeadorDeLead';
import MapeadorDeLeadLink from '../mapeadores/MapeadorDeLeadLink';
import {Resposta} from '../utils/Resposta';
import {Lead} from '../domain/crm/Lead';
import { OrigemLead } from '../domain/crm/LeadEnums';
import { InstagramData } from '../domain/crm/LeadInterfaces';
import { CrmEmpresa } from '../domain/crm/CrmEmpresa';
import { TipoLinkLead, LeadLink } from '../domain/crm/LeadLink';
import MapeadorDeCrmEmpresa from '../mapeadores/MapeadorDeCrmEmpresa';
const { sample } = require('lodash');
import { ChatGPTService } from '../service/ia/ChatGPTService';
import { BitrixServiceFactory } from '../service/bitrix/BitrixService';
import { CnpjDiscoveryService } from '../service/CnpjDiscoveryService';
import { JSDOM } from 'jsdom';

const router: Router = Router();
const axios = require('axios');

// DEBUG: Verificar importação de Lead
console.log('LEADS: === VERIFICAÇÃO DE IMPORTAÇÃO NO INÍCIO ===');
console.log('LEADS: Lead importado?', !!Lead);
console.log('LEADS: Tipo de Lead:', typeof Lead);
console.log('LEADS: Lead.name:', Lead?.name);
console.log('LEADS: Lead é função?', typeof Lead === 'function');

// Lista de domínios e padrões de sistemas concorrentes de cardápio digital
const CONCORRENTES_CONHECIDOS = [
  // Principais concorrentes
  'goomer.com.br', 'goomer.app',
  'cardapio.delivery', 'cardapiodelivery.com',
  'menudigital.com', 'menudigital.com.br',
  'cardapiodigital.com', 'cardapiodigital.com.br',
  'qrmenu.com.br', 'qrmenu.com',
  'meniuapp.com', 'meniu.app',
  'cardapioqr.com', 'cardapioqr.com.br',
  'digitalmenubr.com', 'digitalmenu.com.br',
  'smartmenu.com.br', 'smartmenu.app',
  'easymenu.com.br', 'easymenu.app',
  'quickmenu.com.br', 'quickmenu.app',
  'fastmenu.com.br', 'fastmenu.app',
  'menufacil.com.br', 'menufacil.app',
  'cardapiosmart.com.br', 'cardapiosmart.app',
  'qrfood.com.br', 'qrfood.app',
  'menuqr.com.br', 'menuqr.app',
  'cardapioqrcode.com.br', 'cardapioqrcode.app',
  'digitalmenu.app', 'digitalmenu.io',
  'menuonline.com.br', 'menuonline.app',
  'cardapioonline.com.br', 'cardapioonline.app',
  'restaurantmenu.com.br', 'restaurantmenu.app',
  'menutouch.com.br', 'menutouch.app',
  'touchmenu.com.br', 'touchmenu.app',
  'tabletmenu.com.br', 'tabletmenu.app',
  'grandchef.com.br', 'grandchef.app',
  'mistercheff.com.br', 'mistercheff.app',
  'consumer.com.br', 'consumer.app',
  'saipos.com', 'saipos.app',
  'foodydelivery.com', 'foodydelivery.app',
  // Padrões comuns
  'cardapio', 'menu', 'qrmenu', 'digitalmenu', 'smartmenu', 'easymenu', 'quickmenu', 'fastmenu'
];

/**
 * Verifica se uma URL é de um sistema concorrente de cardápio digital
 */
function isUrlConcorrente(url: string): boolean {
  try {
    const urlObj = new URL(url);
    const hostname = urlObj.hostname.toLowerCase();

    // Verifica domínios exatos
    if (CONCORRENTES_CONHECIDOS.some(dominio => hostname.includes(dominio))) {
      return true;
    }

    // Verifica padrões na URL completa
    const urlCompleta = url.toLowerCase();
    const padroesConcorrentes = [
      'cardapio', 'menu', 'qr', 'digital', 'smart', 'easy', 'quick', 'fast',
      'tablet', 'touch', 'online', 'delivery', 'restaurant'
    ];

    // Se contém múltiplos padrões, é provável que seja concorrente
    const padroesEncontrados = padroesConcorrentes.filter(padrao =>
      urlCompleta.includes(padrao)
    );

    return padroesEncontrados.length >= 2;
  } catch {
    return false;
  }
}

// Rota para descobrir múltiplos CNPJs da empresa via busca no Google
router.get('/descobrir-cnpj', async (req: any, res) => {
  try {
    const { nomeEmpresa, cidade } = req.query;

    if (!nomeEmpresa || typeof nomeEmpresa !== 'string') {
      return res.json(Resposta.erro('Nome da empresa é obrigatório'));
    }

    console.log('Descobrindo CNPJs para empresa:', nomeEmpresa, cidade ? `em ${cidade}` : '');

    const cnpjDiscoveryService = new CnpjDiscoveryService();
    const resultado = await cnpjDiscoveryService.descobrirCnpj(nomeEmpresa, cidade);

    if (resultado.sucesso) {
      const totalEncontrados = resultado.data?.totalEncontrados || 0;
      console.log(`CNPJs descobertos: ${totalEncontrados} opções encontradas para "${nomeEmpresa}"`);

      if (totalEncontrados > 0) {
        console.log('Primeiros CNPJs encontrados:', resultado.data.cnpjsEncontrados.slice(0, 2));
      }

      res.json(resultado);
    } else {
      console.error('Erro ao descobrir CNPJs:', resultado.erro);
      res.json(resultado);
    }
  } catch (err) {
    console.error('Erro na rota descobrir-cnpj:', err);
    res.json(Resposta.erro('Erro interno ao descobrir CNPJ: ' + err.message));
  }
});

// Rota para buscar leads no Bitrix24 por telefone (com dados completos do primeiro lead)
router.get('/bitrix/buscar-por-telefone', async (req: any, res) => {
  try {
    const { telefone } = req.query;

    if (!telefone || typeof telefone !== 'string') {
      return res.json(Resposta.erro('Telefone é obrigatório'));
    }

    console.log('BITRIX: Buscando leads por telefone:', telefone);

    // Validar e normalizar telefone brasileiro (com ou sem código do país)
    let telefoneLimpo = telefone.replace(/[^\d]/g, '');

    console.log('BITRIX: Telefone original:', telefone);
    console.log('BITRIX: Telefone limpo inicial:', telefoneLimpo);

    // Remover código do país brasileiro (55) se presente (12-13 dígitos)
    if (telefoneLimpo.startsWith('55') && (telefoneLimpo.length === 12 || telefoneLimpo.length === 13)) {
      telefoneLimpo = telefoneLimpo.substring(2);
      console.log('BITRIX: Removido código do país 55:', telefoneLimpo);
    }

    // Validar formato após normalização (deve ter 10 ou 11 dígitos)
    if (telefoneLimpo.length < 10 || telefoneLimpo.length > 11) {
      return res.json(Resposta.erro(`Telefone inválido. Formato esperado: 10-11 dígitos (brasileiro) ou 12-13 dígitos (com 55). Recebido: ${telefoneLimpo.length} dígitos`));
    }

    // Verificar se é um número válido (não pode ser sequência óbvia)
    const sequenciasInvalidas = ['0000000000', '1111111111', '1234567890', '0123456789', '00000000000', '11111111111'];
    if (sequenciasInvalidas.includes(telefoneLimpo)) {
      return res.json(Resposta.erro('Número de telefone inválido (sequência não permitida)'));
    }

    // Verificar se DDD é válido (11-99)
    const ddd = parseInt(telefoneLimpo.substring(0, 2));
    if (ddd < 11 || ddd > 99) {
      return res.json(Resposta.erro(`DDD inválido: ${ddd}. Use um DDD brasileiro válido (11-99)`));
    }

    console.log('BITRIX: Telefone normalizado para busca:', telefoneLimpo);

    // Criar instância do serviço Bitrix24
    const bitrixService = BitrixServiceFactory.criarInstancia();

    // PASSO 1: Buscar leads básicos por telefone
    console.log('BITRIX: Passo 1 - Buscando leads básicos por telefone...');
    const resultadoBusca = await bitrixService.buscarLeadsPorTelefone(telefone);

    if (!resultadoBusca.sucesso) {
      console.error('BITRIX: Erro ao buscar leads:', resultadoBusca.erro);
      return res.json(Resposta.erro(resultadoBusca.erro));
    }

    const totalEncontrados = resultadoBusca.data?.length || 0;
    console.log(`BITRIX: ${totalEncontrados} leads encontrados para telefone ${telefone}`);

    if (totalEncontrados === 0) {
      return res.json(Resposta.sucesso({
        leadPrincipal: null,
        outrosLeads: [],
        telefoneConsultado: telefone,
        telefoneLimpo: telefoneLimpo,
        mensagem: 'Nenhum lead encontrado com este telefone'
      }));
    }

    // PASSO 2: Buscar dados completos do primeiro lead
    const primeiroLead = resultadoBusca.data[0];
    const leadId = parseInt(primeiroLead.id);

    console.log(`BITRIX: Passo 2 - Buscando dados completos do lead principal (ID: ${leadId})...`);
    const resultadoCompleto = await bitrixService.buscarLeadCompleto(leadId);

    let leadPrincipal = null;
    let leadConvertido = null;

    if (resultadoCompleto.sucesso) {
      leadPrincipal = resultadoCompleto.data;
      console.log(`BITRIX: Dados completos obtidos para: ${leadPrincipal.titulo || leadPrincipal.nomeCompleto}`);

      // PASSO 2.1: Converter dados do Bitrix24 para Lead do sistema
      try {
        console.log('BITRIX: Passo 2.1 - Convertendo para Lead do sistema...');
        // Usar crmEmpresaId = 1 como padrão (pode ser ajustado conforme necessário)
        leadConvertido = bitrixService.converterParaLead(leadPrincipal, 1);
        console.log(`BITRIX: Lead convertido com sucesso: ${leadConvertido.nomeResponsavel} (${leadConvertido.empresa})`);
      } catch (errorConversao) {
        console.warn('BITRIX: Erro ao converter lead para formato do sistema:', errorConversao.message);
        leadConvertido = null;
      }
    } else {
      console.warn(`BITRIX: Erro ao buscar dados completos do lead ${leadId}:`, resultadoCompleto.erro);
      // Se falhar, usar dados básicos do primeiro lead
      leadPrincipal = primeiroLead;
    }

    // PASSO 3: Preparar lista dos outros leads (se houver)
    const outrosLeads = resultadoBusca.data.slice(1).map((lead: any) => ({
      id: lead.id,
      titulo: lead.titulo,
      nome: lead.nome,
      sobrenome: lead.sobrenome,
      empresa: lead.empresa,
      status: lead.status,
      dataCriacao: lead.dataCriacao,
      resumo: `${lead.titulo || lead.nome || 'Lead'} - ${lead.empresa || 'Empresa não informada'}`
    }));

    console.log(`BITRIX: Resposta preparada - Lead principal + ${outrosLeads.length} outros leads`);

    // RESPOSTA FINAL: Lead principal completo + lead convertido + resumo dos outros
    res.json(Resposta.sucesso(leadConvertido));

  } catch (err) {
    console.error('BITRIX: Erro na rota buscar-por-telefone:', err);
    res.json(Resposta.erro('Erro interno ao buscar leads no Bitrix24: ' + err.message));
  }
});

// Rota para buscar lead completo no Bitrix24 por ID
router.get('/bitrix/buscar-por-id/:id', async (req: any, res) => {
  try {
    const { id } = req.params;

    if (!id) {
      return res.json(Resposta.erro('ID do lead é obrigatório'));
    }

    // Validar se ID é um número válido
    const leadId = parseInt(id);
    if (isNaN(leadId) || leadId <= 0) {
      return res.json(Resposta.erro('ID do lead deve ser um número positivo'));
    }

    console.log('BITRIX: Buscando lead completo por ID:', leadId);

    // Criar instância do serviço Bitrix24
    const bitrixService = BitrixServiceFactory.criarInstancia();

    // Buscar lead completo no Bitrix24
    const resultado = await bitrixService.buscarLeadCompleto(leadId);

    if (resultado.sucesso) {
      const dadosBitrix = resultado.data;
      console.log(`BITRIX: Lead encontrado: ${dadosBitrix.titulo || dadosBitrix.nomeCompleto}`);
      console.log(`BITRIX: Empresa: ${dadosBitrix.empresa || 'Não informada'}`);
      console.log(`BITRIX: Status: ${dadosBitrix.status.nome}`);
      console.log(`BITRIX: Telefones: ${dadosBitrix.telefones.length}`);
      console.log(`BITRIX: Emails: ${dadosBitrix.emails.length}`);

      // Converter dados do Bitrix24 para Lead do sistema
      let leadConvertido = null;
      try {
        console.log('BITRIX: Convertendo para Lead do sistema...');
        // Usar crmEmpresaId = 1 como padrão (pode ser ajustado conforme necessário)
        leadConvertido = bitrixService.converterParaLead(dadosBitrix, 1);
        console.log(`BITRIX: Lead convertido com sucesso: ${leadConvertido.nomeResponsavel} (${leadConvertido.empresa})`);

        res.json(Resposta.sucesso(leadConvertido));
      } catch (errorConversao) {
        console.warn('BITRIX: Erro ao converter lead para formato do sistema:', errorConversao.message);

        // Se falhar na conversão, retornar dados originais do Bitrix24
        res.json(Resposta.sucesso({
          lead: dadosBitrix,
          leadConvertido: null,
          leadId: leadId,
          dataConsulta: new Date().toISOString(),
          observacoes: {
            conversaoSucesso: false,
            erroConversao: errorConversao.message,
            metodoBusca: 'id_dados_bitrix_originais'
          }
        }));
      }
    } else {
      console.error('BITRIX: Erro ao buscar lead:', resultado.erro);
      res.json(Resposta.erro(resultado.erro));
    }
  } catch (err) {
    console.error('BITRIX: Erro na rota buscar-por-id:', err);
    res.json(Resposta.erro('Erro interno ao buscar lead no Bitrix24: ' + err.message));
  }
});

/**
 * Analisa os sócios e identifica automaticamente o sócio principal
 * baseado em critérios inteligentes como cargo, ordem e antiguidade
 */
function analisarSocioPrincipal(socios: any[], razaoSocial?: string): any[] {
  if (!socios || socios.length === 0) return socios;

  console.log('🔍 Analisando sócio principal entre', socios.length, 'sócios...');

  // Se há apenas 1 sócio, é automaticamente o principal
  if (socios.length === 1) {
    socios[0].principal = true;
    socios[0].scoreAnalise = 100;
    socios[0].motivoSelecao = 'Sócio único da empresa';
    console.log('✅ Sócio único identificado:', socios[0].nome);
    return socios;
  }

  // Calcular score para cada sócio
  socios.forEach((socio, index) => {
    let score = 0;
    let motivos: string[] = [];

    // 1. CRITÉRIO: Cargo/Qualificação (peso 40)
    const cargo = (socio.cargo || '').toLowerCase();
    if (cargo.includes('diretor') || cargo.includes('presidente') || cargo.includes('ceo') ||
        cargo.includes('administrador') || cargo.includes('proprietário')) {
      score += 40;
      motivos.push('Cargo de direção');
    } else if (cargo.includes('gerente') || cargo.includes('sócio-gerente') ||
               cargo.includes('sócio gerente')) {
      score += 25;
      motivos.push('Cargo gerencial');
    } else if (cargo.includes('administrador') || cargo.includes('responsável')) {
      score += 20;
      motivos.push('Cargo administrativo');
    } else {
      score += 10; // Sócio comum
    }

    // 2. CRITÉRIO: Posição na lista (peso 20)
    // Primeiro sócio geralmente é o principal
    if (index === 0) {
      score += 20;
      motivos.push('Primeiro na lista');
    } else if (index === 1) {
      score += 10;
    } else {
      score += Math.max(0, 10 - index); // Decrementa conforme posição
    }

    // 3. CRITÉRIO: Similaridade com razão social (peso 20)
    if (razaoSocial && socio.nome) {
      const nomeCompleto = socio.nome.toLowerCase();
      const razaoLower = razaoSocial.toLowerCase();

      // Verifica se o nome do sócio está contido na razão social
      const palavrasNome = nomeCompleto.split(' ').filter((p: string) => p.length > 2);
      const palavrasRazao = razaoLower.split(' ');

      let palavrasEncontradas = 0;
      palavrasNome.forEach((palavra: string) => {
        if (palavrasRazao.some((r: string) => r.includes(palavra) || palavra.includes(r))) {
          palavrasEncontradas++;
        }
      });

      if (palavrasEncontradas > 0) {
        const similaridade = (palavrasEncontradas / palavrasNome.length) * 20;
        score += similaridade;
        if (similaridade > 10) {
          motivos.push('Nome similar à empresa');
        }
      }
    }

    // 4. CRITÉRIO: Data de entrada (peso 20)
    if (socio.dataEntrada) {
      try {
        const partesData = socio.dataEntrada.split('/');
        if (partesData.length === 3) {
          const dataEntrada = new Date(parseInt(partesData[2]), parseInt(partesData[1]) - 1, parseInt(partesData[0]));
          const agora = new Date();
          const anosNaEmpresa = (agora.getTime() - dataEntrada.getTime()) / (1000 * 60 * 60 * 24 * 365);

          if (anosNaEmpresa >= 5) {
            score += 20;
            motivos.push('Sócio antigo (5+ anos)');
          } else if (anosNaEmpresa >= 2) {
            score += 15;
            motivos.push('Sócio experiente (2+ anos)');
          } else if (anosNaEmpresa >= 1) {
            score += 10;
          } else {
            score += 5;
          }
        }
      } catch (error) {
        // Se não conseguir calcular antiguidade, dá pontuação neutra
        score += 10;
      }
    } else {
      score += 10; // Pontuação neutra se não tem data
    }

    // Armazenar score e motivos no objeto do sócio
    socio.scoreAnalise = Math.round(score);
    socio.motivoSelecao = motivos.join(', ');
    socio.principal = false; // Reset - será definido abaixo

    console.log(`📊 ${socio.nome}: Score ${socio.scoreAnalise} (${socio.motivoSelecao})`);
  });

  // Encontrar sócio com maior score
  const socioComMaiorScore = socios.reduce((melhor, atual) =>
    atual.scoreAnalise > melhor.scoreAnalise ? atual : melhor
  );

  // Marcar como principal
  socioComMaiorScore.principal = true;

  console.log(`🎯 Sócio principal selecionado: ${socioComMaiorScore.nome} (Score: ${socioComMaiorScore.scoreAnalise})`);
  console.log(`   Motivos: ${socioComMaiorScore.motivoSelecao}`);

  return socios;
}

// Rota para buscar detalhes dos sócios de uma empresa
router.post('/buscar-detalhes-socios', async (req: any, res) => {
  try {
    const { cnpj } = req.body;

    if (!cnpj || typeof cnpj !== 'string') {
      return res.json(Resposta.erro('CNPJ é obrigatório'));
    }

    console.log('Buscando detalhes dos sócios para CNPJ:', cnpj);

    // Limpar CNPJ removendo caracteres especiais
    const cnpjLimpo = cnpj.replace(/[^\d]/g, '');

    if (cnpjLimpo.length !== 14) {
      return res.json(Resposta.erro('CNPJ inválido'));
    }

    try {
      // Fazer chamada para API pública de CNPJ
      const url = `https://publica.cnpj.ws/cnpj/${cnpjLimpo}`;
      console.log('Consultando API pública:', url);

      const response = await axios.get(url, {
        timeout: 10000, // 10 segundos de timeout
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
      });

      if (response.data && response.data.socios) {
        console.log(`Empresa encontrada: ${response.data.razao_social}`);
        console.log(`Total de sócios: ${response.data.socios.length}`);

        // Formatar CPF para ocultar parcialmente
        const formatarCPF = (cpf: string): string => {
          if (!cpf || cpf.length < 11) return cpf;
          // Remover caracteres não numéricos
          const cpfLimpo = cpf.replace(/[^\d]/g, '');
          // Ocultar dígitos do meio
          if (cpfLimpo.length === 11) {
            return `***.${cpfLimpo.substr(3, 3)}.***-**`;
          }
          return cpf;
        };

        // Formatar data
        const formatarData = (data: string): string => {
          if (!data) return '';
          const partes = data.split('-');
          if (partes.length === 3) {
            return `${partes[2]}/${partes[1]}/${partes[0]}`;
          }
          return data;
        };

        // Mapear sócios para o formato esperado pelo frontend
        const sociosFormatados = response.data.socios.map((socio: any, index: number) => ({
          nome: socio.nome,
          cpf: socio.cpf_cnpj_socio ? formatarCPF(socio.cpf_cnpj_socio) : null,
          participacao: null as string | null, // API não fornece percentual de participação
          cargo: socio.qualificacao_socio?.descricao || 'Sócio',
          dataEntrada: formatarData(socio.data_entrada),
          qualificacao: socio.qualificacao_socio?.descricao || 'Sócio',
          faixaEtaria: socio.faixa_etaria,
          tipo: socio.tipo,
          principal: false, // Será definido pela análise inteligente
          observacoes: '',
          scoreAnalise: 0,
          motivoSelecao: ''
        }));

        // ✨ APLICAR ANÁLISE INTELIGENTE PARA IDENTIFICAR SÓCIO PRINCIPAL
        const sociosAnalisados = analisarSocioPrincipal(sociosFormatados, response.data.razao_social);

        // Adicionar informações extras da empresa
        const dadosResposta = {
          socios: sociosAnalisados,
          empresa: {
            razaoSocial: response.data.razao_social,
            capitalSocial: response.data.capital_social,
            porte: response.data.porte?.descricao,
            naturezaJuridica: response.data.natureza_juridica?.descricao,
            situacaoSimples: response.data.simples?.simples,
            mei: response.data.simples?.mei
          }
        };

        res.json(Resposta.sucesso(dadosResposta));
      } else {
        console.log('Resposta da API não contém dados de sócios');
        res.json(Resposta.sucesso({ socios: [] }));
      }
    } catch (apiErr: any) {
      if (apiErr.response?.status === 404) {
        console.log('CNPJ não encontrado na base de dados');
        res.json(Resposta.erro('CNPJ não encontrado na base de dados pública'));
      } else if (apiErr.code === 'ECONNABORTED') {
        console.error('Timeout ao consultar API de CNPJ');
        res.json(Resposta.erro('Tempo esgotado ao buscar dados. Tente novamente.'));
      } else {
        console.error('Erro ao consultar API de CNPJ:', apiErr.message);
        res.json(Resposta.erro('Erro ao consultar dados do CNPJ'));
      }
    }
  } catch (err) {
    console.error('Erro na rota buscar-detalhes-socios:', err);
    res.json(Resposta.erro('Erro ao buscar detalhes dos sócios: ' + err.message));
  }
});

// Rota para analisar website e extrair informações
router.get('/analisar-website', async (req: any, res) => {
  try {
    const { url } = req.query;

    if (!url || typeof url !== 'string') {
      return res.json(Resposta.erro('URL é obrigatória'));
    }

    // Validar se é uma URL válida
    try {
      new URL(url);
    } catch {
      return res.json(Resposta.erro('URL inválida'));
    }

    console.log('Analisando website:', url);

    // Buscar o HTML da página
    let html;
    try {
      const response = await axios.get(url, {
        timeout: 10000,
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
      });
      html = response.data;
    } catch (error) {
      console.error('Erro ao buscar conteúdo do website:', error.message);
      return res.json(Resposta.erro('Não foi possível acessar o website. Verifique se a URL está correta e acessível.'));
    }

    // Constrói o DOM em memória usando JSDOM
    const dom = new JSDOM(html);
    const doc = dom.window.document;

    // Extrair todos os links da página
    const linkElements = doc.querySelectorAll('a[href]');
    const links: string[] = [];

    linkElements.forEach(link => {
      const href = link.getAttribute('href');
      if (href) {
        // Converter links relativos em absolutos
        try {
          const absoluteUrl = new URL(href, url).href;
          if (!links.includes(absoluteUrl)) {
            links.push(absoluteUrl);
          }
        } catch {
          // Se não conseguir converter, adiciona o link original
          if (!links.includes(href)) {
            links.push(href);
          }
        }
      }
    });

    console.log(`Encontrados ${links.length} links no website:`, links);

    // Se há links, categorizar automaticamente usando IA
    let linksCategorized = [];
    if (links.length > 0) {
      try {
        console.log('Categorizando links automaticamente...');

        // Prompt para categorizar os links usando IA
        const prompt = `
Você é um especialista em análise de links para categorização de leads comerciais.
Analise a lista de links fornecida e categorize cada um conforme os tipos disponíveis.

TIPOS DISPONÍVEIS:
- Ifood: Links do iFood (ifood.com.br)
- Site do Cardápio: Sites de cardápio online próprios ou neutros
- Concorrente: Sistemas de cardápio digital concorrentes (goomer, cardapio.delivery, menudigital, qrmenu, meniuapp, grandchef, mistercheff, consumer, saipos, etc.)
- Reservas: Sistemas de reservas (opentable, resy, etc.)
- WhatsApp: Links do WhatsApp (wa.me, whatsapp.com, api.whatsapp.com)
- Localização: Links de mapas e localização (google.com/maps, g.page, goo.gl/maps)
- Site: Site principal da empresa ou institucional
- Instagram: Links do Instagram (instagram.com)

DOMÍNIOS CONCORRENTES CONHECIDOS:
goomer.com.br, goomer.app, cardapio.delivery, menudigital.com, qrmenu.com.br, meniuapp.com,
cardapiodigital.com, smartmenu.com.br, easymenu.com.br, quickmenu.com.br, fastmenu.com.br,
menufacil.com.br, cardapiosmart.com.br, qrfood.com.br, menuqr.com.br, grandchef.com.br,
mistercheff.com.br, consumer.com.br, saipos.com, foodydelivery.com, digitalmenu.app,
menuonline.com.br, cardapioonline.com.br, menutouch.com.br, touchmenu.com.br, tabletmenu.com.br

REGRAS PARA DETECTAR CONCORRENTES:
- URLs que contenham domínios da lista acima = Concorrente
- URLs com padrões como: cardapio + digital/qr/online/smart = Concorrente
- URLs com padrões como: menu + digital/qr/online/smart = Concorrente
- URLs que combinem palavras: cardapio, menu, qr, digital, smart, easy, quick, fast, tablet, touch, online = Concorrente
- Se houver dúvida entre "Site do Cardápio" e "Concorrente", prefira "Concorrente" se a URL contiver os padrões acima

Retorne um JSON com a seguinte estrutura EXATA:

{
  "links": [
    {
      "url": "url_original",
      "tipo": "tipo_categorizado",
      "descricao": "descricao_do_link",
      "ordem": numero_da_ordem
    }
  ]
}

REGRAS IMPORTANTES:
- Para WhatsApp, extraia o número do telefone na descrição se possível
- Para Instagram, use o nome do perfil na descrição
- Para Localização, mencione "Ver localização" ou similar
- Para iFood, use "Cardápio iFood"
- Para Sites de cardápio próprios, use "Cardápio Online"
- Para Concorrentes, use "Sistema [Nome do Concorrente]" (ex: "Sistema Goomer", "Sistema MenuDigital")
- Para Site principal, use "Site oficial" ou nome da empresa
- A ordem deve começar em 1 e seguir a relevância (WhatsApp=1, Concorrente=2, Site=3, Instagram=4, etc.)
- Use descrições claras e úteis para o usuário final
- PRIORIZE a detecção de concorrentes - é muito importante identificá-los corretamente

Lista de links para análise:
${JSON.stringify(links, null, 2)}
`;

        // Chamar ChatGPT para categorizar links
        const chatGPTService = new ChatGPTService();
        const resultado: any = await chatGPTService.chameOpenAIChat(
          'sistema',
          'categorizar_links',
          prompt,
          JSON.stringify(links),
          [],
          0.3,
          '[categorizar-links]',
          { type: "json_object" }
        );

        if (resultado && resultado.text) {
          try {
            const parsedResult = JSON.parse(resultado.text);
            if (parsedResult.links && Array.isArray(parsedResult.links)) {
              linksCategorized = parsedResult.links;

              // Validação adicional para detectar concorrentes que a IA pode ter perdido
              linksCategorized = linksCategorized.map((link: any) => {
                if (link.tipo === 'Site do Cardápio' && isUrlConcorrente(link.url)) {
                  console.log(`Corrigindo categorização: ${link.url} é um concorrente`);
                  return {
                    ...link,
                    tipo: 'Concorrente',
                    descricao: link.descricao.replace('Cardápio Online', 'Sistema Concorrente'),
                    ordem: 2 // Prioridade alta para concorrentes
                  };
                }
                return link;
              });

              console.log('Links categorizados com sucesso:', linksCategorized);
            }
          } catch (parseError) {
            console.error('Erro ao fazer parse da categorização:', parseError);
          }
        }
      } catch (error) {
        console.error('Erro ao categorizar links:', error);
        // Continua sem categorização em caso de erro
      }
    }

    // Retorna tanto os links brutos quanto os categorizados
    const resultado = {
      linksRaw: links,
      linksCategorized: linksCategorized,
      total: links.length
    };

    res.json(Resposta.sucesso(resultado));

  } catch (err) {
    console.error('Erro ao analisar website:', err);
    res.json(Resposta.erro('Erro ao analisar website: ' + err.message));
  }
});

// Rota para categorizar lista de links usando IA
router.post('/categorizar-links', async (req: any, res) => {
  try {
    const { links } = req.body;

    if (!links || !Array.isArray(links) || links.length === 0) {
      return res.json(Resposta.erro('Lista de links é obrigatória'));
    }

    console.log('Categorizando links:', links);

    // Prompt para categorizar os links usando IA
    const prompt = `
Você é um especialista em análise de links para categorização de leads comerciais.
Analise a lista de links fornecida e categorize cada um conforme os tipos disponíveis.

TIPOS DISPONÍVEIS:
- Ifood: Links do iFood (ifood.com.br)
- Site do Cardápio: Sites de cardápio online próprios ou neutros
- Concorrente: Sistemas de cardápio digital concorrentes (goomer, cardapio.delivery, menudigital, qrmenu, meniuapp, grandchef, mistercheff, consumer, saipos, etc.)
- Reservas: Sistemas de reservas (opentable, resy, etc.)
- WhatsApp: Links do WhatsApp (wa.me, whatsapp.com, api.whatsapp.com)
- Localização: Links de mapas e localização (google.com/maps, g.page, goo.gl/maps)
- Site: Site principal da empresa ou institucional
- Instagram: Links do Instagram (instagram.com)

DOMÍNIOS CONCORRENTES CONHECIDOS:
goomer.com.br, goomer.app, cardapio.delivery, menudigital.com, qrmenu.com.br, meniuapp.com,
cardapiodigital.com, smartmenu.com.br, easymenu.com.br, quickmenu.com.br, fastmenu.com.br,
menufacil.com.br, cardapiosmart.com.br, qrfood.com.br, menuqr.com.br, grandchef.com.br,
mistercheff.com.br, consumer.com.br, saipos.com, foodydelivery.com, digitalmenu.app,
menuonline.com.br, cardapioonline.com.br, menutouch.com.br, touchmenu.com.br, tabletmenu.com.br

REGRAS PARA DETECTAR CONCORRENTES:
- URLs que contenham domínios da lista acima = Concorrente
- URLs com padrões como: cardapio + digital/qr/online/smart = Concorrente
- URLs com padrões como: menu + digital/qr/online/smart = Concorrente
- URLs que combinem palavras: cardapio, menu, qr, digital, smart, easy, quick, fast, tablet, touch, online = Concorrente
- Se houver dúvida entre "Site do Cardápio" e "Concorrente", prefira "Concorrente" se a URL contiver os padrões acima

Retorne um JSON com a seguinte estrutura EXATA:

{
  "links": [
    {
      "url": "url_original",
      "tipo": "tipo_categorizado",
      "descricao": "descricao_do_link",
      "ordem": numero_da_ordem
    }
  ]
}

REGRAS IMPORTANTES:
- Para WhatsApp, extraia o número do telefone na descrição se possível
- Para Instagram, use o nome do perfil na descrição
- Para Localização, mencione "Ver localização" ou similar
- Para iFood, use "Cardápio iFood"
- Para Sites de cardápio próprios, use "Cardápio Online"
- Para Concorrentes, use "Sistema [Nome do Concorrente]" (ex: "Sistema Goomer", "Sistema MenuDigital")
- Para Site principal, use "Site oficial" ou nome da empresa
- A ordem deve começar em 1 e seguir a relevância (WhatsApp=1, Concorrente=2, Site=3, Instagram=4, etc.)
- Use descrições claras e úteis para o usuário final
- PRIORIZE a detecção de concorrentes - é muito importante identificá-los corretamente

Lista de links para análise:
${JSON.stringify(links, null, 2)}
`;

    // Chamar ChatGPT para categorizar links
    const chatGPTService = new ChatGPTService();
    const resultado: any = await chatGPTService.chameOpenAIChat(
      'sistema',
      'categorizar_links',
      prompt,
      JSON.stringify(links),
      [],
      0.3,
      '[categorizar-links]',
      { type: "json_object" }
    );

    if (!resultado || !resultado.text) {
      console.error('Erro na resposta da IA:', resultado);
      return res.json(Resposta.erro('Erro ao categorizar links com IA'));
    }

    // Parse da resposta JSON
    let linksCategorized;
    try {
      linksCategorized = JSON.parse(resultado.text);
    } catch (parseError) {
      console.error('Erro ao fazer parse da resposta da IA:', parseError);
      return res.json(Resposta.erro('Erro ao interpretar resposta da IA'));
    }

    console.log('Links categorizados:', linksCategorized);

    // Validar estrutura da resposta
    if (!linksCategorized.links || !Array.isArray(linksCategorized.links)) {
      console.error('Estrutura inválida na resposta da IA:', linksCategorized);
      return res.json(Resposta.erro('Estrutura de dados inválida retornada pela IA'));
    }

    // Validação adicional para detectar concorrentes que a IA pode ter perdido
    linksCategorized.links = linksCategorized.links.map((link: any) => {
      if (link.tipo === 'Site do Cardápio' && isUrlConcorrente(link.url)) {
        console.log(`Corrigindo categorização: ${link.url} é um concorrente`);
        return {
          ...link,
          tipo: 'Concorrente',
          descricao: link.descricao.replace('Cardápio Online', 'Sistema Concorrente'),
          ordem: 2 // Prioridade alta para concorrentes
        };
      }
      return link;
    });

    res.json(Resposta.sucesso(linksCategorized.links));

  } catch (err) {
    console.error('Erro ao categorizar links:', err);
    res.json(Resposta.erro('Erro ao categorizar links: ' + err.message));
  }
});


// Rota dadosig que recebe dados do Instagram prontos
router.post('/dadosig', async (req: any, res) => {
  try {
    const { instagramData, crmEmpresaId } = req.body;

    if (!instagramData || !instagramData.data || !instagramData.data.user) {
      return res.json(Resposta.erro('Dados do Instagram são obrigatórios'));
    }
    // crmEmpresaId agora é opcional – se não vier será criado/selecionado automaticamente

    const user = instagramData.data.user;
    const username = user.username;

    console.log('Processando dados do Instagram para:', username);

    // Debug dos bio_links
    if (user?.bio_links) {
      console.log('Bio Links encontrados:', JSON.stringify(user.bio_links, null, 2));
    }
    if (!user) {
      console.error('Dados do usuário não encontrados nos dados do Instagram');
      return res.json(Resposta.erro('Dados do usuário do Instagram não encontrados'));
    }

    // Obter ou criar empresa
    let crmEmpresaIdNumeric: number;
    let empresaObj: CrmEmpresa;

    if (crmEmpresaId) {
      crmEmpresaIdNumeric = parseInt(crmEmpresaId as string, 10);
      const mapeadorCrmEmpresa = new MapeadorDeCrmEmpresa();
      empresaObj = await mapeadorCrmEmpresa.selecioneSync({ id: crmEmpresaIdNumeric });
    } else {
      const nomeEmpresa = user.full_name || user.username;
      const mapeadorCrmEmpresa = new MapeadorDeCrmEmpresa();

      // Tenta encontrar empresa existente pelo nome (primeira ocorrência)
      const existente = await mapeadorCrmEmpresa.selecioneSync({ nome: nomeEmpresa });

      if (existente && existente.id) {
        crmEmpresaIdNumeric = existente.id;
        empresaObj = existente;
      } else {
        const novaEmpresa = new CrmEmpresa(nomeEmpresa);
        // Preencher dados básicos quando disponíveis
        if (user.business_phone_number) novaEmpresa.telefone = user.business_phone_number;
        if (user.business_email) novaEmpresa.email = user.business_email;
        if (user.business_address_json) novaEmpresa.endereco = JSON.stringify(user.business_address_json);

        const criada = await mapeadorCrmEmpresa.insiraSync(novaEmpresa);

        crmEmpresaIdNumeric = criada.id;
        empresaObj = novaEmpresa;
      }
    }

    // Montar objeto instagramData conforme nosso domínio
    const instagramDataFormatted: InstagramData = {
      bio: user.biography,
      followers: user.edge_followed_by?.count,
      following: user.edge_follow?.count,
      accountType: (user.is_business_account ? 'Business' : 'Pessoal') as 'Business' | 'Pessoal',
      businessCategory: user.business_category_name || user.category_name,
      location: user.business_address_json ? JSON.stringify(user.business_address_json) : undefined,
      website: user.external_url
    };

    // Processar bio_links se existir
    let observacoesBioLinks = '';
    if (user.bio_links && Array.isArray(user.bio_links) && user.bio_links.length > 0) {
      const links = user.bio_links.map((link: any, index: number) => {
        if (typeof link === 'string') {
          return `${index + 1}. ${link}`;
        } else if (link && link.url) {
          const titulo = link.title || link.text || 'Link';
          return `${index + 1}. ${titulo}: ${link.url}`;
        }
        return `${index + 1}. ${JSON.stringify(link)}`;
      });
      observacoesBioLinks = `Bio Links do Instagram:\n${links.join('\n')}`;
    }

    // Criar Lead parcialmente preenchido
    const lead = new Lead();
    lead.configurarDadosBasicos(
      crmEmpresaIdNumeric,
      user.full_name || user.username,
      user.full_name || user.username,
      user.business_phone_number || '',
      user.username,
      user.biography || undefined,
      OrigemLead.Instagram
    );
    lead.instagramData = instagramDataFormatted;
    lead.website = user.external_url || undefined; // Website da bio
    lead.linkInsta = user.external_url || undefined; // Link da bio (compatibilidade)
    lead.notas = observacoesBioLinks || undefined; // Adicionar bio_links nas observações
    if (empresaObj) lead.crmEmpresa = empresaObj;

    res.json(Resposta.sucesso(lead));
  } catch (err) {
    console.error('Erro ao processar dados do Instagram:', err);
    res.json(Resposta.erro('Erro ao processar dados do Instagram: ' + err.message));
  }
});

// Rota dadosig2 que recebe texto bruto e extrai dados usando IA
router.post('/dadosig2', async (req: any, res) => {
  try {
    const { texto, crmEmpresaId } = req.body;

    if (!texto || typeof texto !== 'string') {
      return res.json(Resposta.erro('Texto é obrigatório'));
    }

    console.log('Processando texto do Instagram com IA...');

    // Prompt para extrair dados do Instagram e criar objeto Lead estruturado
    const prompt = `
Você é um especialista em análise de perfis do Instagram para geração de leads.
Analise o texto fornecido e extraia as informações para criar um objeto Lead completo.

⚠️ *** REGRAS CRÍTICAS: NUNCA INVENTE DADOS *** ⚠️
🚫 PROIBIDO ABSOLUTO: Inventar, deduzir ou gerar qualquer informação
✅ REGRA DE OURO: Apenas extraia dados que estejam LITERALMENTE presentes no texto
✅ SE NÃO TIVER CERTEZA → USE NULL
✅ É MELHOR NÃO TER DADOS do que ter DADOS INCORRETOS

*** REGRAS ESPECÍFICAS PARA TELEFONES ***
🔍 PROCURE apenas por telefones com indicadores INEQUÍVOCOS:
✅ Emojis de telefone: ☎️ 📞 📱 📲
✅ Palavras-chave: "Tel:", "Telefone:", "WhatsApp:", "Contato:", "Fone:"
✅ Formato telefônico claro: (XX) XXXX-XXXX ou (XX) 9XXXX-XXXX

🚫 NUNCA considere como telefone:
❌ Horários: "11:30", "18h", "das 9h às 22h"
❌ Números de endereço: "Rua 123", "número 300"
❌ Anos: "2023", "desde 2020"
❌ Quantidades: "há 5 anos", "mais de 100"
❌ Códigos: "CEP", códigos de produto
❌ Números isolados sem contexto de contato

*** IMPORTANTE: Se não encontrar telefones com indicadores claros, deixe VAZIO ***

🔍 EXEMPLOS DE O QUE NÃO É TELEFONE:
❌ "Aberto das 11:30 às 22:00" → São horários, NÃO telefones
❌ "Rua das Flores, 123" → É número de endereço, NÃO telefone
❌ "Desde 2020" → É ano, NÃO telefone
❌ "Há mais de 5 anos" → É quantidade de tempo, NÃO telefone
❌ "Delivery até as 23h" → É horário, NÃO telefone
❌ "123 opções de sabor" → É quantidade, NÃO telefone
❌ "CEP: 12345-678" → É código postal, NÃO telefone

✅ EXEMPLOS DE TELEFONES VÁLIDOS:
✅ "☎️ (62) 3333-4444" → TEM emoji de telefone
✅ "📱 WhatsApp: 99999-8888" → TEM emoji e palavra-chave
✅ "Tel: (11) 2222-3333" → TEM palavra-chave "Tel:"
✅ "Contato: (85) 91234-5678" → TEM palavra-chave "Contato:"

Retorne um JSON com a seguinte estrutura EXATA:

{
  "crmEmpresa": {
    "nome": "nome_da_empresa_extraido",
    "cnpj": null,
    "telefone": "telefone_apenas_numeros_ou_null",
    "email": null,
    "endereco": "endereco_completo_ou_null",
    "ativa": true
  },
  "lead": {
    "nomeResponsavel": "nome_do_responsavel_ou_nome_empresa",
    "empresa": "nome_da_empresa",
    "cidade": "cidade_descoberta_ou_null",
    "endereco": "endereco_completo_ou_null",
    "telefone": "telefone_principal_apenas_numeros_ou_vazio",
    "telefones": [
      {
        "tipo": "WhatsApp|Telefone Fixo|Celular|Comercial|Emergência",
        "numero": "numero_apenas_digitos_sem_formatacao",
        "descricao": "descricao_opcional_do_contexto"
      }
    ],
    "instagramHandle": "username_sem_@",
    "bioInsta": "biografia_ou_descricao_do_perfil",
    "origem": "Instagram",
    "etapa": "Prospecção",
    "score": 0,
    "instagramData": {
      "bio": "biografia_ou_descricao_do_perfil",
      "followers": numero_de_seguidores_ou_null,
      "following": numero_de_seguindo_ou_null,
      "accountType": "Business",
      "businessCategory": "categoria_do_negocio_ou_null",
      "location": "endereco_completo_ou_null",
      "website": "website_da_bio_se_houver_ou_null"
    },
    "website": "website_da_bio_se_houver_ou_null",
    "linkInsta": "link_da_bio_se_diferente_do_website_ou_null",
    "links": [
      {
        "tipo": "Site|Ifood|WhatsApp|Instagram|Localização|SiteCardapio|Concorrente|Reservas",
        "url": "url_completa_válida",
        "descricao": "descrição_clara_do_link",
        "ordem": numero_de_1_a_10
      }
    ],
    "notas": "TEXTO_COMPLETO_DO_PERFIL_MAIS_informacoes_extras"
  }
}

═══════════════════════════════════════════════════════════════════════════════
*** SEÇÃO CRÍTICA: EXTRAÇÃO DE LINKS MÚLTIPLOS ***
═══════════════════════════════════════════════════════════════════════════════

🔍 PROCURE e EXTRAIA TODOS os links encontrados no texto do perfil:
1. Links na bio/descrição
2. Links em stories highlights mencionados
3. Links mencionados em postagens
4. URLs completas ou parciais (como "meusite.com.br")
5. Menções a plataformas (iFood, WhatsApp, etc.)
6. Referências a redes sociais
7. Links de localização/mapas

🏷️ CATEGORIZE cada link conforme os tipos disponíveis:
- **Site**: Sites oficiais da empresa, blogs corporativos
- **Ifood**: Links do iFood, menções ao iFood
- **WhatsApp**: Links wa.me, números com contexto WhatsApp
- **Instagram**: Links de outros perfis Instagram relacionados
- **Localização**: Google Maps, endereços clicáveis, coordenadas
- **SiteCardapio**: Cardápios online próprios ou neutros
- **Concorrente**: Sistemas de cardápio de terceiros (Goomer, etc.)
- **Reservas**: Sistemas de reserva de mesa (OpenTable, etc.)

📋 REGRAS PARA EXTRAÇÃO DE LINKS:
- Extraia URLs completas quando disponíveis
- Para WhatsApp sem URL completa, monte: "https://wa.me/55DDNUMERO"
- Para Instagram sem URL, monte: "https://instagram.com/username"
- Para sites incompletos, adicione "https://" se necessário
- Ordene por relevância (1=mais importante, 10=menos importante)
- Use descrições claras e úteis para o usuário final
- Se não encontrar links, retorne array vazio []

💡 EXEMPLOS DE EXTRAÇÃO MÚLTIPLA:

EXEMPLO 1: Texto com WhatsApp, iFood e Site
Input: "🍕 Pizza artesanal | 📱 WhatsApp (11) 99999-9999 | 🛍️ iFood: minha-pizzaria | Site: www.minhapizzaria.com"
Output: Gerar 3 links - WhatsApp (wa.me), iFood e Site oficial

EXEMPLO 2: Texto com cardápio e localização
Input: "📍 Rua das Flores, 123 | Delivery via app próprio: cardapio.minhaempresa.com.br"
Output: Gerar 2 links - SiteCardapio e Localização (Google Maps)

⚠️ IMPORTANTE PARA LINKS:
- Se não encontrar links válidos, use array vazio: "links": []
- Mantenha os campos "website" e "linkInsta" para compatibilidade
- "website" = link principal da bio
- "linkInsta" = link secundário se diferente do website

═══════════════════════════════════════════════════════════════════════════════

REGRAS IMPORTANTES:
- Se não encontrar uma informação, use null (não string "null")
- Para telefone, extraia apenas números (ex: "***********")
- Para seguidores/seguindo/publicações, use números inteiros ou null
- Para username, remova @ se houver
- Para website e instagramData.website, use o link da bio se houver (ex: "https://exemplo.com.br")
- Para linkInsta, use apenas se for um link da bio DIFERENTE do website (caso raro, geralmente null)
- Para notas, SEMPRE inclua o TEXTO COMPLETO DO PERFIL no início, seguido de informações extras organizadas
- Mantenha a estrutura JSON exata conforme especificado
- Use "Business" para accountType sempre que for um perfil comercial
- IMPORTANTE: O link do perfil Instagram (https://instagram.com/username) será gerado automaticamente pelo sistema

REGRAS PARA NOME DA EMPRESA:
- REMOVA SEMPRE nomes de cidades do nome da empresa
- Exemplos: "Torii Sushi Goiânia" → "Torii Sushi", "Pizzaria Roma São Paulo" → "Pizzaria Roma"
- REMOVA TAMBÉM estados: "SP", "RJ", "GO", "MG", etc.
- REMOVA palavras de localização: "centro", "zona sul", "shopping", etc.
- MANTENHA apenas o nome comercial essencial da empresa
- Se houver dúvida, prefira o nome mais curto e comercial

REGRAS PARA DESCOBRIR CIDADE:
1. PRIORIDADE ALTA - Procure menções explícitas de cidade no texto:
   - "📍 São Paulo", "Localizado em Fortaleza", "Delivery em Goiânia"
   - Palavras como: "em", "localizado", "delivery", "atendemos", seguidas de nome de cidade

2. PRIORIDADE MÉDIA - Use DDD do telefone para inferir cidade principal:
   - DDD 11: "São Paulo"
   - DDD 21: "Rio de Janeiro"
   - DDD 31: "Belo Horizonte"
   - DDD 41: "Curitiba"
   - DDD 51: "Porto Alegre"
   - DDD 61: "Brasília"
   - DDD 62: "Goiânia"
   - DDD 71: "Salvador"
   - DDD 81: "Recife"
   - DDD 85: "Fortaleza"
   - DDD 47: "Joinville"
   - DDD 48: "Florianópolis"

3. PRIORIDADE BAIXA - Analise endereço na location ou bio
4. Se não conseguir determinar, use null

EXEMPLOS DE DESCOBERTA DE CIDADE:
- "📍 Goiânia/GO" → cidade: "Goiânia"
- Telefone: "(62) 99999-9999" → cidade: "Goiânia"
- "Delivery em Fortaleza" → cidade: "Fortaleza"
- "Atendemos São Paulo e região" → cidade: "São Paulo"
- "Localizado no centro de Curitiba" → cidade: "Curitiba"

═══════════════════════════════════════════════════════════════════════════════
*** SEÇÃO CRÍTICA: EXTRAÇÃO DE ENDEREÇO ***
═══════════════════════════════════════════════════════════════════════════════

REGRAS PARA EXTRAÇÃO DE ENDEREÇO:
1. PROCURE por indicadores: 📍, "Endereço:", "Localizado em:", "Rua", "Av.", "Avenida"
2. IDENTIFIQUE padrões de endereço brasileiro:
   - "Rua X, nº Y", "Av. Nome, 123", "📍 Rua/Av + número"
   - "JD Nome - Rua X", "Bairro - Av. Y"
3. EXTRAIA endereço completo quando disponível
4. COMBINE múltiplos endereços se houver (separados por ";")
5. IGNORE coordenadas GPS, foque no endereço legível
6. INCLUA bairro/referência quando mencionado

EXEMPLOS DE EXTRAÇÃO DE ENDEREÇO:
- "📍JD Goiás - Rua 54 esq. 56" → "Rua 54 esq. 56, JD Goiás"
- "📍JD Planalto Av. Marconi n.300" → "Av. Marconi, 300, JD Planalto"
- "Localizado na Rua das Flores, 123" → "Rua das Flores, 123"
- "📍Centro - Av. Brasil, 456 📍Setor Sul - Rua X, 789" → "Av. Brasil, 456, Centro; Rua X, 789, Setor Sul"
- "Endereço: Rua Exemplo, 100, Bairro ABC" → "Rua Exemplo, 100, Bairro ABC"

ATENÇÃO ESPECIAL:
- Múltiplos endereços devem ser separados por ";"
- Sempre inclua bairro/setor quando presente
- Formate de forma legível e padronizada
- Se houver apenas referência de bairro sem endereço, use null

═══════════════════════════════════════════════════════════════════════════════

🔍 REGRAS ULTRA-CONSERVADORAS PARA TELEFONES:
1. 🚫 SE NÃO TIVER INDICADOR CLARO → NÃO É TELEFONE
2. ✅ PROCURE APENAS por indicadores INEQUÍVOCOS: ☎️, 📞, 📱, 📲, Tel:, ZAP:, WhatsApp:, Contato:
3. ❌ IGNORE completamente números isolados sem contexto
4. ⚠️ NA DÚVIDA → NÃO INCLUA (é melhor não ter dados que ter dados errados)
5. 🎯 CLASSIFIQUE cada telefone por tipo APENAS com contexto claro:
   - "☎️", "📞", "Tel:", "Telefone:", números 8 dígitos (XXXX-XXXX) → "Telefone Fixo"
   - "📱", "📲", "ZAP:", "WhatsApp:", "WA:", números 9 dígitos (9XXXX-XXXX) → "WhatsApp"
   - "Cel:", "Celular:", números com 9º dígito → "Celular"
   - "Comercial:", "Escritório:" → "Comercial"
   - "Emergência:", "Urgência:" → "Emergência"
4. ANALISE formato do número:
   - 8 dígitos após DDD (3XXX-XXXX) = Telefone Fixo
   - 9 dígitos após DDD (9XXXX-XXXX) = Celular/WhatsApp
5. EXTRAIA apenas números (ex: "62999887766")
6. USE descricao para contexto adicional encontrado

EXEMPLOS DE EXTRAÇÃO DE TELEFONES:
- "☎️3252-2000 📲99100-5005" →
  [{"tipo": "Telefone Fixo", "numero": "6232522000", "descricao": "Telefone comercial"},
   {"tipo": "WhatsApp", "numero": "62991005005", "descricao": "WhatsApp"}]
- "📞 Tel.: 62 3591-1007 ou 62 3591-1009" →
  [{"tipo": "Telefone Fixo", "numero": "6235911007", "descricao": "Telefone principal"},
   {"tipo": "Telefone Fixo", "numero": "6235911009", "descricao": "Telefone alternativo"}]
- "📱ZAP: 62 99699-3881" →
  [{"tipo": "WhatsApp", "numero": "62996993881", "descricao": "WhatsApp"}]
- "Tel: (11) 3333-4444 | WhatsApp: (11) 99888-7777" →
  [{"tipo": "Telefone Fixo", "numero": "1133334444", "descricao": "Telefone comercial"},
   {"tipo": "WhatsApp", "numero": "11998887777", "descricao": "WhatsApp"}]
- "Contato: 85 98765-4321 (WhatsApp)" →
  [{"tipo": "WhatsApp", "numero": "85987654321", "descricao": "Contato principal"}]
- "☎️3252-2000 📲99100-5005" (texto de Goiânia) →
  [{"tipo": "Telefone Fixo", "numero": "6232522000", "descricao": "Telefone comercial"},
   {"tipo": "WhatsApp", "numero": "62991005005", "descricao": "WhatsApp"}]

VALIDAÇÃO DE TELEFONES:
- Números com 8 dígitos (XXXX-XXXX) são "Telefone Fixo"
- Números com 9 dígitos (9XXXX-XXXX) são "WhatsApp" ou "Celular"
- Emojis 📞☎️ indicam telefone fixo, emojis 📱📲 indicam WhatsApp
- APENAS adicione DDD se o número já tiver formato de telefone válido
- NÃO adicione DDD automaticamente a números ambíguos
- Se não puder confirmar que é um telefone, NÃO inclua

🎯 REGRAS FINAIS DE PREENCHIMENTO:
- telefone (campo único): use apenas se houver UM telefone claramente identificado
- telefones (array): inclua apenas telefones com indicadores claros
- ✅ É PERFEITAMENTE ACEITÁVEL deixar campos de telefone vazios/null se não houver telefones claros
- ⭐ QUALIDADE é mais importante que QUANTIDADE - prefira não extrair a extrair incorretamente
- 🚫 NUNCA transforme outros números em telefones "por tentativa"

⚠️ LEMBRETE CRÍTICO FINAL:
"Se você não conseguir apontar EXATAMENTE onde um telefone aparece no texto com indicadores claros, então NÃO é um telefone válido. Deixe vazio."

✅ EXEMPLOS DE QUANDO DEIXAR VAZIO:
- Texto só tem horários: "Aberto das 11h às 22h" → telefones: []
- Texto só tem endereço: "Rua 15, nº 300" → telefones: []
- Texto só tem anos: "Desde 2020" → telefones: []
- Números sem contexto: "123 sabores disponíveis" → telefones: []

FORMATO DAS NOTAS:
"TEXTO ORIGINAL DO PERFIL:
[texto_completo_aqui]

INFORMAÇÕES EXTRAÍDAS:
- Cidade: [cidade_descoberta_e_como]
- Horário: [horario_se_houver]
- Serviços: [servicos_se_houver]
- WhatsApp: [whatsapp_se_houver]
- Publicações: [numero_publicacoes_se_houver]"

🔍 CHECKLIST FINAL OBRIGATÓRIO - VALIDAÇÃO DE QUALIDADE:
Antes de gerar o JSON, responda mentalmente SIM/NÃO para cada pergunta:

📞 VALIDAÇÃO DE TELEFONES:
1. "Todos os telefones que inclui têm indicadores CLAROS (☎️📞📱📲 ou palavras-chave)?"
2. "Posso apontar EXATAMENTE onde cada telefone aparece no texto original?"
3. "Confirmei que NÃO são horários (11:30), endereços (123) ou anos (2023)?"
4. "Evitei inventar/deduzir telefones que não estão explícitos?"
5. "Se tive dúvida sobre algum número, deixei de fora?"

🔗 VALIDAÇÃO DE LINKS:
6. "Extraí TODOS os links mencionados no texto?"
7. "Categorizei cada link com o tipo correto?"
8. "Montei URLs completas quando necessário?"
9. "Ordenei links por relevância?"
10. "Se não encontrei links, deixei array vazio?"

🏢 VALIDAÇÃO GERAL:
11. "Todos os dados que incluo estão LITERALMENTE presentes no texto?"
12. "Preferi usar null ao invés de adivinhar informações?"
13. "Removi nomes de cidades do nome da empresa?"
14. "Extraí apenas informações que tenho CERTEZA ABSOLUTA?"

⚠️ SE ALGUMA RESPOSTA FOR "NÃO", REVISE O JSON ANTES DE ENVIAR!

Texto para análise:
${texto}
`;

    // Chamar ChatGPT para extrair dados
    const chatGPTService = new ChatGPTService();
    const resultado: any = await chatGPTService.chameOpenAIChat(
      'sistema',
      'extrair_dados_instagram',
      prompt,
      texto,
      [],
      0.3,
      '[dadosig2]',
      { type: "json_object" }
    );

    if (!resultado || !resultado.text) {
      console.error('Erro na resposta da IA:', resultado);
      return res.json(Resposta.erro('Erro ao processar dados com IA'));
    }

    // Parse da resposta JSON
    let dadosEstruturados;
    try {
      console.log('Resposta da IA:', resultado.text);

      dadosEstruturados = JSON.parse(resultado.text);
    } catch (parseError) {
      console.error('Erro ao fazer parse da resposta da IA:', parseError);
      return res.json(Resposta.erro('Erro ao interpretar resposta da IA'));
    }

    console.log('Dados estruturados pela IA:', dadosEstruturados);

    // Função para validar telefones brasileiros
    const validarTelefoneBrasileiro = (numero: string): boolean => {
      if (!numero || typeof numero !== 'string') return false;

      // Remove qualquer formatação
      const numeroLimpo = numero.replace(/\D/g, '');

      // Telefone deve ter 10 ou 11 dígitos (DDD + 8 ou 9 dígitos)
      if (numeroLimpo.length !== 10 && numeroLimpo.length !== 11) return false;

      // Verifica se DDD é válido (11-99)
      const ddd = parseInt(numeroLimpo.substring(0, 2));
      if (ddd < 11 || ddd > 99) return false;

      // Verifica se não é uma sequência óbvia de números inválidos
      const sequenciasInvalidas = ['0000000000', '1111111111', '1234567890', '0123456789'];
      if (sequenciasInvalidas.includes(numeroLimpo)) return false;

      return true;
    };

    // Validar e limpar telefones na resposta da IA
    if (dadosEstruturados.crmEmpresa?.telefone) {
      if (!validarTelefoneBrasileiro(dadosEstruturados.crmEmpresa.telefone)) {
        console.warn('Telefone da empresa inválido removido:', dadosEstruturados.crmEmpresa.telefone);
        dadosEstruturados.crmEmpresa.telefone = null;
      }
    }

    if (dadosEstruturados.lead?.telefone) {
      if (!validarTelefoneBrasileiro(dadosEstruturados.lead.telefone)) {
        console.warn('Telefone principal do lead inválido removido:', dadosEstruturados.lead.telefone);
        dadosEstruturados.lead.telefone = '';
      }
    }

    if (dadosEstruturados.lead?.telefones && Array.isArray(dadosEstruturados.lead.telefones)) {
      const telefonesValidos = dadosEstruturados.lead.telefones.filter((tel: any) => {
        const isValid = validarTelefoneBrasileiro(tel.numero);
        if (!isValid) {
          console.warn('Telefone inválido removido da lista:', tel);
        }
        return isValid;
      });
      dadosEstruturados.lead.telefones = telefonesValidos;
      console.log(`Validação de telefones: ${dadosEstruturados.lead.telefones.length} de ${dadosEstruturados.lead?.telefones?.length || 0} telefones mantidos`);
    }

    // Validar estrutura da resposta
    if (!dadosEstruturados.crmEmpresa || !dadosEstruturados.lead) {
      console.error('Estrutura inválida na resposta da IA:', dadosEstruturados);
      return res.json(Resposta.erro('Estrutura de dados inválida retornada pela IA'));
    }

    // Obter ou criar empresa
    let crmEmpresaIdNumeric: number;
    let empresaObj: CrmEmpresa;

    if (crmEmpresaId) {
      crmEmpresaIdNumeric = parseInt(crmEmpresaId as string, 10);
      const mapeadorCrmEmpresa = new MapeadorDeCrmEmpresa();
      empresaObj = await mapeadorCrmEmpresa.selecioneSync({ id: crmEmpresaIdNumeric });
    } else {
      const dadosEmpresa = dadosEstruturados.crmEmpresa;
      const mapeadorCrmEmpresa = new MapeadorDeCrmEmpresa();

      // Tenta encontrar empresa existente pelo nome
      const existente = await mapeadorCrmEmpresa.selecioneSync({ nome: dadosEmpresa.nome });

      if (existente && existente.id) {
        crmEmpresaIdNumeric = existente.id;
        empresaObj = existente;
      } else {
        // Criar nova empresa com dados extraídos pela IA
        const novaEmpresa = new CrmEmpresa(dadosEmpresa.nome);
        novaEmpresa.telefone = dadosEmpresa.telefone;
        novaEmpresa.endereco = dadosEmpresa.endereco;
        novaEmpresa.email = dadosEmpresa.email;
        novaEmpresa.cnpj = dadosEmpresa.cnpj;
        novaEmpresa.ativa = dadosEmpresa.ativa;

        const criada = await mapeadorCrmEmpresa.insiraSync(novaEmpresa);
        crmEmpresaIdNumeric = criada.id;
        empresaObj = novaEmpresa;
        empresaObj.id = criada.id;
      }
    }

    // Criar Lead com dados estruturados pela IA
    const dadosLead = dadosEstruturados.lead;

    const lead = new Lead();
    lead.configurarDadosBasicos(
      crmEmpresaIdNumeric,
      dadosLead.nomeResponsavel,
      dadosLead.empresa,
      dadosLead.telefone || '',
      dadosLead.instagramHandle,
      dadosLead.bioInsta,
      OrigemLead.Instagram
    );

    // Adicionar cidade se descoberta pela IA
    if (dadosLead.cidade) {
      lead.cidade = dadosLead.cidade;
    }

    // Adicionar endereço se extraído pela IA
    if (dadosLead.endereco) {
      lead.endereco = dadosLead.endereco;
      console.log('Endereço extraído pela IA:', dadosLead.endereco);
    }

    // Aplicar dados estruturados do Lead
    lead.etapa = dadosLead.etapa as any;
    lead.score = dadosLead.score;
    lead.instagramData = dadosLead.instagramData;
    lead.website = dadosLead.website; // Website da bio
    lead.linkInsta = dadosLead.linkInsta; // Link da bio se diferente do website
    lead.notas = dadosLead.notas;

    // Processar telefones múltiplos se fornecidos pela IA
    if (dadosLead.telefones && Array.isArray(dadosLead.telefones)) {
      console.log('Processando telefones múltiplos:', dadosLead.telefones);
      lead.adicionarTelefonesFromArray(dadosLead.telefones);

      // Sincronizar telefone principal para compatibilidade
      lead.sincronizarTelefonePrincipal();

      console.log('Telefones processados:', lead.telefones);
      console.log('Telefone principal sincronizado:', lead.telefone);
    }

    // Processar links múltiplos se fornecidos pela IA
    if (dadosLead.links && Array.isArray(dadosLead.links)) {
      console.log('Processando links múltiplos:', dadosLead.links);

      // Converter para instâncias LeadLink
      const linksFormatados = dadosLead.links.map((link: any, index: number) => {
        // Validar tipo do link
        const tiposValidos = Object.values(TipoLinkLead);
        let tipoLink = link.tipo;

        // Mapear tipos que podem vir da IA para tipos válidos
        if (tipoLink === 'SiteCardapio' || tipoLink === 'Site do Cardápio') {
          tipoLink = TipoLinkLead.SiteCardapio;
        } else if (tipoLink === 'Localização') {
          tipoLink = TipoLinkLead.Localizacao;
        } else if (tipoLink === 'WhatsApp') {
          tipoLink = TipoLinkLead.Whatsapp;
        }

        // Se o tipo não for válido, usar 'Site' como padrão
        if (!tiposValidos.includes(tipoLink)) {
          console.warn(`Tipo de link inválido '${link.tipo}', usando 'Site' como padrão`);
          tipoLink = TipoLinkLead.Site;
        }

        return new LeadLink(
          0, // crmLeadId será atualizado quando lead for salvo
          tipoLink,
          link.url,
          link.descricao,
          link.ordem || index + 1
        );
      });

      lead.links = linksFormatados;
      console.log('Links processados:', lead.links);

      // Atualizar campos de compatibilidade website/linkInsta baseado nos links
      if (linksFormatados.length > 0) {
        const linkSite = linksFormatados.find((l: LeadLink) => l.tipo === TipoLinkLead.Site);
        const linkCardapio = linksFormatados.find((l: LeadLink) => l.tipo === TipoLinkLead.SiteCardapio);

        // Se já não tem website definido, usar o primeiro link de site encontrado
        if (!lead.website && linkSite) {
          lead.website = linkSite.url;
          console.log('Website atualizado a partir dos links:', lead.website);
        }

        // Se tem link de cardápio e é diferente do website, usar como linkInsta
        if (linkCardapio && linkCardapio.url !== lead.website) {
          lead.linkInsta = linkCardapio.url;
          console.log('LinkInsta atualizado com cardápio:', lead.linkInsta);
        }
      }
    }

    // Vincular empresa
    lead.crmEmpresa = empresaObj;

    res.json(Resposta.sucesso(lead));

  } catch (err) {
    console.error('Erro ao processar dados do Instagram com IA:', err);
    res.json(Resposta.erro('Erro ao processar dados do Instagram com IA: ' + err.message));
  }
});

// Listagem com filtros e paginação
router.get('/', async (req: any, res) => {
  const mapeador = new MapeadorDeLead();
  try {
    const params = {
      inicio: req.query.inicio ? parseInt(req.query.inicio) : 0,
      total: req.query.total ? parseInt(req.query.total) : 20,
      texto: req.query.texto || null,
      etapa: req.query.etapa || null,
      crmEmpresaId: req.query.crmEmpresaId || null
    };

    const dados = await mapeador.listeAsync(params);
    const total = await mapeador.selecioneTotal(params);
    res.json(Resposta.sucesso({ data: dados, total }));
  } catch (err) {
    console.error('Erro ao listar leads', err);
    res.json(Resposta.erro('Erro ao listar leads'));
  }
});

// Buscar lead por username do Instagram
router.get('/porUsername/:username', async (req: any, res) => {
  const mapeador = new MapeadorDeLead();
  try {
    const username = req.params.username.replace('@', '');
    const lead = await mapeador.selecioneSync({ instagramHandle: username });
    if (!lead) return res.json(Resposta.erro('Lead não encontrado'));
    res.json(Resposta.sucesso(lead));
  } catch (err) {
    console.error('Erro ao buscar lead por username', err);
    res.json(Resposta.erro('Erro ao buscar lead por username'));
  }
});

// Buscar lead por telefone
router.get('/buscar-por-telefone', async (req: any, res) => {
  const { telefone } = req.query;

  if (!telefone) {
    return res.json(Resposta.erro('Telefone é obrigatório'));
  }

  const mapeador = new MapeadorDeLead();
  const mapeadorLink = new MapeadorDeLeadLink();

  try {
    console.log('LEADS: Buscando lead por telefone:', telefone);

    // Buscar lead pelo telefone
    const leads = await mapeador.listeAsync({
      crmEmpresaId: req.empresa.id,
      telefone: telefone
    });

    if (leads && leads.length > 0) {
      const lead = leads[0];

      // Carregar links do lead
      if (lead.id) {
        lead.links = await mapeadorLink.buscarPorLead(lead.id);
      }

      console.log('LEADS: Lead encontrado:', {
        id: lead.id,
        nome: lead.nomeResponsavel,
        empresa: lead.empresa,
        telefone: lead.telefone
      });

      return res.json(Resposta.sucesso(lead));
    } else {
      console.log('LEADS: Nenhum lead encontrado para o telefone:', telefone);
      return res.json(Resposta.sucesso(null));
    }
  } catch (erro) {
    console.error('LEADS: Erro ao buscar lead por telefone:', erro);
    return res.json(Resposta.erro('Erro ao buscar lead'));
  }
});

// Selecionar um lead
router.get('/:id', async (req: any, res) => {
  const mapeador = new MapeadorDeLead();
  const mapeadorLink = new MapeadorDeLeadLink();
  try {
    const lead = await mapeador.selecioneSync({ id: req.params.id });
    if (!lead) return res.json(Resposta.erro('Lead não encontrado'));

    // Carregar links do lead
    lead.links = await mapeadorLink.buscarPorLead(parseInt(req.params.id));

    res.json(Resposta.sucesso(lead));
  } catch (err) {
    console.error('Erro ao obter lead', err);
    res.json(Resposta.erro('Erro ao obter lead'));
  }
});

// Inserir lead
router.post('/', async (req: any, res) => {
  const mapeador = new MapeadorDeLead();
  try {
    const obj = req.body;
    console.log('Dados recebidos para inserir lead:', JSON.stringify(obj, null, 2));

    // Se não tiver crmEmpresaId, usar uma empresa padrão ou criar uma nova
    if (!obj.crmEmpresaId) {
      // Usar empresa padrão ou criar uma baseada no nome da empresa do lead
      const mapeadorCrmEmpresa = new MapeadorDeCrmEmpresa();

      if (obj.empresa) {
        // Tenta encontrar empresa existente pelo nome
        const existente = await mapeadorCrmEmpresa.selecioneSync({ nome: obj.empresa });

        if (existente && existente.id) {
          obj.crmEmpresaId = existente.id;
        } else {
          // Criar nova empresa
          const novaEmpresa = new CrmEmpresa(obj.empresa);
          if (obj.telefone) novaEmpresa.telefone = obj.telefone;

          const criada = await mapeadorCrmEmpresa.insiraSync(novaEmpresa);
          obj.crmEmpresaId = criada.id;
        }
      } else {
        // Criar empresa com nome genérico baseado no responsável
        const nomeEmpresa = `${obj.nomeResponsavel || 'Lead'} - Instagram`;
        const novaEmpresa = new CrmEmpresa(nomeEmpresa);
        if (obj.telefone) novaEmpresa.telefone = obj.telefone;

        const criada = await mapeadorCrmEmpresa.insiraSync(novaEmpresa);
        obj.crmEmpresaId = criada.id;
      }
    }

    console.log('LEADS: Inserindo lead no banco...');
    console.log('LEADS: Objeto a ser inserido:', obj);

    // Validar campos obrigatórios antes da inserção
    const camposObrigatorios = {
      crmEmpresaId: obj.crmEmpresaId,
      nomeResponsavel: obj.nomeResponsavel,
      telefone: obj.telefone,
      etapa: obj.etapa,
      origem: obj.origem
    };

    console.log('LEADS: Validando campos obrigatórios:', camposObrigatorios);

    for (const [campo, valor] of Object.entries(camposObrigatorios)) {
      if (!valor || valor === null || valor === undefined || valor === '') {
        throw new Error(`Campo obrigatório '${campo}' está vazio ou inválido: ${valor}`);
      }
    }

    try {
      await mapeador.insiraSync(obj);
      console.log('LEADS: Lead inserido com sucesso');
      console.log('LEADS: ID gerado:', obj.id);
      console.log('LEADS: Objeto após inserção:', obj);
    } catch (insertError) {
      console.error('LEADS: Erro específico na inserção do lead:', insertError);
      console.error('LEADS: SQL Error details:', insertError.sql);
      console.error('LEADS: Objeto que causou erro:', obj);
      throw new Error(`Erro na inserção do lead: ${insertError.message}`);
    }

    // O ID é adicionado ao objeto original pelo MyBatis
    const novo = obj;

    // Salvar links se fornecidos
    if (obj.links && Array.isArray(obj.links) && obj.links.length > 0) {
      console.log('LEADS: Salvando links para lead...');
      console.log('LEADS: Lead inserido:', novo);
      console.log('LEADS: ID do lead:', novo?.id);
      console.log('LEADS: Links a serem salvos:', obj.links);

      if (!novo || !novo.id) {
        throw new Error('Lead não foi criado corretamente - ID não encontrado');
      }

      const mapeadorLink = new MapeadorDeLeadLink();
      await mapeadorLink.salvarLinks(novo.id, obj.links);
      console.log('LEADS: Links salvos com sucesso para lead ID:', novo.id);
    }

    // Verificar se deve sincronizar com Bitrix automaticamente
    const sincronizarBitrix = req.body.sincronizarBitrix || req.query.sincronizarBitrix;
    let bitrixId = null;
    let mensagemBitrix = '';

    if (true || sincronizarBitrix === 'true' || sincronizarBitrix === true) {
      try {
        console.log('LEADS: Sincronizando lead com Bitrix...');
        const bitrixService = BitrixServiceFactory.criarInstancia();

        // Usar o lead salvo se tiver ID, senão usar o original
        let leadParaBitrix = novo?.id ? novo : obj;

        // DEBUG: Verificar tipo do objeto
        console.log('LEADS DEBUG: Tipo de leadParaBitrix:', typeof leadParaBitrix);
        console.log('LEADS DEBUG: É instância de Lead?', leadParaBitrix instanceof Lead);
        console.log('LEADS DEBUG: Tem crmEmpresa?', !!leadParaBitrix.crmEmpresa);
        console.log('LEADS DEBUG: Propriedades do objeto:', Object.keys(leadParaBitrix));
        console.log('LEADS DEBUG: Tem método hasSocios?', typeof leadParaBitrix.hasSocios === 'function');

        // Carregar links se o lead foi salvo e tem ID
        if (leadParaBitrix.id) {
          console.log('LEADS: Carregando links para lead ID:', leadParaBitrix.id);
          const mapeadorLink = new MapeadorDeLeadLink();
          leadParaBitrix.links = await mapeadorLink.buscarPorLead(leadParaBitrix.id);
          console.log('LEADS: Links carregados:', leadParaBitrix.links);
          console.log('LEADS DEBUG: Total de links carregados:', leadParaBitrix.links?.length || 0);
          console.log('LEADS DEBUG: Testando getAllLinksUrls():', leadParaBitrix.getAllLinksUrls?.());

          // Log detalhado dos links por tipo
          if (leadParaBitrix.links && leadParaBitrix.links.length > 0) {
            leadParaBitrix.links.forEach((link: any, index: number) => {
              console.log(`LEADS: Link ${index + 1}: ${link.tipo} = ${link.url} (crmLeadId: ${link.crmLeadId})`);
            });
          }

          // Converter para instância da classe Lead para ter acesso aos métodos
          console.log('LEADS DEBUG ANTES DE NEW: Tipo de Lead:', typeof Lead);
          console.log('LEADS DEBUG ANTES DE NEW: Lead:', Lead);
          console.log('LEADS DEBUG ANTES DE NEW: Lead.name:', Lead?.name);
          console.log('LEADS DEBUG ANTES DE NEW: Lead.prototype:', Lead?.prototype);

          let leadInstance;
          try {
            leadInstance = new Lead();
            leadInstance.configurarDadosBasicos(
              leadParaBitrix.crmEmpresaId,
              leadParaBitrix.nomeResponsavel,
              leadParaBitrix.empresa,
              leadParaBitrix.telefone,
              leadParaBitrix.instagramHandle,
              leadParaBitrix.bioInsta,
              leadParaBitrix.origem
            );
          } catch (erro) {
            console.error('LEADS DEBUG: ERRO ao criar instância de Lead:', erro);
            console.error('LEADS DEBUG: Stack trace:', erro.stack);
            console.log('LEADS DEBUG: Tentando importação alternativa...');

            // Tentar importação alternativa
            try {
              const LeadClass = require('../domain/crm/Lead').default;
              console.log('LEADS DEBUG: LeadClass via require:', typeof LeadClass);
              leadInstance = new LeadClass();
              leadInstance.configurarDadosBasicos(
                leadParaBitrix.crmEmpresaId,
                leadParaBitrix.nomeResponsavel,
                leadParaBitrix.empresa,
                leadParaBitrix.telefone,
                leadParaBitrix.instagramHandle,
                leadParaBitrix.bioInsta,
                leadParaBitrix.origem
              );
              console.log('LEADS DEBUG: Instância criada com require!');
            } catch (erro2) {
              console.error('LEADS DEBUG: ERRO com require também:', erro2);
              throw erro; // Re-throw original error
            }
          }

          // Copiar todas as propriedades
          Object.assign(leadInstance, leadParaBitrix);

          // Usar a instância da classe
          leadParaBitrix = leadInstance;
          console.log('LEADS: Lead convertido para instância da classe');

          // DEBUG: Verificar após conversão
          console.log('LEADS DEBUG APÓS CONVERSÃO: É instância de Lead?', leadParaBitrix instanceof Lead);
          console.log('LEADS DEBUG APÓS CONVERSÃO: Tem método hasSocios?', typeof leadParaBitrix.hasSocios === 'function');
        } else {
          console.log('LEADS: Lead ainda não tem ID, links não podem ser carregados');

          // DEBUG: Quando não tem ID, precisa converter também!
          console.log('LEADS DEBUG: Lead sem ID - criando instância da classe');
          console.log('LEADS DEBUG SEM ID: Tipo de Lead:', typeof Lead);
          console.log('LEADS DEBUG SEM ID: Lead:', Lead);

          let leadInstance;
          try {
            leadInstance = new Lead();
            leadInstance.configurarDadosBasicos(
              obj.crmEmpresaId,
              obj.nomeResponsavel,
              obj.empresa,
              obj.telefone,
              obj.instagramHandle,
              obj.bioInsta,
              obj.origem
            );
          } catch (erro) {
            console.error('LEADS DEBUG SEM ID: ERRO ao criar instância:', erro);
            console.error('LEADS DEBUG SEM ID: Stack trace:', erro.stack);

            // Tentar importação alternativa
            try {
              const LeadClass = require('../domain/crm/Lead').default;
              console.log('LEADS DEBUG SEM ID: LeadClass via require:', typeof LeadClass);
              leadInstance = new LeadClass();
              leadInstance.configurarDadosBasicos(
                obj.crmEmpresaId,
                obj.nomeResponsavel,
                obj.empresa,
                obj.telefone,
                obj.instagramHandle,
                obj.bioInsta,
                obj.origem
              );
              console.log('LEADS DEBUG SEM ID: Instância criada com require!');
            } catch (erro2) {
              console.error('LEADS DEBUG SEM ID: ERRO com require também:', erro2);
              throw erro;
            }
          }

          // Copiar todas as propriedades
          Object.assign(leadInstance, obj);

          leadParaBitrix = leadInstance;
          console.log('LEADS DEBUG: Lead sem ID convertido para instância');
          console.log('LEADS DEBUG: Tem método hasSocios agora?', typeof leadParaBitrix.hasSocios === 'function');
        }

        console.log('LEADS: Enviando lead para Bitrix:', {
          id: leadParaBitrix.id,
          nome: leadParaBitrix.nomeResponsavel,
          empresa: leadParaBitrix.empresa,
          telefone: leadParaBitrix.telefone,
          totalLinks: leadParaBitrix.links?.length || 0
        });


        const resultadoBitrix = await bitrixService.criarLead(leadParaBitrix);

        if (resultadoBitrix.sucesso) {
          bitrixId = resultadoBitrix.data;
          mensagemBitrix = `Lead sincronizado com Bitrix24. ID: ${bitrixId}`;
          console.log('LEADS: Lead sincronizado com Bitrix. ID:', bitrixId);

          // TODO: Opcional - salvar bitrixId no banco de dados local
          // await mapeador.atualizeSync({ id: novo.id, bitrixId: bitrixId });
        } else {
          mensagemBitrix = `Erro na sincronização Bitrix: ${resultadoBitrix.erro}`;
          console.error('LEADS: Erro ao sincronizar com Bitrix:', resultadoBitrix.erro);
        }
      } catch (bitrixError) {
        mensagemBitrix = `Erro na conexão com Bitrix: ${bitrixError.message}`;
        console.error('LEADS: Erro na sincronização com Bitrix:', bitrixError);
        // Não falhamos a criação do lead por erro no Bitrix
      }
    }

    // Montar resposta incluindo informações do Bitrix se houve sincronização
    const resposta = {
      ...novo,
      bitrixInfo: sincronizarBitrix ? {
        sincronizado: bitrixId !== null,
        bitrixId: bitrixId,
        mensagem: mensagemBitrix
      } : null
    };

    res.json(Resposta.sucesso(resposta));
  } catch (err) {
    console.error('Erro ao inserir lead', err);
    res.json(Resposta.erro('Erro ao inserir lead: ' + err.message));
  }
});

// Atualizar lead
router.put('/:id', async (req: any, res) => {
  const mapeador = new MapeadorDeLead();
  try {
    const obj = req.body;
    obj.id = req.params.id;
    const atualizado = await mapeador.atualizeSync(obj);

    // Atualizar links se fornecidos
    if (obj.links && Array.isArray(obj.links)) {
      const mapeadorLink = new MapeadorDeLeadLink();
      await mapeadorLink.salvarLinks(parseInt(req.params.id), obj.links);
    }

    res.json(Resposta.sucesso(atualizado));
  } catch (err) {
    console.error('Erro ao atualizar lead', err);
    res.json(Resposta.erro('Erro ao atualizar lead'));
  }
});

// Remover lead
router.delete('/:id', async (req: any, res) => {
  const mapeador = new MapeadorDeLead();
  try {
    await mapeador.removaAsync({ id: req.params.id });
    res.json(Resposta.sucesso({}));
  } catch (err) {
    console.error('Erro ao remover lead', err);
    res.json(Resposta.erro('Erro ao remover lead'));
  }
});

// Sincronizar lead específico com Bitrix
router.post('/:id/sincronizar-bitrix', async (req: any, res) => {
  const mapeador = new MapeadorDeLead();
  const mapeadorLink = new MapeadorDeLeadLink();
  try {
    const lead = await mapeador.selecioneSync({ id: req.params.id });

    if (!lead) {
      return res.json(Resposta.erro('Lead não encontrado'));
    }

    // Carregar links do lead
    lead.links = await mapeadorLink.buscarPorLead(parseInt(req.params.id));
    console.log('LEADS DEBUG: Links carregados do banco para lead ID', req.params.id, ':', lead.links);
    console.log('LEADS DEBUG: Total de links carregados:', lead.links?.length || 0);

    // Converter para instância da classe Lead para ter acesso aos métodos
    const leadInstance = new Lead();
    leadInstance.configurarDadosBasicos(
      lead.crmEmpresaId,
      lead.nomeResponsavel,
      lead.empresa,
      lead.telefone,
      lead.instagramHandle,
      lead.bioInsta,
      lead.origem
    );

    // Copiar todas as propriedades
    Object.assign(leadInstance, lead);

    console.log('LEADS DEBUG: Links após Object.assign:', leadInstance.links);
    console.log('LEADS DEBUG: Testando getAllLinksUrls():', leadInstance.getAllLinksUrls());
    console.log('LEADS DEBUG: Tipo de leadInstance.links:', typeof leadInstance.links);
    console.log('LEADS DEBUG: É array?', Array.isArray(leadInstance.links));

    console.log('LEADS: Sincronizando lead ID', req.params.id, 'com Bitrix...');
    console.log('LEADS: Dados do lead:', {
      id: leadInstance.id,
      nome: leadInstance.nomeResponsavel,
      empresa: leadInstance.empresa,
      telefone: leadInstance.telefone,
      instagram: leadInstance.instagramHandle,
      totalLinks: leadInstance.links?.length || 0
    });

    const bitrixService = BitrixServiceFactory.criarInstancia();
    const resultado = await bitrixService.criarLead(leadInstance);

    if (resultado.sucesso) {
      console.log('LEADS: Lead sincronizado com sucesso. Bitrix ID:', resultado.data);

      res.json(Resposta.sucesso({
        leadId: req.params.id,
        bitrixId: resultado.data,
        mensagem: 'Lead sincronizado com Bitrix24 com sucesso',
        lead: {
          id: lead.id,
          nome: lead.nomeResponsavel,
          empresa: lead.empresa,
          telefone: lead.telefone,
          instagram: lead.instagramHandle
        },
        sincronizadoEm: new Date().toISOString()
      }));
    } else {
      console.error('LEADS: Erro ao sincronizar lead:', resultado.erro);
      res.json(Resposta.erro(`Erro ao sincronizar com Bitrix24: ${resultado.erro}`));
    }
  } catch (err) {
    console.error('LEADS: Erro ao sincronizar lead com Bitrix:', err);
    res.json(Resposta.erro('Erro ao sincronizar lead com Bitrix24: ' + err.message));
  }
});

// Testar lead com links categorizados
router.get('/teste-links-bitrix', async (req: any, res) => {
  try {
    console.log('Testando lead com links categorizados...');

    // Criar um lead de teste
    const leadTeste = new Lead();
    leadTeste.configurarDadosBasicos(
      1, // crmEmpresaId
      'Teste Links Bitrix',
      'Restaurante Teste Ltda',
      '***********',
      'teste_links',
      'Bio de teste para links categorizados',
      OrigemLead.Instagram
    );

    leadTeste.score = 90;
    leadTeste.segmento = 'Alimentação' as any;
    leadTeste.observacoes = 'Lead de teste com links categorizados';
    leadTeste.instagramData = {
      followers: 2000,
      following: 400,
      accountType: 'Business',
      businessCategory: 'Restaurante',
      bio: 'Restaurante especializado em comida japonesa'
    };

    // Adicionar links de teste usando instâncias da classe LeadLink
    // Nota: O crmLeadId será definido como 0 temporariamente e será atualizado quando o lead for salvo
    leadTeste.links = [
      new LeadLink(
        0, // crmLeadId será atualizado quando o lead for salvo
        TipoLinkLead.Instagram,
        'https://instagram.com/teste_links',
        'Perfil Instagram',
        1
      ),
      new LeadLink(
        0, // crmLeadId será atualizado quando o lead for salvo
        TipoLinkLead.Ifood,
        'https://ifood.com.br/delivery/goiania-go/restaurante-teste',
        'Cardápio iFood',
        2
      ),
      new LeadLink(
        0, // crmLeadId será atualizado quando o lead for salvo
        TipoLinkLead.Concorrente,
        'https://goomer.app/restaurante-teste',
        'Sistema Goomer',
        3
      ),
      new LeadLink(
        0, // crmLeadId será atualizado quando o lead for salvo
        TipoLinkLead.Site,
        'https://restauranteteste.com.br',
        'Site oficial',
        4
      )
    ];

    console.log('TESTE: Lead criado com', leadTeste.links.length, 'links');
    console.log('TESTE: Testando métodos do Lead:');
    console.log('TESTE: getLinkInstagram():', leadTeste.getLinkInstagram());
    console.log('TESTE: getLinkIfood():', leadTeste.getLinkIfood());
    console.log('TESTE: getLinkConcorrente():', leadTeste.getLinkConcorrente());
    console.log('TESTE: getLinkSite():', leadTeste.getLinkSite());

    const bitrixService = BitrixServiceFactory.criarInstancia();
    const resultado = await bitrixService.criarLead(leadTeste);

    if (resultado.sucesso) {
      res.json(Resposta.sucesso({
        bitrixId: resultado.data,
        mensagem: 'Teste de links categorizados realizado com sucesso',
        leadTeste: {
          nome: leadTeste.nomeResponsavel,
          empresa: leadTeste.empresa,
          telefone: leadTeste.telefone,
          totalLinks: leadTeste.links.length,
          linksTestados: {
            instagram: leadTeste.getLinkInstagram(),
            ifood: leadTeste.getLinkIfood(),
            concorrente: leadTeste.getLinkConcorrente(),
            site: leadTeste.getLinkSite()
          }
        }
      }));
    } else {
      res.json(Resposta.erro(`Falha no teste: ${resultado.erro}`));
    }
  } catch (err) {
    console.error('Erro no teste de links categorizados:', err);
    res.json(Resposta.erro('Erro no teste de links: ' + err.message));
  }
});

// Testar conexão com Bitrix
router.get('/teste-bitrix', async (req: any, res) => {
  try {
    console.log('Testando conexão com Bitrix...');

    // Criar um lead de teste
    const leadTeste = new Lead();
    leadTeste.configurarDadosBasicos(
      1, // crmEmpresaId
      'Teste Bitrix',
      'Empresa Teste Ltda',
      '***********',
      'teste_bitrix',
      'Bio de teste para integração Bitrix',
      OrigemLead.Instagram
    );

    leadTeste.score = 85;
    leadTeste.segmento = 'Alimentação' as any;
    leadTeste.observacoes = 'Lead de teste criado para validar integração com Bitrix24';
    leadTeste.instagramData = {
      followers: 1500,
      following: 300,
      accountType: 'Business',
      businessCategory: 'Restaurante',
      bio: 'Restaurante especializado em comida italiana'
    };

    const bitrixService = BitrixServiceFactory.criarInstancia();
    const resultado = await bitrixService.criarLead(leadTeste);

    if (resultado.sucesso) {
      res.json(Resposta.sucesso({
        bitrixId: resultado.data,
        mensagem: 'Teste de integração realizado com sucesso',
        leadTeste: {
          nome: leadTeste.nomeResponsavel,
          empresa: leadTeste.empresa,
          telefone: leadTeste.telefone
        }
      }));
    } else {
      res.json(Resposta.erro(`Falha no teste: ${resultado.erro}`));
    }
  } catch (err) {
    console.error('Erro no teste de integração Bitrix', err);
    res.json(Resposta.erro('Erro no teste de integração: ' + err.message));
  }
});


// ===== ROTAS PARA GERENCIAR LINKS =====

// Listar links de um lead
router.get('/:id/links', async (req: any, res) => {
  const mapeadorLink = new MapeadorDeLeadLink();
  try {
    const links = await mapeadorLink.buscarPorLead(parseInt(req.params.id));
    res.json(Resposta.sucesso(links));
  } catch (err) {
    console.error('Erro ao listar links do lead', err);
    res.json(Resposta.erro('Erro ao listar links do lead'));
  }
});

// Adicionar link a um lead
router.post('/:id/links', async (req: any, res) => {
  const mapeadorLink = new MapeadorDeLeadLink();
  try {
    const obj = req.body;
    obj.crmLeadId = parseInt(req.params.id);
    const novoLink = await mapeadorLink.insiraSync(obj);
    res.json(Resposta.sucesso(novoLink));
  } catch (err) {
    console.error('Erro ao adicionar link ao lead', err);
    res.json(Resposta.erro('Erro ao adicionar link ao lead'));
  }
});

// Atualizar link específico
router.put('/:id/links/:linkId', async (req: any, res) => {
  const mapeadorLink = new MapeadorDeLeadLink();
  try {
    const obj = req.body;
    obj.id = parseInt(req.params.linkId);
    obj.crmLeadId = parseInt(req.params.id);
    const linkAtualizado = await mapeadorLink.atualizeSync(obj);
    res.json(Resposta.sucesso(linkAtualizado));
  } catch (err) {
    console.error('Erro ao atualizar link do lead', err);
    res.json(Resposta.erro('Erro ao atualizar link do lead'));
  }
});

// Remover link específico
router.delete('/:id/links/:linkId', async (req: any, res) => {
  const mapeadorLink = new MapeadorDeLeadLink();
  try {
    await mapeadorLink.removaAsync({ id: parseInt(req.params.linkId) });
    res.json(Resposta.sucesso({}));
  } catch (err) {
    console.error('Erro ao remover link do lead', err);
    res.json(Resposta.erro('Erro ao remover link do lead'));
  }
});

// Remover link por tipo
router.delete('/:id/links/tipo/:tipo', async (req: any, res) => {
  const mapeadorLink = new MapeadorDeLeadLink();
  try {
    await mapeadorLink.removerPorTipo(parseInt(req.params.id), req.params.tipo);
    res.json(Resposta.sucesso({}));
  } catch (err) {
    console.error('Erro ao remover link por tipo', err);
    res.json(Resposta.erro('Erro ao remover link por tipo'));
  }
});

export const LeadsController: Router = router;
