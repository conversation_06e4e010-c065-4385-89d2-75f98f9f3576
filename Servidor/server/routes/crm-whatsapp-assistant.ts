import { Request, Response, Router } from "express";
import { ChatGPTService } from "../service/ia/ChatGPTService";
import MapeadorDeLead from "../mapeadores/MapeadorDeLead";
import { CrmWhatsappAssistantService } from "../service/crm/CrmWhatsappAssistantService";

const router = Router();

/**
 * API endpoint para gerar sugestões de resposta usando IA
 * Baseado na metodologia SPIN Selling
 */
router.post('/api/whatsapp/sugestao-resposta', async (req: Request, res: Response) => {
  console.log('[API WhatsApp Assistant] Recebendo request:', req.body);
  try {
    const { telefone, mensagens, faseSpin, produto, tomConversa } = req.body;

    if (!telefone || !mensagens ) {
      return res.status(400).json({
        sucesso: false,
        erro: 'Telefone e mensagens são obrigatórios'
      });
    }

    const empresaId = (req as any).empresa?.id || 1;

    // Buscar dados do lead no banco
    let lead = null;
    try {
      const mapeadorLead = new MapeadorDeLead();
      lead = await mapeadorLead.selecioneSync({ telefone });
    } catch (erro) {
      console.error(`[whatsapp-assistant] Erro ao buscar lead: ${erro}`);
    }

    // Instanciar serviço para gerar sugestões
    const assistantService = new CrmWhatsappAssistantService();

    // Gerar sugestão usando IA
    console.log('[API WhatsApp Assistant] Gerando sugestão para:', { telefone, faseSpin, produto, tomConversa });
    const resultado = await assistantService.gerarSugestaoResposta({
      telefone,
      mensagens,
      faseSpin: faseSpin || 'situacao',
      produto: produto || 'Meu Cardápio',
      tomConversa: tomConversa || 'formal',
      contato: lead
    });
    console.log('[API WhatsApp Assistant] Resultado gerado:', resultado);

    return res.json({
      sucesso: true,
      data: resultado
    });

  } catch (erro: any) {
    console.error('[whatsapp-assistant] Erro ao gerar sugestão:', erro);
    return res.status(500).json({
      sucesso: false,
      erro: 'Erro ao gerar sugestão de resposta',
      detalhes: erro.message
    });
  }
});

/**
 * API endpoint para detectar a fase SPIN automaticamente
 */
router.post('/api/whatsapp/detectar-fase-spin', async (req: Request, res: Response) => {
  try {
    const { mensagens } = req.body;

    if (!mensagens || mensagens.length === 0) {
      return res.status(400).json({
        sucesso: false,
        erro: 'Mensagens são obrigatórias para detectar a fase'
      });
    }

    const assistantService = new CrmWhatsappAssistantService();
    const faseSpin = await assistantService.detectarFaseSpin(mensagens);

    return res.json({
      sucesso: true,
      data: {
        faseSpin,
        confianca: 0.85 // TODO: Implementar cálculo de confiança real
      }
    });

  } catch (erro: any) {
    console.error('[whatsapp-assistant] Erro ao detectar fase SPIN:', erro);
    return res.status(500).json({
      sucesso: false,
      erro: 'Erro ao detectar fase SPIN',
      detalhes: erro.message
    });
  }
});

/**
 * API endpoint para detectar e salvar a fase SPIN no lead
 */
router.post('/api/whatsapp/detectar-e-salvar-fase-spin', async (req: Request, res: Response) => {
  try {
    const { telefone, mensagens } = req.body;

    if (!telefone) {
      return res.status(400).json({
        sucesso: false,
        erro: 'Telefone é obrigatório'
      });
    }

    if (!mensagens || mensagens.length === 0) {
      return res.status(400).json({
        sucesso: false,
        erro: 'Mensagens são obrigatórias para detectar a fase'
      });
    }

    const assistantService = new CrmWhatsappAssistantService();

    // Detectar a fase SPIN
    const faseSpin = await assistantService.detectarFaseSpin(mensagens);
    const confianca = 0.85; // TODO: Implementar cálculo de confiança real

    // Buscar o lead pelo telefone
    const mapeadorLead = new MapeadorDeLead();
    const leads = await mapeadorLead.listeAsync({
      crmEmpresaId: (req as any).empresa?.id,
      telefone: telefone
    });

    let lead = null;
    if (leads && leads.length > 0) {
      lead = leads[0];

      // Atualizar o lead com a fase detectada
      lead.faseSpinDetectada = faseSpin;
      lead.confiancaFaseDetectada = confianca;
      lead.dataUltimaDeteccaoFase = new Date();

      await mapeadorLead.atualizeSync(lead);
    }

    return res.json({
      sucesso: true,
      data: {
        faseSpin,
        confianca,
        leadEncontrado: !!lead,
        leadId: lead?.id
      }
    });

  } catch (erro) {
    console.error('[detectar-e-salvar-fase-spin] Erro:', erro);
    return res.status(500).json({
      sucesso: false,
      erro: 'Erro interno do servidor'
    });
  }
});

/**
 * API endpoint para salvar uso de sugestão (analytics)
 */
router.post('/api/whatsapp/salvar-uso-sugestao', async (req: Request, res: Response) => {
  try {
    const { telefone, faseSpin, sugestaoGerada, sugestaoUsada, tempoResposta } = req.body;

    const empresaId = (req as any).empresa?.id || 1;

    // TODO: Implementar salvamento no banco de dados
    // Por enquanto, apenas logamos
    console.log('[whatsapp-assistant] Uso de sugestão:', {
      empresaId,
      telefone,
      faseSpin,
      sugestaoUsada: !!sugestaoUsada,
      tempoResposta
    });

    return res.json({
      sucesso: true,
      mensagem: 'Uso de sugestão registrado'
    });

  } catch (erro: any) {
    console.error('[whatsapp-assistant] Erro ao salvar uso de sugestão:', erro);
    return res.status(500).json({
      sucesso: false,
      erro: 'Erro ao salvar uso de sugestão',
      detalhes: erro.message
    });
  }
});

/**
 * API endpoint para gerar mensagens de rapport/atratividade
 * Para trabalho outbound de vendas
 */
router.post('/api/whatsapp/gerar-rapport', async (req: Request, res: Response) => {
  console.log('[API WhatsApp Rapport] Recebendo request:', req.body);
  try {
    const { telefone, nomeContato, empresa, tipoAbordagem, produto, ultimaMensagem, mensagensSDR } = req.body;

    if (!telefone || !tipoAbordagem) {
      return res.status(400).json({
        sucesso: false,
        erro: 'Telefone e tipo de abordagem são obrigatórios'
      });
    }

    const empresaId = (req as any).empresa?.id || 1;

    // Buscar dados do lead no banco se não foram fornecidos
    let lead = null;
    try {
      console.log('[API WhatsApp Rapport] Buscando lead no banco:', { telefone });
      const mapeadorLead = new MapeadorDeLead();
      lead = await mapeadorLead.selecioneSync({ telefone });
    } catch (erro) {
      console.error(`[whatsapp-rapport] Erro ao buscar lead: ${erro}`);
    }

    // Instanciar serviço para gerar mensagem de rapport
    const assistantService = new CrmWhatsappAssistantService();

    // Gerar mensagem de rapport usando IA
    console.log('[API WhatsApp Rapport] Gerando mensagem para:', { telefone, tipoAbordagem, produto });
    const resultado = await assistantService.gerarMensagemRapport({
      telefone,
      nomeContato: nomeContato || lead?.nomeResponsavel,
      empresa: empresa || lead?.crmEmpresa?.nome || lead?.empresa,
      tipoAbordagem,
      produto: produto || 'Meu Cardápio',
      ultimaMensagem,
      mensagensSDR: mensagensSDR || [], // Mensagens já enviadas pelo SDR
      contato: lead // Passar o objeto completo do lead
    });
    console.log('[API WhatsApp Rapport] Resultado gerado:', resultado);

    return res.json({
      sucesso: true,
      data: resultado
    });

  } catch (erro: any) {
    console.error('[whatsapp-rapport] Erro ao gerar mensagem de rapport:', erro);
    return res.status(500).json({
      sucesso: false,
      erro: 'Erro ao gerar mensagem de rapport',
      detalhes: erro.message
    });
  }
});

/**
 * API endpoint para gerar mensagem de apresentação profissional
 * Para primeiro contato com leads
 */
router.post('/api/whatsapp/gerar-apresentacao', async (req: Request, res: Response) => {
  console.log('[API WhatsApp Apresentação] Recebendo request:', req.body);
  try {
    const { telefone, nomeContato, empresa, produto, lead } = req.body;

    if (!telefone) {
      return res.status(400).json({
        sucesso: false,
        erro: 'Telefone é obrigatório'
      });
    }

    const empresaId = (req as any).empresa?.id || 1;

    // Buscar dados do lead no banco se não foi passado
    let leadData = lead;
    if (!leadData && (!nomeContato || !empresa)) {
      try {
        const mapeadorLead = new MapeadorDeLead();
        leadData = await mapeadorLead.selecioneSync({ telefone });
      } catch (erro) {
        console.error(`[whatsapp-apresentacao] Erro ao buscar lead: ${erro}`);
      }
    }

    // Instanciar serviço para gerar mensagem de apresentação
    const assistantService = new CrmWhatsappAssistantService();

    // Gerar mensagem de apresentação usando IA
    console.log('[API WhatsApp Apresentação] Gerando mensagem para:', { telefone, nomeContato, empresa });
    const resultado = await assistantService.gerarMensagemApresentacao({
      telefone,
      nomeContato: nomeContato || leadData?.nomeResponsavel,
      empresa: empresa || leadData?.crmEmpresa?.nome || leadData?.empresa,
      produto: produto || 'Meu Cardápio',
      lead: leadData
    });
    console.log('[API WhatsApp Apresentação] Resultado gerado:', resultado);

    return res.json({
      sucesso: true,
      data: resultado
    });

  } catch (erro: any) {
    console.error('[whatsapp-apresentacao] Erro ao gerar mensagem de apresentação:', erro);
    return res.status(500).json({
      sucesso: false,
      erro: 'Erro ao gerar mensagem de apresentação',
      detalhes: erro.message
    });
  }
});

export default router;
