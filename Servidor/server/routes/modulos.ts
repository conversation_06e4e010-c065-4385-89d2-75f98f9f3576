import {Router} from "express";
import { Resposta } from "../utils/Resposta";

import {Modulo} from "../domain/Modulo";
import {MapeadorDeModulo} from "../mapeadores/MapeadorDeModulo";
import {Empresa} from "../domain/Empresa";
import {MapeadorDeEmpresa} from "../mapeadores/MapeadorDeEmpresa";
import {EmpresaService} from "../service/EmpresaService";
import {IuguService} from "../service/IuguService";
import * as moment from "moment";
import { FaturaAtivacao } from "../domain/FaturaAtivacao";
import {MapeadorDeContrato} from "../mapeadores/MapeadorDeContrato";
import {DateUtils} from "../lib/DateUtils";
import {MapeadorDeFatura} from "../mapeadores/MapeadorDeFatura";
import {Fatura} from "../domain/faturamento/Fatura";
import {DTOFatura} from "../lib/dto/DTOFatura";

import {ContratoService} from "../service/ContratoService";

const router: Router = Router();

router.get('/', async (req, res) => {
  try {
    const mapeador = new MapeadorDeModulo();
    const modulos = await mapeador.listeAsync(req.query);
    res.json(Resposta.sucesso(modulos));
  } catch (error) {
    res.json(Resposta.erro('Erro ao listar módulos: ' + error.message));
  }
});

router.get('/:id', async (req, res) => {
  try {
    const mapeador = new MapeadorDeModulo();
    const modulo = await mapeador.selecioneSync({id: req.params.id});
    res.json(Resposta.sucesso(modulo));
  } catch (error) {
    res.json(Resposta.erro('Erro ao buscar módulo: ' + error.message));
  }
});

router.post('/', async (req, res) => {
  try {

    const mapeador = new MapeadorDeModulo();
    const modulo = Object.assign(new Modulo(), req.body);
    await mapeador.insiraSync(modulo);
    res.json(Resposta.sucesso(modulo));
  } catch (error) {
    res.json(Resposta.erro('Erro ao inserir módulo: ' + error.message));
  }
});

router.put('/:id', async (req, res) => {
  try {
    const mapeador = new MapeadorDeModulo();
    const modulo = Object.assign(new Modulo(), req.body);
    modulo.id = Number(req.params.id);
    await mapeador.atualizeSync(modulo);
    res.json(Resposta.sucesso(modulo));
  } catch (error) {
    res.json(Resposta.erro('Erro ao atualizar módulo: ' + error.message));
  }
});

router.delete('/:id', async (req, res) => {
  try {
    const mapeador = new MapeadorDeModulo();
    await mapeador.removaAsync(Number(req.params.id));
    res.json(Resposta.sucesso('Módulo removido com sucesso'));
  } catch (error) {
    res.json(Resposta.erro('Erro ao remover módulo: ' + error.message));
  }
});




//
router.post('/:empresaId/desativar/:id', async (req: any, res: any) => {
  const empresaId = Number(req.params.empresaId);

  // Buscar empresa para dados do cliente
  const mapeadorEmpresa = new MapeadorDeEmpresa();
  const empresa = await mapeadorEmpresa.selecioneSync({id: empresaId});
  if (!empresa)
    return res.json(Resposta.erro('Empresa não encontrada.'));

  let modulo = empresa.modulos.find((item: any) => item.id === Number(req.params.id));

  if(!modulo)
    return res.json(Resposta.erro('Modulo nao ativo na empresa: ' + req.params.id));


  await new EmpresaService().removaModulo(modulo, empresa);
  await new MapeadorDeEmpresa().removaDasCaches(empresa);
  res.json(Resposta.sucesso())
})

// Gerar fatura de ativação
router.post('/:empresaId/fatura-ativacao', async (req: any, res: any) => {
  try {
    const empresaId = Number(req.params.empresaId);
    const {  numeroParcelas, modulos, valor} = req.body;

    if (!modulos || !Array.isArray(modulos) || modulos.length === 0)
      return res.json(Resposta.erro('Itens da fatura são obrigatórios.'));

    // Buscar empresa para dados do cliente
    const mapeadorEmpresa = new MapeadorDeEmpresa();
    const empresa = await mapeadorEmpresa.selecioneSync({id: empresaId});

    if (!empresa)
      return res.json(Resposta.erro('Empresa não encontrada.'));

    // Gerar fatura no Iugu
    const iuguService = new IuguService();

    const dataVencimento = DateUtils.obtenhaVencimentoDiaUtil();

    let itensFatura: any = [], customVariables: any = []

    modulos.forEach((modulo: any) => {
      modulo.itensFaturaveis.forEach((item: any) => {
        itensFatura.push({
          descricao: item.descricao,
          qtde: 1,
          total: item.total
        })
      })

      customVariables.push({
        "name": "modulo",
        "value": modulo.nome
      })
    })

    const invoice: any = await iuguService.crieFaturaAvulsa(
      empresa,
      itensFatura,
      'all', // Todas as formas de pagamento
      dataVencimento,
      numeroParcelas, // Máximo 1 parcela
      `ativacaomodulos`,
      customVariables
    );

    if (!invoice || !invoice.id)
      return res.json(Resposta.erro('Erro ao gerar fatura no Iugu.'));

    // Salvar fatura de ativação no banco
    const faturaAtivacao = new FaturaAtivacao(  empresa,  valor , dataVencimento);

    faturaAtivacao.codigo = invoice.id;

    await faturaAtivacao.insira();

    faturaAtivacao.setDadosInvoice(invoice);
    await faturaAtivacao.atualize();

    res.json(Resposta.sucesso(new DTOFatura(faturaAtivacao)));

  } catch (error) {
    console.error('Erro ao gerar fatura de ativação:', error);
    res.json(Resposta.erro('Erro ao gerar fatura de ativação: ' +  (error.message || error)));
  }
});

// Verificar status de pagamento da fatura
router.get('/faturas/:faturaId/status', async (req, res) => {
  try {
    const faturaId = req.params.faturaId;
    // Buscar fatura de ativação no banco
    const faturaAtivacao = await new MapeadorDeFatura().selecioneAvulsa({id: Number(faturaId)})

    if (!faturaAtivacao)
      return res.json(Resposta.erro('Fatura de ativação não encontrada.'));

    const invoice: any = await new IuguService().obtenhaFatura(faturaAtivacao.codigo);

    if (!invoice)   return res.json(Resposta.erro('Fatura não encontrada.'));

    await new ContratoService().sincronizeFaturaAtivacaoModulo(faturaAtivacao, invoice).catch((err) => {
      throw err
    });

    res.json(Resposta.sucesso(new DTOFatura(faturaAtivacao)));
  } catch (error) {
    console.error('Erro ao verificar status da fatura:', error);
    res.json(Resposta.erro('Erro ao verificar status da fatura: ' + (error.message || error)));
  }
});

// Rota para calcular valores dos módulos baseado no contrato da empresa
router.get('/empresa/:empresaId/valores', async (req: any, res: any) => {
  try {
    const empresaId = Number(req.params.empresaId);

    // Buscar contrato da empresa
    const mapeadorContrato = new MapeadorDeContrato();
    const empresa = await new MapeadorDeEmpresa().selecioneSync({id: empresaId});
    const contrato = await mapeadorContrato.selecioneSync({idEmpresa: empresa.id});

    // Buscar todos os módulos
    const mapeadorModulo = new MapeadorDeModulo();
    const modulos = await mapeadorModulo.listeAsync({});

    const faturasPendentesAtivacao: Array<Fatura> =
      await new MapeadorDeFatura().listeAvulsa({ alvo: 'modulos', pendente: true, idEmpresa: empresaId})

    // Calcular valores baseado no contrato
    const modulosComValores = modulos.map((modulo: any) => {
      const valores: any = calcularValoresModulo(modulo, contrato);

      let itensFaturaveis: any = [];

      if(modulo.valorAtivacao)
          itensFaturaveis.push({
            descricao: `Módulo ${modulo.nome} - Ativação`,
            tipo: 'Taxa de Ativação',
            qtde: 1,
            total: modulo.valorAtivacao,
            taxa: true,
          })


      if(modulo.valorMensalidade){
        let descricao = `Módulo ${modulo.nome} - Mensalidade`

        if(valores.mesesRestantes > 1)
           descricao = `Módulo ${modulo.nome} - ${valores.mesesRestantes} meses restantes do ${valores.tipoAssinatura}`

        itensFaturaveis.push({
          descricao: descricao,
          tipo: 'Mensalidade',
          qtde: 1,
          total: valores.valorMensalidadeCalculado
        })
      }

      return {
        ...modulo,
        ativo: empresa.temModulo(modulo),
        itensFaturaveis: itensFaturaveis,
        mesesRestantes: valores.mesesRestantes,
        diasParaVencimento: valores.diasParaVencimento,
        tipoAssinatura: valores.tipoAssinatura,
        numeroParcelas: valores.mesesRestantes,
        dataProximoVencimento: contrato ? contrato.dataProximoVencimento : null
      };
    });

    let faturasDto: any = [];

    faturasPendentesAtivacao.forEach((fatura: Fatura) => {
      let modulosName: Array<number> = fatura.getModulosAtivar();
      let modulosDaFatura: any = [];

      modulosName.forEach((nomeModulo: number) => {
        let modulo = modulosComValores.find((item: any) => item.nome === nomeModulo )

        if(modulo){
          modulo.aguardandoAtivacao  = true;
          modulosDaFatura.push({id: modulo.id, nome: modulo.nome})
        }
      })

      let faturaDto: DTOFatura = new DTOFatura(fatura);

      faturaDto.modulos = modulosDaFatura;

      faturasDto.push(faturaDto)
    })

    let resposta: any  = {
         bloqueada: empresa.estaBloqueada(),
         modulos: modulosComValores,
         faturasPendentes: faturasDto,
         dataProximoVencimento: contrato ? contrato.dataProximoVencimento : null
    }

    res.json(Resposta.sucesso(resposta));
  } catch (error) {
    console.error('Erro ao calcular valores dos módulos:', error);
    res.json(Resposta.erro('Erro ao calcular valores dos módulos: ' + error.message));
  }
});


router.post('/:empresaId/config',  async (req: any, res: any) => {
  const dados = req.body;
  console.log(dados)

  let empresa: Empresa = await new MapeadorDeEmpresa().selecioneSync(Number(req.params.empresaId));

  new EmpresaService().atualizeConfigModulos(empresa, dados).then( () => {
    res.json(Resposta.sucesso())
  }).catch( (erro: any) => {
    res.json( Resposta.erro(erro))
  });
})

router.post('/:empresaId/gratuitos/ativar',  async (req: any, res: any) => {
  const { modulos } = req.body;
  console.log(modulos)
  let empresa: Empresa = await new MapeadorDeEmpresa().selecioneSync(Number(req.params.empresaId));

  new EmpresaService().ativeModulosGratuitos(empresa, modulos).then( () => {
    res.json(Resposta.sucesso())
  }).catch( (erro: any) => {
    res.json( Resposta.erro(erro))
  });

})

router.get('/pagos', async (req, res) => {
  try {
    const mapeador = new MapeadorDeModulo();
    const modulos = await mapeador.listeAsync({});
    res.json(Resposta.sucesso(modulos));
  } catch (e) {
    res.json(Resposta.erro(e.message));
  }
});

router.get('/fatura/pagos', async (req, res) => {
  try {
    const mapeador = new MapeadorDeModulo();
    const modulos = await mapeador.listeAsync({});

    // Filtra apenas os módulos que podem ser adicionados na fatura
    const modulosFatura = modulos.filter((modulo: any) => {
      return modulo.valor > 0 || modulo.valorAtivacao > 0;
    });

    res.json(Resposta.sucesso(modulosFatura));
  } catch (e) {
    res.json(Resposta.erro(e.message));
  }
});

// Função para calcular valores baseado no contrato
function calcularValoresModulo(modulo: any, contrato: any) {
  // 1. Determinar tipo de assinatura baseado no plano
  const intervaloPlano = contrato && contrato.plano ? contrato.plano.intervalo : 1; // em meses
  let tipoAssinatura = 'mensal';

  if (intervaloPlano === 12) {
    tipoAssinatura = 'anual';
  } else if (intervaloPlano === 6) {
    tipoAssinatura = 'semestral';
  } else if (intervaloPlano === 3) {
    tipoAssinatura = 'trimestral';
  }

  // 4. Aplicar cálculos aos valores do módulo
  const valorMensalidadeOriginal = modulo.valorMensalidade || 0;
  let mesesRestantes = 0, diasParaVencimento = 0;
  // Para mensalidade: calcular valor total pelos meses restantes da assinatura
  let valorMensalidadeCalculado = valorMensalidadeOriginal;

  if(contrato){
    // 2. Calcular dias para próximo vencimento
    const agora = moment();
    const proximoVencimento = moment(contrato.dataProximoVencimento);
    diasParaVencimento = Math.max(0, proximoVencimento.diff(agora, 'days'));

    // 3. Calcular meses restantes
    mesesRestantes = Math.max(0, Math.ceil(diasParaVencimento / 30));

    if (diasParaVencimento > 0 && valorMensalidadeOriginal > 0) {
      // Cobrar o valor da mensalidade multiplicado pelos meses restantes
      valorMensalidadeCalculado = Number((valorMensalidadeOriginal * mesesRestantes).toFixed(2));
    }
  }

  return {
    valorMensalidadeCalculado: valorMensalidadeCalculado,
    diasParaVencimento: diasParaVencimento,
    tipoAssinatura: tipoAssinatura,
    mesesRestantes: mesesRestantes
  };
}



export const ModulosController: Router = router;
