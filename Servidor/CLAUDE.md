# PromoKit System Overview for <PERSON>

This document provides an overview of the PromoKit system for Claude instances working with this codebase.

## Project Overview

PromoKit (also known as "SorteiemeJs" in the README) is a restaurant and food service management platform with the following key features:

- Customer loyalty programs and promotions
- Online ordering and food delivery
- Menu/catalog management
- QR code based solutions
- Marketing campaign management
- WhatsApp and Instagram integration
- Payment processing integration (multiple providers)
- POS (Point of Sale) functionality

The system appears to be primarily focused on Brazilian businesses based on the payment integrations and location data.

## System Architecture

### Frontend
- Angular 14.x application (originally started with v1.7.2)
- Multiple frontend applications:
  - Admin panel for businesses
  - Customer-facing storefront
  - Group/franchise management interfaces
  - Mobile-optimized views

### Backend
- Node.js Express server (TypeScript)
- MySQL database
- Redis for session storage and caching
- Bull for job processing
- Socket.IO for real-time communications
- Integrations with multiple external APIs:
  - Payment processors (PagSeguro, Cielo, MercadoPago, etc.)
  - WhatsApp Cloud API
  - Instagram API
  - Google Maps/Geo services
  - OpenAI/ChatGPT for AI features

## Project Structure

The project follows a standard structure:

```
/Servidor
├── server/             # Backend TypeScript source
│   ├── App.ts          # Main application entry point
│   ├── routes/         # API endpoints
│   ├── domain/         # Business domain models
│   ├── mapeadores/     # Data mappers (ORM-like functionality)
│   ├── service/        # Business logic services
│   ├── config/         # Configuration files
│   └── utils/          # Utility functions
├── src/                # Frontend Angular source
├── distServer/         # Compiled backend code
├── sql/                # SQL scripts and database migrations
├── projects/           # Additional Angular projects
└── config files        # Various configuration files
```

## Key Configuration Files

- `server/config.json`: Environment-specific configuration for different deployment environments
- `server/database.json`: Database connection configuration
- `package.json`: Project dependencies and scripts

## Development Workflow

### Build & Run Commands

The project uses npm scripts for development and building:

```bash
# Start the full development environment (Angular + TypeScript + Express)
npm run start

# Start only the admin interface
npm run startAdmin

# Start only the storefront
npm run startLoja 

# Compile the TypeScript server code
npm run compilar

# Build the project for production
npm run build

# Run tests
npm run test
```

### Key Development Scripts

- `startAngular`: Starts the Angular admin application
- `startAngularLoja`: Starts the Angular storefront application
- `startTsc`: Watches and compiles TypeScript files
- `startExpress`: Starts the Express server
- `compilar`: Compiles TypeScript files once

## System Components

### Authentication & Authorization

The system uses a combination of authentication methods:
- Passport.js for local authentication
- JWT for API authentication
- Session-based authentication for web interfaces
- OAuth for third-party services

### Multi-tenant Architecture

The system supports multiple businesses/restaurants with their own configurations:
- Domain-based tenant identification
- Domain-specific routing and branding
- Franchise/group management for chains

### WhatsApp & Messaging Integration

The system has extensive WhatsApp integration:
- Message sending and receiving
- Chatbot functionality
- Order status notifications
- Marketing campaigns

### Payment Processing

Supports multiple payment providers:
- PagSeguro
- Cielo
- MercadoPago
- E-Rede Itaú
- TunaPay
- PagBank

### API & Integration Points

The system provides multiple API endpoints for integration:
- `/api/v1/`: Bearer token authenticated API
- Various webhook endpoints for third-party services
- Real-time event system

## Database Structure

The database follows a domain-driven design with tables corresponding to business entities. Key entities include:

- Empresa (Company/Business)
- Contato (Contact/Customer)
- Pedido (Order)
- Produto (Product)
- Campanha (Marketing Campaign)
- CartaoCliente (Customer Loyalty Card)

## AI Integration

The system has AI features including:
- ChatGPT integration for customer support
- AI-driven marketing campaigns
- Product recommendations

## Working with the Codebase

### Common Tasks

1. **Adding a new API endpoint**:
   - Create a new controller in `server/routes/`
   - Add the route to the main `App.ts` file
   - Implement the necessary service and mapper classes

2. **Adding a new database entity**:
   - Create a domain class in `server/domain/`
   - Create a mapper in `server/mapeadores/`
   - Create an XML mapping file in `server/mapeamentos/`

3. **Adding frontend functionality**:
   - Work with the Angular components in `src/app/`
   - Os services Angular sempre herdam de ServerService e retorno dos métodos de busca sempre são os dados, se a resposta falha (sucesso false) ele já dispara um Error.
   - Use the existing services for API communication
   - Sistema usa Boostrap 4 na UI
   - Sempre se atende para adicionar espaços em branco entre os botões para eles não ficarem colados

### Important Notes

1. The system uses a custom MyBatis-like ORM system for database access
No nosso mybatis não existe <where> assim, tem que ser where do sql mesmo.
2. Session management uses Redis for persistence
3. The system has extensive multi-tenancy features that need to be considered
4. Environment configuration is managed through `server/config.json`

## Production Deployment

The system appears to be deployed in a Linux environment with:
- PM2 for process management
- Nginx or similar for HTTP serving
- Redis for session management
- MySQL for database

## Common Issues and Solutions

1. **Session Management**: Be careful with the Redis session store implementation
2. **Database Transactions**: The system uses custom database handling with specific patterns
3. **Multi-tenancy**: Always consider the multi-tenant nature when making changes
4. **Payment Integrations**: Payment providers have specific requirements and error handling needs

## Additional Resources

Refer to the following files for more details:
- `README.md`: Basic project setup and commands
- `CHANGELOG.md`: Recent changes to the system
- `package.json`: All dependencies and scripts
