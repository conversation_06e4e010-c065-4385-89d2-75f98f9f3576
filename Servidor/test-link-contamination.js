const axios = require('axios');

/**
 * Teste para reproduzir e diagnosticar o problema de cross-contamination de links
 * entre leads vindos do Bitrix
 */

const BASE_URL = 'http://localhost:5000';

// IDs de exemplo do Bitrix (ajustar conforme necessário)
const LEAD_IDS = [
  '123', // Lead 1
  '124', // Lead 2
  '125'  // Lead 3
];

async function testLinkContamination() {
  console.log('🔍 TESTE: Iniciando teste de contaminação de links entre leads');
  console.log('========================================================');
  
  const results = [];
  
  for (let i = 0; i < LEAD_IDS.length; i++) {
    const leadId = LEAD_IDS[i];
    console.log(`\n📋 TESTE: [${i + 1}/${LEAD_IDS.length}] Testando Lead ID: ${leadId}`);
    console.log('─'.repeat(50));
    
    try {
      const response = await axios.get(`${BASE_URL}/leads/bitrix/${leadId}`);
      
      if (response.data.sucesso) {
        const leadData = response.data.data;
        
        console.log(`✅ TESTE: Lead ${leadId} convertido com sucesso:`);
        console.log(`   Nome: ${leadData.nomeResponsavel}`);
        console.log(`   Empresa: ${leadData.empresa}`);
        console.log(`   Links encontrados: ${leadData.links ? leadData.links.length : 0}`);
        
        if (leadData.links && leadData.links.length > 0) {
          console.log('   📎 Links:');
          leadData.links.forEach((link, index) => {
            console.log(`     [${index}] ${link.url} (tipo: ${link.tipo})`);
          });
        }
        
        results.push({
          leadId,
          nome: leadData.nomeResponsavel,
          empresa: leadData.empresa,
          links: leadData.links || [],
          linksCount: leadData.links ? leadData.links.length : 0
        });
        
      } else {
        console.log(`❌ TESTE: Erro ao buscar Lead ${leadId}: ${response.data.erro}`);
      }
      
    } catch (error) {
      console.log(`💥 TESTE: Exceção ao buscar Lead ${leadId}:`, error.message);
    }
    
    // Aguardar um pouco entre requests
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  console.log('\n🔎 TESTE: Análise de contaminação de links');
  console.log('==========================================');
  
  // Analisar se há links duplicados entre diferentes leads
  const allLinks = new Map();
  
  results.forEach(result => {
    result.links.forEach(link => {
      if (!allLinks.has(link.url)) {
        allLinks.set(link.url, []);
      }
      allLinks.get(link.url).push({
        leadId: result.leadId,
        nome: result.nome,
        empresa: result.empresa
      });
    });
  });
  
  let contaminationFound = false;
  
  allLinks.forEach((leads, url) => {
    if (leads.length > 1) {
      contaminationFound = true;
      console.log(`🚨 CONTAMINAÇÃO DETECTADA! URL duplicada:`);
      console.log(`   URL: ${url}`);
      console.log(`   Presente em ${leads.length} leads:`);
      leads.forEach((lead, index) => {
        console.log(`     [${index + 1}] Lead ${lead.leadId}: ${lead.nome} (${lead.empresa})`);
      });
      console.log('');
    }
  });
  
  if (!contaminationFound) {
    console.log('✅ TESTE: Nenhuma contaminação de links detectada!');
  }
  
  console.log('\n📊 TESTE: Resumo dos resultados');
  console.log('===============================');
  results.forEach(result => {
    console.log(`Lead ${result.leadId}: ${result.nome} - ${result.linksCount} links`);
  });
  
  console.log('\n🏁 TESTE: Teste concluído!');
  console.log('Verifique os logs do servidor para detalhes da conversão dos leads.');
}

// Executar teste
testLinkContamination().catch(console.error);