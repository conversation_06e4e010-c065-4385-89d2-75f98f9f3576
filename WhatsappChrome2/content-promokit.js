// Content script injetado nas páginas *.promokit.com.br
// Faz a ponte entre a página PromoKit (Angular) e a extensão.

// Estado da conversa atual
let contextoAtual = {
  mensagens: [],
  contatoAtual: '',
  telefoneAtual: '',
  etapaFunil: 'Prospecção',
  tomConversa: 'Consultivo'
};

// 1. Mensagens vindas do background / sidepanel → página
chrome.runtime.onMessage.addListener((msg) => {
  console.log('Mensagem recebida no content-promokit:', msg);
  
  // Quando um contato é selecionado no WhatsApp
  if (msg.type === 'SELECIONOU_CONTATO') {
    // Atualiza informações do contato no contexto
    contextoAtual.contatoAtual = msg.payload.nome || '';
    contextoAtual.telefoneAtual = msg.payload.telefone || '';
    
    // Reenvia para o contexto da página (AppComponent já escuta window.onmessage)
    window.postMessage({ tipo: 'SELECIONOU_CONTATO', payload: msg.payload }, '*');
    
    // Também envia o contexto atualizado para o CRM
    enviarContextoParaCRM();
  }
  
  // Quando novas mensagens são detectadas no WhatsApp
  if (msg.type === 'NOVAS_MENSAGENS') {
    // Atualiza o histórico de mensagens no contexto
    if (msg.payload && Array.isArray(msg.payload.mensagens)) {
      contextoAtual.mensagens = msg.payload.mensagens;
      enviarContextoParaCRM();
    }
  }
});

// 2. Mensagens da página → background (se necessário)
window.addEventListener('message', (e) => {
  if (!e.data || !e.data.tipo) return;
  
  // Página quer solicitar algo à extensão
  if (e.data.tipo === 'PROMOKIT_TO_EXTENSION') {
    chrome.runtime.sendMessage(e.data.msg);
  }
  
  // Atualização da etapa do funil ou tom da conversa
  if (e.data.tipo === 'ATUALIZAR_CONTEXTO') {
    if (e.data.payload) {
      // Atualiza apenas os campos fornecidos
      if (e.data.payload.etapaFunil) {
        contextoAtual.etapaFunil = e.data.payload.etapaFunil;
      }
      if (e.data.payload.tomConversa) {
        contextoAtual.tomConversa = e.data.payload.tomConversa;
      }
      console.log('Contexto atualizado:', contextoAtual);
    }
  }
  
  // Requisição para enviar mensagem para o WhatsApp
  if (e.data.tipo === 'ENVIAR_MENSAGEM_WHATSAPP') {
    if (e.data.payload && e.data.payload.texto) {
      chrome.runtime.sendMessage({
        tipo: 'ENVIAR_TEXTO_WHATSAPP',
        texto: e.data.payload.texto
      });
      
      // Adiciona a mensagem enviada ao contexto
      const novaMensagem = {
        texto: e.data.payload.texto,
        remetente: 'Eu',
        horario: new Date().toLocaleTimeString(),
        tipo: 'saida'
      };
      contextoAtual.mensagens.push(novaMensagem);
      enviarContextoParaCRM();
    }
  }
});

/**
 * Função que envia o contexto atual da conversa para o CRM
 */
function enviarContextoParaCRM() {
  window.postMessage({
    type: 'crm_conversa_atualizada',
    payload: contextoAtual
  }, '*');
  console.log('Contexto enviado para CRM:', contextoAtual);
}

/**
 * Função que coleta mensagens do DOM do WhatsApp
 * Será chamada periodicamente para monitorar novas mensagens
 */
function coletarMensagensWhatsApp() {
  try {
    // Buscar todas as mensagens na conversa ativa
    const mensagens = [];
    
    // Seletores do WhatsApp Web (podem mudar com atualizações)
    const messageContainers = document.querySelectorAll('[data-testid^="msg-"]');
    
    messageContainers.forEach(container => {
      try {
        // Verificar se é mensagem de saída (enviada pelo usuário)
        const isOutgoing = container.classList.contains('message-out') || 
                          container.querySelector('[data-testid="msg-dblcheck"]') !== null ||
                          container.closest('[data-testid="msg-container"]')?.classList.contains('message-out');
        
        // Extrair texto da mensagem
        const textElement = container.querySelector('[data-testid="msg-text"] span, .copyable-text span');
        const texto = textElement?.textContent?.trim() || '';
        
        // Extrair horário
        const timeElement = container.querySelector('[data-testid="msg-time"]');
        const horario = timeElement?.textContent?.trim() || new Date().toLocaleTimeString('pt-BR');
        
        if (texto) {
          mensagens.push({
            texto: texto,
            remetente: isOutgoing ? 'Eu' : 'Lead',
            horario: horario,
            tipo: isOutgoing ? 'saida' : 'entrada',
            fromMe: isOutgoing,
            isOutgoing: isOutgoing
          });
        }
      } catch (err) {
        console.error('Erro ao processar mensagem individual:', err);
      }
    });
    
    // Só atualiza se houver mensagens diferentes
    if (mensagens.length > 0 && JSON.stringify(mensagens) !== JSON.stringify(contextoAtual.mensagens)) {
      contextoAtual.mensagens = mensagens;
      console.log('Mensagens capturadas do WhatsApp:', mensagens);
      
      // Enviar para o PromoKit
      window.postMessage({ 
        tipo: 'CONTEXTO_CONVERSA', 
        mensagens: mensagens,
        nome: contextoAtual.contatoAtual
      }, '*');
    }
  } catch (error) {
    console.error('Erro ao coletar mensagens do WhatsApp:', error);
  }
}

// Inicia o monitoramento de mensagens (a cada 2 segundos)
setInterval(coletarMensagensWhatsApp, 2000);

console.log('PromoKit content-script carregado e bridge estabelecida.');
